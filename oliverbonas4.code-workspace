{"folders": [{"path": "."}, {"path": "store/git_modules/@tomandco"}, {"path": "store-server"}], "settings": {"angular.enable-strict-mode-prompt": false, "prettier.prettierPath": "/sites/oliverbonas4/store/node_modules/prettier", "git.ignoreLimitWarning": true, "github.copilot.chat.codeGeneration.useInstructionFiles": false, "github.copilot.chat.codeGeneration.instructions": [{"file": ".github/copilot-instructions.md"}], "github.copilot.chat.commitMessageGeneration.instructions": [{"text": "Use conventional commit message format. For example: feat: add new feature, fix: fix a bug, docs: update documentation, style: change style, refactor: refactor code, test: add tests, chore: change build process, ci: change CI process, perf: change performance, build: change build process, revert: revert a commit"}], "[html]": {"github.copilot.chat.codeGeneration.instructions": [{"text": "Use semantic HTML elements. For example: header, nav, main, section, article, aside, footer"}, {"text": "Order HTML attributes in a consistent way. For example: id, class, data-*"}]}}}
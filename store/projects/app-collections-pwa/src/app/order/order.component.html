<div class="p-r-6 p-r-4-s p-l-6 p-l-4-s flex-grow overflow-hidden overflow-y-auto">
    <box small>
        <h1 class="p-t p-r-9 p-r-4-s p-b p-l-9 p-l-4-s bg-col-w h2 _inner">
            {{'Order number: %' | translate : order?.data?.order_increment_id}}
        </h1>
        @for (shipment of order?.data?.shipments; track $index) {
        <div class="m-t-6 p-t p-r-9 p-r-4-s p-b p-l-9 p-l-4-s bg-col-w _inner">
            <h2 class="h2">{{'Shipment number: %' | translate : shipment.increment_id}}</h2>

            <p class="m-t-3 p-b-2 b-b p1">
                <span class="h3-bold">{{'Customer name' | translate}}:</span>
                <span class="h3 p-l-1">{{shipment.customer_name}}</span>
            </p>

            <p class="m-t-3 p-b-2 b-b p1">
                <span class="s1">{{'Order date' | translate}}:</span>
                {{order?.data?.order_created_at | date: 'dd MMM yyyy HH:mm:ss'}}
            </p>

            <p class="m-t-3 p-b-2 b-b p1">
                <span class="s1">{{'Shipped date' | translate}}:</span>
                {{shipment?.created_at | date: 'dd MMM yyyy HH:mm:ss'}}
            </p>

            <p class="m-t-3 h3-bold">{{'Status' | translate}}</p>
            @if (shipment.collection_status_label) {
            <button class="w-12 m-t-2 button _status"
                    [ngClass]="'_status-' + shipment.collection_status">
                <span>{{shipment.collection_status_label}}</span>
            </button>
            }
            @if ([2, 3].indexOf(shipment.collection_status) !== -1) {
            <button class="w-12 m-t-3 button _status"
                    (click)="historyBack()">
                <span>{{'Back to search results' | translate}}</span>
            </button>
            }
            @for (action of shipment.actions; track $index) {
            @if (!isExtendByAction(action)) {
            <button class="w-12 m-t-3 _status"
                    [ngClass]="action.color === 'red' ? 'button-1' : 'button'"
                    [action]="actionStatus[$index]"
                    (click)="handleAction(shipment.entity_id, action.url, $index)">
                <span>{{action.label}}</span>
            </button>
            }
            }
            @if (actionResponse) {
            <div class="m-t-2 p-t-1">
                @if (actionUpdated) {
                <p class="col-21 p1">
                    {{'% order% have been updated.' | translate : actionUpdated : actionUpdated > 1 ? 's' : ''}}
                </p>
                }
                @for (orderId of actionFailed; track $index) {
                <p class="col-2 p1">{{'Error: The status of % couldn’t be updated.' | translate : orderId}}</p>
                }
                @for (orderId of actionWrongStore; track $index) {
                <p class="col-2 p1">{{'% order is not available in your store.' | translate : orderId}}</p>
                }
            </div>
            }
        </div>
        @if (shipment.collection_extended === 1) {
        <ng-container *ngTemplateOutlet="collectionRef; context: { $implicit: shipment }" />
        } @else {
        @for (action of shipment.actions; track $index) {
        @if (isExtendByAction(action)) {
        <ng-container *ngTemplateOutlet="collectionRef context: { $implicit: shipment, action: action }" />
        }
        }
        }
        }
        <div class="m-t-6 p-t p-r-9 p-r-4-s p-b p-l-9 p-l-4-s bg-col-w _inner">
            <p class="b-b h3-bold">{{'Items' | translate}}</p>
            @for (item of order?.data?.items; track item.order_increment_id) {
            <div class="m-t-3 p-b-1 b-b"
                 [grid]="7"
                 [gridColumnGap]="'5px'">
                <p class="p3"
                   [gridColumn]="4">
                    {{item.name}}
                </p>
                <div class="center">{{item.qty}}</div>
                <div class="center"
                     [gridColumn]="2">{{item.sku}}</div>
            </div>
            }
        </div>
    </box>
</div>

<ng-template let-shipment
             let-action="action"
             #collectionRef>
    <div class="m-t-6 p-t p-r-9 p-r-4-s p-l-9 p-l-4-s bg-col-w _inner"
         [ngClass]="extendByResponse || shipment.collection_extended === 1 ? 'p-b-1' : 'p-b'">
        <p class="h3-bold">{{'Collection Extension' | translate}}</p>
        <p class="p1"
           [class.col-2]="isOverdueDate(shipment.collection_overdue_date)">
            <span class="s1">{{(extendByUpdated || shipment.collection_extended === 1 ? 'New collection date' :
                'Collection date') | translate}}:</span>
            {{shipment.collection_overdue_date | date: 'dd MMM yyy'}}
        </p>
        @if (!extendByUpdated && shipment.collection_extended !== 1) {
        <button class="w-12 m-t-3 button"
                [action]="extendByStatus"
                (click)="extendBy(shipment.entity_id, action.days)">
            <span>{{'Extend collection +% Day%' | translate : action?.days || 7 : action?.days || 7 > 1 ? 's'
                : ''}}</span>
        </button>
        }
        @if (extendByResponse || shipment.collection_extended === 1) {
        <div [ngClass]="!extendByUpdated && shipment.collection_extended !== 1 ? 'm-t-2 p-t-1': 'm-t-1 p-b-3'">
            @if (extendByUpdated || shipment.collection_extended === 1) {
            <p class="col-21 p1">
                {{'Collection date extended by % day%.' | translate : action?.days || 7 : action?.days || 7 > 1 ? 's'
                : ''}}
            </p>
            }
            @for (orderId of extendByFailed; track $index) {
            <p class="col-2 p1">{{'Error: The collection date of % couldn’t be updated.' | translate : orderId}}</p>
            }
            @for (orderId of extendByWrongStore; track $index) {
            <p class="col-2 p1">{{'% order is not available in your store.' | translate : orderId}}</p>
            }
        </div>
        }
    </div>
</ng-template>

import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { Status } from '@df/core/status';
import { CustomerModel } from '@df/session/api/customer.model';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { UiComponent } from '@df/ui/ui.component';
import { ActionComponent } from '../../../../../app-default/src/app/ui/api/action.component';
import { IfAclDirective } from '../../../../../app-default/src/app/ui/if-acl.directive';
import { AclDecorator } from '../../core/service/acl.decorator';
import { BoxComponent } from '../../ui/box/box.component';

@Component({
    selector: 'route-500',
    templateUrl: './route-500.component.html',
    standalone: true,
    imports: [ActionComponent, BoxComponent, IfAclDirective, RouterModule, TranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class Route500Component extends UiComponent implements OnInit {
    public logoutStatus = new Status();

    protected router = inject(Router);
    protected acl = inject(AclDecorator);
    protected customerModel = inject(CustomerModel);

    override ngOnInit(): void {
        super.ngOnInit();

        this.subscriptions.add(this.customerModel.obs.status.subscribe(() => this.detectChanges()));
        this.subscriptions.add(this.logoutStatus.subscribe(() => this.detectChanges()));
    }

    protected tryAgain() {
        if (window.location.href.match(/error/)) {
            this.router.navigateByUrl('/');
        } else {
            window.location.reload();
        }
    }

    protected logout(): void {
        this.acl.logout(this.logoutStatus);
    }
}

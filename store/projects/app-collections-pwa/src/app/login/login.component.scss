@use '@tomandco/atomic/scss/atom/color';

:host {
    display: flex;
    height: calc(100vh - var(--element-height-header, 0));
    flex-direction: column;

    ::ng-deep {
        input-wrap.async-validation:not(.has-validation) .mui-input.is-invalid {
            input[required] {
                border-color: color.get(4) !important;

                ~ label {
                    color: color.get(12) !important;
                }
            }

            &::before {
                display: none !important;
            }
        }
    }
}

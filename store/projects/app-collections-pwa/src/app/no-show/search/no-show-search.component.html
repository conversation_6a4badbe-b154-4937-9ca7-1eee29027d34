@if (result.data.items.length) {
<div class="p-t-3 p-r-6 p-r-4-s p-b-3 p-l-6 p-l-4-s">
    <h1 class="h2">{{'Search results for: %' | translate : terms}}</h1>
    @if (!response) {
    <button class="m-t-3 w-12 button"
            [action]="status"
            (click)="handleClick()">{{'Mark as No Show' | translate}}</button>
    <result [status]="status" />
    } @else {
    <div class="m-t-2 p-t-1 b-t">
        <p class="col-21 p1">
            {{'% order% have been updated.' | translate : updated : updated > 1 ? 's' : ''}}
        </p>
        @for (orderId of failed; track $index) {
        <p class="col-2 p1">{{'Error: The status of % couldn’t be updated.' | translate : orderId}}</p>
        }
        @for (orderId of wrongStore; track $index) {
        <p class="col-2 p1">{{'% order is not available in your store.' | translate : orderId}}</p>
        }
    </div>
    }
</div>
<div class="w-12 p-t p-r-6 p-r-4-s p-b p-l-6 p-l-4-s bg-col-33 _header">
    <div [grid]="client.isS ? 3 : 6"
         [gridColumnGap]="'9px'">
        <div>Order<br>number</div>
        <div>Shipment<br>number</div>
        <div>Customer<br>name</div>
        @if (!client.isS) {
        <div>Order<br>Status</div>
        <div [gridColumn]="2">&nbsp;</div>
        }
    </div>
</div>
<div class="w-12 flex-grow overflow-hidden overflow-y-auto">
    @for (item of result.data.items; track $index) {
    <div class="p-r-6 p-r-4-s p-l-6 p-l-4-s">
        <div class="p-t-2 p-b-3 b-b">
            <div class="flex-middle h3"
                 [grid]="client.isS ? 3 : 6"
                 [gridColumnGap]="'9px'">
                <div>
                    {{item.order_increment_id}}
                </div>
                <div>
                    {{item.increment_id}}
                </div>
                <div>
                    {{item.customer_name}}
                </div>
                @if (!client.isS) {
                <div>
                    <button class="w-12 button _status"
                            [ngClass]="'_status-' + item.collection_status">
                        <span class="h3">{{item.collection_status_label}}</span>
                    </button>
                </div>
                <div [gridColumn]="2">
                    <a class="w-12 button"
                       [routerLink]="['/order', item.order_id]">
                        <span>{{'View Order' | translate}}</span>
                    </a>
                </div>
                }
            </div>
            @if (client.isS) {
            <div class="m-t"
                 [grid]="2">
                <button class="w-12 button _status"
                        [ngClass]="'_status-' + item.collection_status">
                    <span class="h3">{{item.collection_status_label}}</span>
                </button>
                <a class="w-12 button"
                   [routerLink]="['/order', item.order_id]">
                    <span>{{'View Order' | translate}}</span>
                </a>
            </div>
            }
        </div>
    </div>
    }
</div>
<!-- pagination -->
@if (result.data.lastPage > 1) {
<pager class="w-12 flex flex-middle flex-justify-end m-t p-r-6 p-r-4-s p-l-6 p-l-4-s _pager"
       [total]="result.data.totalCount"
       [pageSize]="result.data.pageSize"
       [page]="result.data.currentPage"
       [pagerSize]="5"
       (pagerChange)="handlePagerChange($event)" />
}
<div class="p-r-6 p-r-4-s p-b-6 p-l-6 p-l-4-s"
     [ngClass]="result.data.lastPage > 1 ? 'p-t' : 'p-t-10'">
    <a class="w-12 button"
       [routerLink]="'/no-show'"><span>{{'New search' | translate}}</span></a>
</div>
} @else {
<div class="m-t p-r-6 p-r-4-s p-l-6 p-l-4-s">
    <box>
        <h3 class="center h1">{{'Sorry no results for' | translate}}<br>{{terms || 'all'}}</h3>
        <a class="w-12 m-t-8 button"
           [routerLink]="'/no-show'"><span>{{'Search again' | translate}}</span></a>
    </box>
</div>
}
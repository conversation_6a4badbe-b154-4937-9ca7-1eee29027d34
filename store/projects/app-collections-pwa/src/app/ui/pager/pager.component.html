<button class="p-a flex flex-middle cursor-pointer"
        [ngClass]="{'col-12 is-disabled': page <= 1}"
        type="button"
        [attr.aria-label]="'Previous page ' + (page - 1) + ' of ' + pagesCount"
        (click)="prev()">
    <i class="icon-chevron-left"
       aria-hidden="true"></i>
</button>
@for (p of pages; track $index) {
<button class="p-a flex flex-middle cursor-pointer"
        [class.fw-bold]="page === p"
        type="button"
        (click)="pick(p)">
    {{p}}
</button>
}
<button class="p-a flex flex-middle cursor-pointer"
        [ngClass]="{'col-12 is-disabled': page >= pagesCount}"
        type="button"
        [attr.aria-label]="'Next page ' + (page + 1) + ' of ' + pagesCount"
        (click)="next()">
    <i class="icon-chevron-right"
       aria-hidden="true"></i>
</button>
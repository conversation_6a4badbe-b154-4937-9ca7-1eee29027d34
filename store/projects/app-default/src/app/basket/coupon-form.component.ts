import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CouponFormComponentDef } from '@df/basket/coupon-form.component.def';
import { ToggleService } from '@df/ui/toggle/toggle.service';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ActionComponent } from '../ui/api/action.component';
import { ResultComponent } from '../ui/api/result.component';
import { InputWrapBodyDirective } from '../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../ui/form/input-wrap.component';
import { MuiInputDirective } from '../ui/form/mui-input.directive';
import { BasketDiscountButtonsComponent } from './basket-discount-buttons.component';

export interface ICouponFormData {
    coupon: string | string[];
    checkout?: boolean;
}

@Component({
    selector: 'coupon-form',
    templateUrl: './coupon-form.html',
    standalone: true,
    imports: [
        ActionComponent,
        CommonModule,
        FormsModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        MuiInputDirective,
        ResultComponent,
        TranslatePipe
    ]
})
export class CouponFormComponent extends CouponFormComponentDef {
    protected override successEvent = 'coupon-form-success';

    constructor(
        protected override ref: ChangeDetectorRef,
        protected toggleService: ToggleService,
        protected basketDiscountButtonsComponent: BasketDiscountButtonsComponent
    ) {
        super(ref);
    }

    protected override onSubmitSuccess(response?: any): void {
        this.toggleService.close([this.basketDiscountButtonsComponent.toggleGroup, this.basketDiscountButtonsComponent.couponToggleId]);
        super.onSubmitSuccess(response);
    }
}

@if (product) {
<!-- image -->
<div class="c-4-set m-r-1 m-b-0 p-r-4-s m-r-0-s">
    <a [routerLink]="product.data?.url">
        <img class="w-12"
             [dfImage]="product.data.image"
             [ratio]="'3/4'">
    </a>
</div>
<div class="c-8-set m-r-0 m-b-0 left">
    <div class="flex flex-justify-between"
         [ngClass]="{'_checkout-minibasket-item-title': !isSalable}">
        <div>
            <a class="fs-3 m-b-1"
               [attr.sl-minibasket-item-name]="item.id"
               [routerLink]="product.data?.url">{{product.data.name}}</a>

            <!-- configurable options -->
            @for (option of item.configurableOptions; track option.code) {
            <p class="fs-2 m-t-2 col-13">
                {{option.code | attributeLabel}}: {{option.value | optionLabel : option.code}}
            </p>
            }
            <!-- custom options -->
            <ul>
                @for (option of item.customOptions; track $index) {
                @if (option.labels.length) {
                <li>
                    <p class="fs-2 col-13">
                        {{option.title}}&nbsp;
                        @for (label of option.labels; track $index) {
                        {{label}}&nbsp;
                        }
                    </p>
                </li>
                }
                }
            </ul>
        </div>

        <!-- price -->
        <div class="p-l-1 fs-3">
            <price class="block price"
                   [attr.sl-minibasket-item-price]="item.id"
                   [ngClass]="{'col-2': item.orgPrice}"
                   [value]="item.price"
                   [precision]="2" />
            @if (item.orgPrice) {
            <price class="block col-13 m-t-1 line-through price"
                   [attr.sl-minibasket-item-orgprice]="item.id"
                   [value]="item.orgPrice"
                   [precision]="2" />
            }
        </div>
    </div>
    <!-- remove -->
    <div class="p-b-3 no-wrap">
        <div class="flex flex-middle">
            <div class="left p-t-2">
                <item-qty [model]="product.isOutOfStock || !isSalable ? 0 : data.qty"
                          [max]="maxQty"
                          [min]="product.isOutOfStock || !isSalable ? 0 : 1"
                          [isDisabled]="!isEditable || !isSalable || product.isOutOfStock"
                          [status]="status"
                          (qtyChange)="setQty($event, true)" />

                <!-- stock change notice -->
                <result [status]="status" />

                <!-- stock info -->
                @if (!item.isInStock && item.maxQty) {
                <div class="m-t-1 ta-center">{{'only % available' | translate : item.maxQty}}</div>
                }
            </div>
            @if (isEditable) {
            <div class="inline-block p-t-2 p-l-3">
                <action class="button-inline button-inline-extra-small p2"
                        [status]="removeStatus"
                        (click)="checkoutRemove()">
                    <i class="icon-trash fs-7"
                       cy-miniBasketRemoveItem
                       aria-hidden="true"></i>
                </action>
                <result class="block col-2 lh-1"
                        [status]="removeStatus" />
            </div>
            }
        </div>
        <!-- removed message-->
        @if (removed) {
        <div class="fill z-2 bg-col-w flex flex-middle flex-justify-center">
            <div class="b-radius-basket w-12 center bg-col-34 p-a-2 p-l-10 p-r-10 p1">{{'Item removed' | translate}}
            </div>
        </div>
        }
    </div>
    <!-- stock info -->
    @if (!isSalable) {
    <p class="col-2">{{'Out of stock' | translate}}</p>
    }
</div>
}
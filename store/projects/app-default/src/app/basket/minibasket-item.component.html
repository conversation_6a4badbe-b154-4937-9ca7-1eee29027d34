@if (item && product?.data) {
<div class="flex pos-relative p-b-3">
    <!-- image -->
    <div class="w-3 p-r-1">
        <a class="w-12 ratio-3-4"
           [routerLink]="product.data?.url">
            <img class="w-12"
                 [dfImage]="product.data.image"
                 [ratio]="'ratio-3-4'"
                 fill>
        </a>
    </div>
    <div class="w-9">
        <listing-label class="w-12 p-l-1"
                       [customClass]="'m-b-1'"
                       [product]="product" />
        <div class="w-12 flex">
            <div class="c-10 p-r-1 p-l-1 m-r-0 m-b-0 flex-column flex-justify-between left">
                <!-- name -->
                <div [style.opacity]="product.isOutOfStock ? '0.2' : null">
                    <a class="link p1 block"
                       [routerLink]="product.data?.url">{{product.data.name}}</a>
                    <!-- configurable options -->
                    @for (option of item.configurableOptions; track option.code) {
                    <span class="p2 col-13">
                        {{($first ? '' : ' | ') + (option.value | optionLabel : option.code)}}
                    </span>
                    }
                    <!-- custom options -->
                    <ul>
                        @for (option of item.customOptions; track $index) {
                        @if (option.labels.length) {
                        <li>
                            <p>
                                @for (label of option.labels; track $index) {
                                <span class="p2 col-13">{{($first ? '' : ' | ') + label}}</span>
                                }
                            </p>
                        </li>
                        }
                        }
                    </ul>
                </div>
                <div>
                    <!-- remove -->
                    <div class="p-b-3 no-wrap">
                        <div class="flex flex-middle">
                            <div class="left p-t-2">
                                <item-qty [model]="product.isOutOfStock || !isSalable ? 0 : data.qty"
                                          [max]="maxQty"
                                          [min]="product.isOutOfStock || !isSalable ? 0 : 1"
                                          [isDisabled]="!isEditable || !isSalable || product.isOutOfStock"
                                          [status]="status"
                                          [itemId]="item.id"
                                          (qtyChange)="setQty($event, true)" />
                                <!-- stock change notice -->
                                <result [status]="status" />
                                <!-- stock info -->
                                @if (!item.isInStock && item.maxQty) {
                                <div class="m-t-1 ta-center">{{'only % available' | translate : item.maxQty}}</div>
                                }
                            </div>
                            @if (isEditable) {
                            <div class="inline-block p-t-2 p-l-3">
                                <action class="button-inline"
                                        [status]="removeStatus"
                                        (click)="checkoutRemove()">
                                    <i class="icon-trash fs-7"
                                       cy-miniBasketRemoveItem
                                       aria-hidden="true"></i>
                                </action>
                                <result class="block col-2 lh-1"
                                        [status]="removeStatus" />
                            </div>
                            @if (item) {
                            <div class="inline-block p-l-3 p-t-2">
                                <wishlist-move-from-basket [basketItems]="item" />
                            </div>
                            }
                            }
                        </div>
                        <!-- removed message-->
                        @if (removed) {
                        <div class="fill z-2 bg-col-w flex flex-middle flex-justify-center">
                            <div class="b-radius-basket w-12 center bg-col-34 p-a-2 p-l-10 p-r-10 p1">{{'Item
                                removed' |
                                translate}}</div>
                        </div>
                        }
                    </div>
                    <!-- stock info -->
                    @if (!isSalable) {
                    <p class="p-t-8px col-2 s1">{{'Out of stock' | translate}}</p>
                    }
                </div>
            </div>
            <div class="w-2 p-b flex-column right"
                 [style.opacity]="product.isOutOfStock ? '0.2' : null">
                <price class="price p1"
                       [ngClass]="{'col-2': item.orgPrice}"
                       [value]="rowPrice" />
                @if (rowOrgPrice) {
                <price class="line-through col-13 price p1"
                       [value]="rowOrgPrice" />
                }
            </div>
        </div>
    </div>
</div>
}
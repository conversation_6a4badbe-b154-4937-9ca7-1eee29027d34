@if (!app.client.isCms) {
<ng-container *abIf="abShowCondition">
    <ng-container [ngTemplateOutlet]="carousel" />
</ng-container>
} @else {
<ng-container [ngTemplateOutlet]="carousel" />
}

<ng-template #carousel>
    <!-- @if(models.length){ -->
    <div class="flex flex-justify-between p-l-4-m p-l-4-s p-r-4-m p-r-4-s">
        <div>
            <span class="m-r-2"
                  [sizeClass]="'XL:p2, SM:h3'"
                  *iCms="'title' of component"></span>
            @if (component?.link) {
            <href [link]="component.link">
                <span class="p2 col-13"
                      [sizeClass]="'XL:c1, SM:p2'">{{'View all' | translate}}</span>
            </href>
            }
        </div>
        <div>
            <button class="m-r-2"
                    [ngClass]="'_previous' + uid"
                    aria-label="Previous slide">
                <i class="icon-chevron-left fs-3"
                   aria-hidden="true"></i>
            </button>
            <button [ngClass]="'_next' + uid"
                    aria-label="Next slide">
                <i class="icon-chevron-right fs-3"
                   aria-hidden="true"></i>
            </button>
        </div>
    </div>
    <carousel class="m-t-2 p-l-4-m p-l-4-s"
              [slidesPerView]="[2.3, 2.3, 3.2, 3.2][client.sizeId]"
              [spaceBetween]="4"
              [navigation]="{
                nextEl: '._next' + uid,
                prevEl: '._previous' + uid
            }">
        @for (item of models; track item?.data.id){
        <ng-template carouselSlide>
            <product [model]="item"
                     [disableCarousel]="true" />
        </ng-template>
        }
    </carousel>
    <!-- } -->
</ng-template>
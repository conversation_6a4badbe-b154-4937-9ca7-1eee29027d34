import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { GtmService } from '@df/module-gtm/gtm.service';
import { OffClickDirective } from '@df/module-off-click/off-click.directive';
import { ClickEventDirective } from '@df/ui/common/click-event.directive';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ToggleBlockAbstact } from '../../common/toggle-block.abstract';
import { GiftboxIntroductionComponent } from './giftbox-introduction.component';

@Component({
    selector: 'giftbox-introduction-tray',
    templateUrl: './giftbox-introduction-tray.html',
    standalone: true,
    imports: [ClickEventDirective, GiftboxIntroductionComponent, OffClickDirective, TranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class GiftboxIntroductionTrayComponent extends ToggleBlockAbstact {
    protected name: string = 'giftboxintroductiontray';

    private gtmService = inject(GtmService);

    override show(...args: any[]): void {
        this.sendGtmEvent('openCreateGiftBox');
        super.show(...args);
    }

    override hide() {
        if (!this.isVisible) return;
        this.sendGtmEvent('closeCreateGiftBox');
        super.hide();
    }

    private sendGtmEvent(event: string): void {
        this.gtmService.push({ event });
    }
}

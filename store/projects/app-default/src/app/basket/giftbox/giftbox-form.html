<form class="pos-relative w-12"
      method="POST"
      #form="ngForm">
    <!-- Products Header -->
    <div class="w-12 flex flex-middle">
        <span class="inline-block b-col-11 col-11 b-a b-radius-max pos-relative"
              style="padding:0.65em;">
            <span class="pos-absolute left-0 right-0 center top-50 p1">1</span>
        </span>
        <span class="inline-block col-11 p-l-1 s1">{{'Choose up to four items to add to this gift box' |
            translate}}</span>
        <span class="inline-block p-l-1 p2"
              [ngClass]="boxItemsLimitReached ? 'col-2' : 'col-12'">
            {{currentBoxAssignments.length || 0}}/{{settings?.boxLimit || 0}}
        </span>
    </div>
    <!-- Products Select-->
    <div class="left p-t-2"
         [grid]="4"
         [gridColumnGap]="1"
         [gridRowGap]="1">
        @for (assignment of displayedAssignments; track assignment) {
        <div [ngClass]="{'cursor-pointer' : assignment.box === boxId || !boxItemsLimitReached}"
             (click)="toggleAssignement(assignment)">
            <div class="ratio-3-4 w-12">
                <img [dfImage]="assignment.item.product.data.image"
                     fill>
                @if (assignment.box !== boxId) {
                <div class="fill pe-none z-1 bg-col-12"
                     style="opacity:0.5;"></div>
                }
                @if (assignment.box !== boxId && !boxItemsLimitReached) {
                <div class="pe-none z-2 pos-absolute top-50 left-0 right-0 center">
                    <span class="inline-block pos-relative b-radius-max p-a-2 p-a-3-s p-a-3-m bg-col-w"
                          style="opacity:0.85;">
                        <span class="pos-absolute top-50 left-0 right-0 flex flex-middle flex-justify-center">
                            <i class="icon-plus-animate"
                               aria-hidden="true"></i>
                        </span>
                    </span>
                </div>
                }
            </div>
        </div>
        }
    </div>
    <!-- dumy input for product assignments form validation -->
    <input-wrap>
        <input type="hidden"
               [(ngModel)]="currentBoxAssignments"
               name="products"
               required>
    </input-wrap>
    <!-- Name Header -->
    <div class="w-12">
        <div class="w-12 flex flex-middle p-t">
            <span class="inline-block  b-a b-radius-max pos-relative"
                  [ngClass]="currentBoxAssignments?.length ? 'b-col-11 col-11' : 'b-col-13 col-13'"
                  style="padding:0.65em;">
                <span class="pos-absolute left-0 right-0 center top-50 p1"
                      style="transform: translateY(-50%) translateX(3%)">2</span>
            </span>
            <span class="inline-block p-l-1 s1"
                  [ngClass]="currentBoxAssignments?.length ? 'col-11' : 'col-13'"
                  translate>Give this gift box a name*</span>
            <span class="inline-block p-l-1 p2"
                  [ngClass]="name?.length === 25 ? 'col-2' : 'col-12'">{{name?.length || 0}}/25</span>
        </div>
        <!-- info content block -->
        <giftbox-name-info [ngClass]="currentBoxAssignments?.length ? 'col-11' : 'b-col-13 col-13'" />
    </div>
    <!-- Name Input-->
    <div class="left p-t-2">
        <div class="w-12 pos-relative">
            <input-wrap [required]="true"
                        [disabled]="!currentBoxAssignments?.length">
                <input class="b-a p-l-2 p-r-2 b-radius-basket"
                       style="height:3rem;"
                       type="text"
                       maxlength="25"
                       required
                       name="name"
                       [(ngModel)]="name"
                       (keyup)="detectChanges()">
            </input-wrap>
        </div>
    </div>
    <!-- Message Header -->
    <div class="w-12 flex flex-middle p-t">
        <span class="inline-block b-a b-radius-max pos-relative"
              [ngClass]="currentBoxAssignments?.length && name?.length ? 'b-col-11 col-11' : 'b-col-13 col-13'"
              style="padding:0.65em;">
            <span class="pos-absolute left-0 right-0 center top-50 p1"
                  style="transform: translateY(-50%) translateX(3%);">3</span>
        </span>
        <span class="inline-block p-l-1 s1"
              [ngClass]="currentBoxAssignments?.length && name?.length ? 'col-11' : 'col-13'"
              translate>Add a personal message</span>
        <span class="inline-block p-l-1 p2"
              [ngClass]="message?.length === 120 ? 'col-2' : 'col-12'">{{message?.length || 0}}/120</span>
    </div>
    <!-- Message Input-->
    <div class="left p-t-2">
        <div class="w-12 pos-relative">
            <input-wrap [disabled]="!currentBoxAssignments?.length || !name?.length">
                <textarea class="b-a p-a-2 b-radius-basket"
                          style="resize:none;height:8rem;"
                          type="textarea"
                          maxlength="120"
                          (keyup)="detectChanges()"
                          name="message"
                          [(ngModel)]="message"></textarea>
            </input-wrap>
        </div>
    </div>
    <!-- submit button -->
    @if (giftingProduct) {
    <action class="button w-12 m-t"
            [class.disabled]="!form.valid"
            [status]="status"
            (click)="submit()">{{(currentBoxItem ? 'Save changes' : 'Add to bag') | translate}}</action>
    <result class="block col-2 fs-3 m-t-1 left"
            [status]="status" />
    }
    <!-- remove button -->
    @if (currentBoxItem) {
    <action class="button-1 w-12 m-t"
            [class.disabled]="!form.valid"
            [status]="removeStatus"
            (click)="remove()">{{'Remove gift box' | translate}}</action>
    <result class="block col-2 fs-3 m-t-1 left"
            [status]="removeStatus" />
    }
    @if (!canShowForm) {
    <div class="pos-absolute fill z-3 bg-col-w p1">
        {{'Sorry, You can only add % Gift boxes to Your basket.' | translate : maxBoxesQty}}
    </div>
    }
</form>
<!-- image -->
<div class="c-4-set m-r-1 m-b-0 p-r-4-s m-r-0-s">
    <img class="w-12"
         [dfImage]="item.product.data.image"
         [ratio]="'3/4'">
</div>
<div class="c-8-set m-r-0 m-b-0 left">
    <div class="flex flex-justify-between">
        <div>
            <div class="w-12 m-b-1">
                <i class="icon-gift"
                   aria-hidden="true"></i>
                <span class="inline-block p-l-1 fs-3">{{item.name}}</span>
            </div>
            <!-- giftbox message -->
            <span class="fs-2 m-t-2 col-13"
                  [innerHTML]="item.message | truncate:30 : '...'">
            </span>
        </div>
        <!-- price -->
        <div class="p-l-1 fs-3">
            <price class="block price"
                   [ngClass]="{'col-2': item.orgPrice}"
                   [attr.sl-minibasket-item-price]="item.id"
                   [value]="item.price"
                   [precision]="2" />
            @if (item.orgPrice) {
            <price class="block col-13 m-t-1 line-through price"
                   [attr.sl-minibasket-item-orgprice]="item.id"
                   [value]="item.orgPrice"
                   [precision]="2" />
            }
        </div>
    </div>
    <div class="pos-relative m-t-3">
        <div class="flex flex-middle">
            <div class="inline-block">
                <!-- remove -->
                <action class="button-inline button-inline-extra-small p2 w-12-m w-12-s m-l-2-l m-l-2-x"
                        [attr.aria-label]="('Remove' | translate) + ' ' + item.name"
                        [status]="removeStatus"
                        (click)="checkoutRemove()">
                    <i class="icon-trash fs-7"
                       aria-hidden="true"></i>
                </action>
                <result class="pos-absolute right-0 ta-right"
                        [status]="removeStatus" />
            </div>
        </div>
    </div>
</div>
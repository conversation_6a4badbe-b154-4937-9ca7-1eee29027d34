@if (item) {
<div class="flex pos-relative p-b-2 p-b-4-s">
    <div class="pos-relative flex w-12">
        <!-- product / X, L, M -->
        <div class="w-8 w-9-m w-10-s flex">
            <!-- image -->
            <div class="w-4 w-5-m w-5-s p-r-3-s">
                <img class="w-12"
                     [dfImage]="item.product.data.image"
                     [ratio]="'3/4'">
            </div>
            <div class="c-9 c-8-m p-l-2 p-t-1 p-t-0-s p-l-0-s left m-r-0 m-b-0 flex flex-column flex-justify-between">
                <!-- name -->
                <div class="p-b-3-s">
                    <div class="p1">{{item.name}}</div>
                    <!-- giftbox message -->
                    <span class="p2 col-13"
                          [innerHTML]="item.message | truncate:30 : '...'"></span>
                    <div class="w-12 left p-t p-t-2-s">
                        <div class="inline-block">
                            <div class="b-radius-max bg-col-w col-12 b-a p-l-2 p-r-2 p-l-3-s p-r-3-s p-a-1 p2 cursor-pointer flex flex-middle"
                                 [clickEvent]="'giftboxformtray.toggle.request'"
                                 [data]="{boxId: item.id}">{{'Edit' | translate}}</div>
                        </div>
                    </div>
                </div>
                <div>
                    <!-- remove -->
                    <div class="p-b-3 p-b-2-s no-wrap">
                        <div class="inline-block">
                            <action class="button-inline"
                                    [status]="removeStatus"
                                    (click)="remove()">
                                <span class="flex flex-middle">
                                    <i class="icon-trash fs-7 fs-8-s"
                                       aria-hidden="true"></i>
                                    <span class="inline-block m-l-1 p2"
                                          *ifSize="'!S'">{{'Remove' | translate}}</span>
                                </span>
                            </action>
                            <result class="block col-2 lh-1"
                                    [status]="removeStatus" />
                        </div>
                        <!-- removed message-->
                        @if (removed) {
                        <div class="fill z-2 bg-col-w flex flex-middle flex-justify-center">
                            <div class="b-radius-basket w-12-s center bg-col-34 p-a-2 p-l-10 p-r-10 p1">
                                {{'Gift box removed' | translate}}</div>
                        </div>
                        }
                    </div>
                </div>
            </div>
        </div>
        <div class="w-4 w-3-m w-2-s flex flex-column-s p-l-2-s">
            <!-- total product price / X, L, M -->
            <div class="w-12 flex flex-column flex-justify-between pos-relative ta-right">
                <div class="m-b m-t-1 m-t-0-s flex-s flex-column-s flex-column-reverse-s">
                    <price class="inline-block block-s price p1"
                           [ngClass]="{'col-2': item.orgPrice}"
                           [value]="item.price" />
                    @if (item.orgPrice) {
                    <price class="line-through col-13 price p1"
                           [value]="item.orgPrice" />
                    }
                </div>
            </div>
        </div>
    </div>
</div>
}
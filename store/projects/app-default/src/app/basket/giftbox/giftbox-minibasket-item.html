<div class="flex pos-relative p-b-3">
    <div class="pos-relative flex w-12">
        <!-- product / X, L, M -->
        <div class="w-10 flex">
            <!-- image -->
            <div class="w-4 p-r-1">
                <img class="w-12"
                     [dfImage]="item.product.data.image"
                     ratio="3/4">
            </div>
            <div class="c-10 left p-l-1 p-r-1 p-t-1 m-r-0 m-b-0 flex flex-column flex-justify-between">
                <!-- name -->
                <div>
                    <div class="w-12">
                        <i class="icon-gift"
                           aria-hidden="true"></i>
                        <span class="inline-block p-l-1 p1">{{item.name}}</span>
                    </div>
                    <!-- giftbox message -->
                    <span class="p2 col-13"
                          [innerHTML]="item.message | truncate:30 : '...'"></span>
                </div>
                <div>
                    <!-- remove -->
                    <div class="p-b-3 no-wrap">
                        <div class="flex flex-middle">
                            <div class="inline-block">
                                <div [clickEvent]="'giftboxformtray.toggle.request'"
                                     [data]="{boxId: item.id}"
                                     class="b-radius-max m-t-2 bg-col-w col-12 b-a p-l-2 p-r-2 p-l-3-s p-r-3-s p-a-1 p2 cursor-pointer flex flex-middle">
                                    {{'Edit' | translate}}</div>
                            </div>
                            <div class="inline-block p-t-2 p-l-3">
                                <action class="button-inline button-inline-extra-small p2"
                                        [status]="removeStatus"
                                        (click)="remove()">
                                    <i class="icon-trash fs-7"
                                       aria-hidden="true"></i>
                                </action>
                                <result class="block col-2 lh-1"
                                        [status]="removeStatus" />
                            </div>
                        </div>
                        <!-- removed message-->
                        @if (removed) {
                        <div class="fill z-2 bg-col-w flex flex-middle flex-justify-center">
                            <div class="b-radius-basket w-12 center bg-col-34 p-a-2 p-l-10 p-r-10 p1">
                                {{'Gift box removed' | translate}}</div>
                        </div>
                        }
                    </div>
                </div>
            </div>
        </div>
        <div class="w-2 flex flex-column-s">
            <!-- total product price -->
            <div class="w-12 flex flex-column flex-justify-between pos-relative ta-right">
                <div class="m-b flex flex-column">
                    <price class="inline-block block-s price p1"
                           [ngClass]="{'col-2' : item.orgPrice}"
                           [value]="item.price" />
                    @if (item.orgPrice) {
                    <price class="line-through col-13 price p1"
                           [value]="item.orgPrice" />
                    }
                </div>
            </div>
        </div>
    </div>
</div>
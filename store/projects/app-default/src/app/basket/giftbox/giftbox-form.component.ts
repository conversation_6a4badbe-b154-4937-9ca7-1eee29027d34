import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, inject, Input, On<PERSON>est<PERSON>, OnInit, ViewChild } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { Product } from '@df/catalog/product/product';
import { App } from '@df/core/app';
import { Status } from '@df/core/status';
import { DfImageDirective } from '@df/module-image/df-image.directive';
import { TimeoutService } from '@df/ng/timeout.service';
import { Basket } from '@df/session/api/basket/basket';
import { BasketItem } from '@df/session/api/basket/basket-item';
import { BasketService } from '@df/session/api/basket/basket.service';
import { Customer } from '@df/session/api/customer';
import { GridModule } from '@df/ui/atomic/atom/grid/grid.module';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import compact from 'lodash-es/compact';
import { Subscription } from 'rxjs/internal/Subscription';
import { CheckoutFormComponent } from '../../checkout/checkout-form.component';
import { ActionComponent } from '../../ui/api/action.component';
import { ResultComponent } from '../../ui/api/result.component';
import { InputWrapBodyDirective } from '../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../ui/form/input-wrap.component';
import { GiftboxAssignment } from './giftbox-assignment';
import { GiftboxBasketItem } from './giftbox-basket-item';
import { GiftboxNameInfoComponent } from './giftbox-name-info.component';
import { GiftboxService } from './giftbox.service';

declare module '@df/checkout/checkout.interfaces' {
    namespace Checkout {
        /**
         * Extend checkout backend data (configuration) interface
         * to include optionsl gifting data
         */
        interface IData {
            gifting?: IGiftingSettings;
        }
        /**
         * Extend checkout FE data interface
         * to include options gifting data
         */
        interface IQuoteDataCustom {
            gifting?: IGiftingData;
        }
    }
}

/**
 * Information provided with checkout data about
 * gifing options
 */
export interface IGiftingSettings {
    // max boxes customer may add
    limit: number;
    // max items per box
    boxLimit: number;
}

export interface IGiftingData {
    isGiftBoxes?: number;
    assignments?: IGiftingDataAssignment[];
}

export interface IGiftingDataAssignment {
    id: number;
    label: string;
    products: number[];
}

@Component({
    selector: 'giftbox-form',
    templateUrl: './giftbox-form.html',
    standalone: true,
    imports: [
        ActionComponent,
        CommonModule,
        DfImageDirective,
        FormsModule,
        GiftboxNameInfoComponent,
        GridModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        ResultComponent,
        TranslatePipe
    ]
})
export class GiftboxFormComponent implements OnInit, OnDestroy {
    @Input()
    checkoutFormComponent!: CheckoutFormComponent;
    /**
     * Bind giftbox basketItem id if You want to edit existing gift box
     */
    @Input()
    set boxId(id: number | 'new') {
        if (id) {
            this._boxId = id;
        }
    }
    get boxId(): number | 'new' {
        return this._boxId;
    }

    _boxId: number | 'new' = 'new';

    /**
     * giftbox message
     */
    message: string;

    /**
     * giftbox name
     */
    name: string;

    /**
     * save/add status
     */
    status = new Status();

    /**
     * remove status
     */
    removeStatus = new Status();

    /**
     * Gift product model
     * @type {Product}
     */
    giftingProduct: Product;

    /**
     * Customer basket
     * @type {Basket}
     */
    basket: Basket;

    /**
     * Boxes qty model
     * @type {number}
     */
    protected _qty: number;

    /**
     * Product-box assigments objects
     * @type {GiftingAssignment[]}
     */
    assignments: GiftboxAssignment[] = [];

    /**
     * Box items added to basket
     */
    boxItems: GiftboxBasketItem[];

    /**
     * currently edited box item, exists only if boxId is proviced
     */
    currentBoxItem?: GiftboxBasketItem;

    /**
     * checks if form should be visible
     */
    canShowForm;

    detectChangesDebounced: () => void;

    /**
     * returns only Assignments that are a part of currently edited/created box
     */
    get currentBoxAssignments(): GiftboxAssignment[] {
        return compact(this.assignments.map(item => (item.box === this.boxId ? item : undefined)));
    }

    /**
     * dummy setter required by hidden input in template
     */
    set currentBoxAssignments(assigments: GiftboxAssignment[]) {}

    /**
     * returns true if we cannot add any more items to currently eidted/created box
     */
    get boxItemsLimitReached(): boolean {
        return this.currentBoxAssignments.length >= this.settings.boxLimit;
    }

    private _basketSubscription?: Subscription;

    /**
     * Form
     * important: you need to put #form="ngForm" on form element in template
     */
    @ViewChild('form', { static: false })
    form!: NgForm;

    giftboxService = inject(GiftboxService);
    basketService = inject(BasketService);
    customer = inject(Customer);
    readonly app = inject(App);
    readonly timeoutService = inject(TimeoutService);

    constructor(protected ref: ChangeDetectorRef) {
        this.detectChangesDebounced = this.timeoutService.debounce(() => this.detectChanges(), 200);
    }

    ngOnDestroy(): void {
        this._basketSubscription?.unsubscribe();
    }

    /**
     * Binds data with proper checkout data object
     */
    public ngOnInit() {
        // load giftnig product if it does not exist;
        if (this.giftboxService.getGiftingProduct()) {
            this.onGiftingProductLoad();
        } else {
            this.giftboxService.loadGiftingProduct().then(() => {
                this.onGiftingProductLoad();
            });
        }
    }

    protected async onBasketChange() {
        // update FE data to match what is on BE side
        this._resetQty();
        // sync box items from basket
        this.boxItems = await this.giftboxService.getBoxItems();
        // sync assignments
        this.updateAssignments();
        // set current box item if boxId was provided
        this.currentBoxItem = this.boxItems?.find(item => item.id === this.boxId);
        // set current values for name and message
        this.name = this.currentBoxItem?.name || '';
        this.message = this.currentBoxItem?.message || '';
        this.canShowForm = !!(this.boxId !== 'new' || this.boxItems?.length < this.maxBoxesQty);

        this.detectChanges();
    }

    /**
     * Resets qty value to match backend data
     */
    protected _resetQty() {
        this._qty = this.giftboxService.boxesQty;
    }

    public onGiftingProductLoad() {
        this.basket = <Basket>this.customer['basket'];
        this.giftingProduct = this.giftboxService.getGiftingProduct();

        this._basketSubscription?.unsubscribe();

        this._basketSubscription = this.basket.subscribe(() => this.onBasketChange(), this);

        this.onBasketChange();
    }

    /**
     * Takes max allowed number of boxes
     * Values is retrieved from BE
     * @return {number}
     */
    public get maxBoxesQty(): number {
        return this.settings.limit;
    }

    /**
     * Takes max allowed number of items
     * in one box
     * @return {number}
     */
    public get maxItemsInBoxQty(): number {
        return this.settings.boxLimit;
    }

    /**
     * Shorthand to get gifting settings retrieved from BE
     * @return {IGiftingSettings}
     */
    public get settings(): IGiftingSettings {
        const gifting = this.checkoutFormComponent.backendData?.gifting;
        return (
            gifting || {
                limit: 0,
                boxLimit: 0
            }
        );
    }

    /**
     * We display only items that are either a part of current box or unassigned to any box
     */
    public get displayedAssignments() {
        return this.assignments.filter(assignment => !assignment.box || assignment.box === this.boxId);
    }

    /**
     * toggles assignment to currently created/edited box
     * we do not change assignment
     * @param assignment
     */
    toggleAssignement(assignment: GiftboxAssignment) {
        const index = this.assignments.findIndex(item => item === assignment);
        if (assignment.box === this.boxId) {
            this.assignments[index].box = undefined;
            this.detectChangesDebounced();
        } else {
            if (!this.boxItemsLimitReached) {
                this.assignments[index].box = this.boxId;
                this.detectChangesDebounced();
            }
        }

        this.detectChanges();
    }

    public updateAssignments(): void {
        // if this is fresh set up flag
        const isInit = !this.assignments || !this.assignments.length;

        const map: { [basketItemId: number]: number[] } = {};

        // in fresh assignments mode prepare map to know what item is in what box
        if (isInit) {
            this.boxItems.forEach(boxItem => {
                boxItem.contents.forEach(basketItemId => {
                    if (!map[basketItemId]) {
                        map[basketItemId] = [];
                    }
                    map[basketItemId].push(boxItem.id);
                });
            });
        }

        // iterate all items we can put in boxes and pass them to new assignments table
        const newAssignments: GiftboxAssignment[] = [];
        this.giftboxService.getGiftingAllowedBasketItems().forEach((basketItem: BasketItem) => {
            const basketItemQty = basketItem.qty;
            for (let piece = 1; piece <= basketItemQty; piece++) {
                const boxId = map[basketItem.id] ? map[basketItem.id].shift() : undefined;
                if (!boxId && isInit) {
                    newAssignments.push(new GiftboxAssignment(basketItem, piece));
                } else {
                    newAssignments.push(new GiftboxAssignment(basketItem, piece, boxId));
                }
            }
        });

        if (isInit) {
            this.assignments = newAssignments;
        }

        // check what assignments no loger exist and remove them
        const newAssignmentsIds = newAssignments.map(item => item.id);
        this.assignments = this.assignments.filter(assignment => newAssignmentsIds.indexOf(assignment.id) !== -1);

        // check what assignments we are missing and push them
        const assignmentsIds = this.assignments.map(item => item.id);
        newAssignments.forEach(assignment => {
            if (assignmentsIds.indexOf(assignment.id) === -1) {
                this.assignments.push(assignment);
            }
        });
        this.detectChanges();
    }

    submit() {
        if (this.form) {
            this.form['submitAttempt'] = true;
            this.form.ngSubmit.emit();
            if (this.form.valid) {
                // create current custom option values
                const options: any = {};
                options[this.giftboxService.getNameOption()?.id] = this.name;
                options[this.giftboxService.getMessageOption()?.id] = this.message;
                options[this.giftboxService.getContentsOption()?.id] = this.currentBoxAssignments
                    .map(assignment => assignment.item.id)
                    .join(',');
                if (this.boxId !== 'new') {
                    // update existing box
                    this.currentBoxItem.data.request.options = options;
                    this.currentBoxItem.requestChanged = true;
                    const promise = this.basketService.saveRequests(this.status, this.currentBoxItem);
                    promise.then(() => this.onSubmitSuccess());
                } else {
                    // add new box
                    this.basketService.add(this.status, { id: this.giftingProduct.id, qty: 1, options }, () => this.onSubmitSuccess());
                }
            }
        }
        this.detectChanges();
    }

    remove() {
        this.currentBoxItem.remove(this.removeStatus, () => this.onSubmitSuccess());
    }

    onSubmitSuccess(): void {
        this.form['submitAttempt'] = false;
        // hides giftbox edit tray
        this.app.events.broadcast('giftboxformtray.close.request');
    }

    detectChanges(): void {
        this.ref.detectChanges();
    }
}

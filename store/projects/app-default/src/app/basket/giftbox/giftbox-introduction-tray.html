<!-- top section-->
<div class="pos-relative z-2 p-t-4 p-b-4 p-t-5-s p-b-5-s p-r-6 p-l-6 flex flex-justify-right flex-middle"
     style="box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);">
    <!-- hide button -->
    <i class="block icon-close col-11 fs-4 cursor-pointer right"
       (click)="hide()"></i>
</div>
<!-- content -->
<div class="w-12 pos-relative  shipping-methods flex"
     (offClick)="hide()"
     [offClickOff]="!isVisible"
     style="height: 100%;padding-bottom:4rem;">
    <div class="w-12 pos-relative overflow-y-auto scrollbar-none left p-a p-l-6 p-r-6 flex flex-column flex-grow">
        <!-- please keep this empty div, its requied for iphones -->
        <div>
            <giftbox-introduction />
            <button class="button w-12 p-t-1 p-b-1 flex flex-middle flex-justify-center m-t-6"
                    type="button"
                    [clickEvent]="'giftboxformtray.toggle.request'">
                <span>{{'Create a gift box' | translate}}</span>
            </button>
        </div>
    </div>
</div>

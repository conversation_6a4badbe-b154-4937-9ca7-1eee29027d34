<div class="b-b b-col-5 p-b-3 p-t-3">
    <div class="flex pos-relative w-12 p-l-6 p-r-6">
        <div class="w-10 flex">
            <div class="w-4 p-r-1">
                <div class="w-12 ratio-3-4">
                    <a class="fill"
                       [routerLink]="item.product?.data?.url">
                        <img [dfImage]="item.product?.data.image"
                             [ratio]="'ratio-3-4'"
                             [alt]="item.product?.data?.name"
                             fill>
                    </a>
                </div>
            </div>
            <div class="c-10 left p-l-1 p-r-1 m-r-0 m-b-0 flex-column flex-justify-between">
                <a class="link p1 block"
                   [style.opacity]="item.product?.isOutOfStock ? '0.2' : null"
                   [routerLink]="item.product?.data?.url">{{ item.product?.data?.name }}</a>
                <div>
                    @for (option of configurableOptions; track option.value) {
                    <span class="p2 col-13">
                        {{ option.value | optionLabel: option.code }}
                    </span>
                    }
                </div>
                <button class="w-12 m-t-2-s button _custom-button"
                        (click)="addToBasket()"
                        [disabled]="item.product?.isOutOfStock">
                    <span>
                        @if (client.isM || client.isS) {
                        <i class="m-r-1 va-m"
                           [ngClass]="status.busy ? 'icon-loading' : 'icon-plus'"></i>
                        }
                        @else {
                        <i class="m-r-1 va-m"
                           [class.icon-loading]="status.busy"></i>
                        }
                        {{ 'Add to bag' | translate }}
                    </span>
                </button>
                @if (item.product?.isOutOfStock) {
                <p class="col-2 s1">{{ 'Out of stock' | translate }}</p>
                }
            </div>
        </div>
        <div class="w-2 flex-column flex-justify-between right">
            <div class="w-12 pos-relative flex-column"
                 [style.opacity]="item.product?.isOutOfStock ? '0.2' : null">
                <price class="inline-block block-s price p1"
                       [class.col-2]="item.product?.orgPrice"
                       [value]="item.product?.price || 0"></price>

                @if (item.product?.orgPrice) {
                <price class="line-through col-13 price p1"
                       [value]="item.product.orgPrice"></price>
                }
            </div>
            <wishlist-remove [item]="item"
                             [wrapButton]="false" />
        </div>

    </div>
</div>
<form autocomplete="off"
      method="POST">
    <div class="qty-select box-shadow-wrapper inline-flex flex-middle p2 p-l-1 p-r-1"
         [class.pe-none]="isDisabled">
        <span class="inline-block m-r-1 fs-7-s cursor-pointer qty-select-less"
              [ngClass]="{'is-disabled': model === min || isDisabled}"
              [style.line-height.px]="0"
              [attr.sl-basket-item-button-qty-less]="custom ? custom.id : null"
              (click)="less()">
            <i class="icon-minus col-13"
               cy-miniBasketDecreaseQty></i>
        </span>

        <div class="inline-block qty-select-input">
            <!-- if there is "max" attribute then if we enter a bigger number then
        the model will be undefined and changed to minimum not to "vm.max" -->
            <input class="center no-spinner ta-center col-12 qty-input"
                   name="item_qty"
                   autocomplete="off"
                   type="number"
                   step="1"
                   [attr.min]="min !== undefined ? min : null"
                   cy-miniBasketQty
                   [attr.sl-basket-item-qty-input]="itemId || null"
                   [(ngModel)]="model">
        </div>

        <span class="inline-block m-l-1 fs-7-s cursor-pointer qty-select-more"
              [ngClass]="{'is-disabled': model === max || isDisabled}"
              [style.line-height.px]="0"
              [attr.sl-basket-item-button-qty-more]="custom ? custom.id : null"
              (click)="more()">
            <i class="icon-plus col-13"
               cy-miniBasketIncreaseQty></i>
        </span>
    </div>
</form>
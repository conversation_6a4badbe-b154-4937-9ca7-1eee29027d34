<div class="bg-col-w center minibasket-height pos-relative"
     style="height:100%"
     (offClick)="hide()"
     [offClickOff]="!isVisible">
    <div class="w-12 flex-column"
         style="height:100%">
        <!-- page title -->
        <div class="pos-relative flex b-b">
            <button class="s1 center w-5 b-l b-col-4 p-l-3 p-r-3 p-t-2 p-t-3-m p-t-3-s left p-b-2 p-b-3-m p-b-3-s _custom-tab basket-title"
                    [ngClass]="{'bg-col-5': currentTab === 'basket'}"
                    (click)="switchTab('basket')">
                {{('Shopping bag' | translate) + (basket.qty ? (' (' + basket.qty + ')') : '')}}
            </button>
            <button class="s1 w-5 center b-l b-col-4 p-r-8 p-l-8 p-t-2 p-t-3-m p-t-3-s left p-b-2 p-b-3-m p-b-3-s _custom-tab basket-title"
                    [ngClass]="{'bg-col-5': currentTab === 'wishlist'}"
                    (click)="switchTab('wishlist')">
                {{('Wishlist' | translate) + (wishlist.items.length ? (' (' + wishlist.items.length + ')') :
                '')}}
            </button>
            <button class="pos-absolute z-10 right-6 top-3 top-4-m top-4-s cursor-pointer"
                    [attr.aria-label]="'Close' | translate">
                <i class="icon-close col-11 fs-4 "
                   aria-hidden="true"
                   (click)="hide()"></i>
            </button>
        </div>
        @if (!displayFastCheckout) {
        <!-- out of stock products alert -->
        @if (basket.hasOOSProducts) {
        <basket-oos-items-warning [status]="removeStatus" />
        }
        @if (showMoveToWishlistMessage && currentTab === 'basket') {
        <div class="p2 center bg-col-23 p-t-1 p-b-1">{{'Item added to wishlist'| translate}}</div>
        }
        }
        <!-- empty basket -->
        @if (!basket.qty && currentTab === 'basket') {
        <div class="m-t-6 ta-center flex-column flex-grow">
            <p class="p2"
               cy-miniBasketEmpty>{{'Your bag is empty' | translate}}</p>
            <div class="m-t">
                <button class="button"
                        type="button"
                        [attr.aria-label]="'Close' | translate"
                        cy-miniBasketContinueShopping
                        (click)="hide()">{{'Continue shopping' | translate}}</button>
            </div>
        </div>
        }
        @if (currentTab === 'basket' && displayFastCheckout) {
        <div class="w-12 flex-column flex-grow">
            <minibasket-fast-checkout-form class="flex-column flex-grow"
                                           [canGoNextStep]="!basket.hasOOSProducts" />
        </div>
        }
        @if (currentTab === 'wishlist') {
        <ng-container *ifNotLoggedIn>
            <div class="p1 center bg-col-23 p-t-1 p-b-1 m-b-1 m-t-1">
                <span class="s1 cursor-pointer"
                      (click)="openLoginModal()"
                      role="button"
                      tabindex="0"
                      (keypress)="openLoginModal()">{{'Create an account' | translate}}</span>&nbsp;<span>
                    {{'or'| translate}}
                    <span class="s1 cursor-pointer"
                          (click)="openLoginModal()"
                          role="button"
                          tabindex="0"
                          (keypress)="openLoginModal()">&nbsp;{{'sign in' | translate}}</span>&nbsp;</span>
                {{'to save your wishlist'| translate}}
            </div>
        </ng-container>
        @if (showAddToBasketMessage) {
        <div class="p2 center bg-col-21 col-w p-t-1 p-b-1">{{'Item added to basket'| translate}}</div>
        }
        <div>
            @if (!!wishlistItems.length) {
            <div class="overflow-auto _custom-wishlist-scroll"
                 [sizeClass]="'SM:scrollbar-none'">
                @for (item of wishlistItems; track $index) {
                <basket-wishlist-item class="pos-relative w-12"
                                      [class._custom_wishlist]="$last"
                                      [item]="item"
                                      [modelId]="item.productId"
                                      [listPosition]="$index"
                                      [listId]="'wishlist'"
                                      [listName]="'Wishlist'" />
                }
            </div>
            <div class="pos-absolute w-12 flex bg-col-w bottom-0 left-0 p-t-6 p-b-3 z-10">
                <button class="button-1 w-6 m-r-1 m-l-3"
                        (click)="switchTab('basket')"><span>{{'Back to basket' | translate}}</span></button>
                <button class="button m-l-1 w-6 m-r-3"
                        (click)="addAllToBasket()">
                    <span>
                        <i class="m-r-1 va-m"
                           [ngClass]="removeStatus.busy ? 'icon-loading' : 'icon-plus'"></i>
                        <span>{{ 'Add all to basket' | translate }}</span>
                    </span>
                </button>
            </div>
            } @else {
            <p class="s1 left p-a b-t b-b b-col-4">{{'You haven\'t added any products to your list yet.' |
                translate}}
            </p>
            <button class="w-11 m-t-6 button"
                    sl-button="back"
                    (click)="hide()"><span class="button__body">{{'Continue shopping' | translate}}</span>
            </button>
            }
        </div>
        }
    </div>
    @if (removeStatus.busy) {
    <basket-loading-state />
    }
</div>
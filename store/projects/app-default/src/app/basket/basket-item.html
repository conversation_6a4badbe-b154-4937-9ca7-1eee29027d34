@if (item && product) {
<div class="flex pos-relative p-b-2 p-b-4-s">
    <div class="pos-relative flex w-12">
        <!-- product / X, L, M -->
        <div class="w-8 w-9-m w-10-s flex">
            <!-- image -->
            <a class="w-4 w-5-m w-5-s p-r-3-s"
               [routerLink]="product?.data.url">
                <img class="w-12"
                     [dfImage]="product?.data.image"
                     [dfSrcset]="'80w, 150w, 220w'"
                     [ratio]="'3/4'">
            </a>
            <div class="c-9 c-8-m p-l-2 p-t-1 p-t-0-s p-l-0-s left m-r-0 m-b-0 flex flex-column flex-justify-between">
                <!-- name -->
                <div class="p-b-3-s">
                    <a class="link p1 block"
                       [style.opacity]="product.isOutOfStock ? '0.2' : null"
                       [routerLink]="product?.data.url">{{product?.data.name}}</a>
                    <!-- configurable options -->
                    @for (option of item.configurableOptions; track option.code) {
                    <span class="p2 col-13">
                        {{($first ? '' : ' | ') + (option.value | optionLabel : option.code)}}
                    </span>
                    }
                    <!-- custom options -->
                    <ul>
                        @for (option of item.customOptions; track $index) {
                        <li>
                            @if (option.labels.length) {
                            <p>
                                @for (label of option.labels; track $index) {
                                <span class="p2 col-13">{{($first ? '' : ' | ') + label}}</span>
                                }
                            </p>
                            }
                        </li>
                        }
                    </ul>
                    <div class="w-12 left p-t p-t-2-s">
                        <item-qty [model]="product.isOutOfStock || !isSalable ? 0 : data.qty"
                                  [max]="maxQty"
                                  [min]="product.isOutOfStock || !isSalable ? 0 : 1"
                                  [isDisabled]="!isEditable || !isSalable || product.isOutOfStock"
                                  [status]="status"
                                  [itemId]="item.id"
                                  (qtyChange)="setQty($event, true)" />
                        @if (isEditable) {
                        <!-- stock change notice -->
                        <result [status]="status" />
                        <!-- stock info -->
                        @if (!item.isInStock && item.maxQty) {
                        <div class="m-t-1 ta-center">{{'only % available' | translate : item.maxQty}}</div>
                        }
                        }
                    </div>
                </div>
                @if (isEditable || !isSalable) {
                <div>
                    <!-- remove -->
                    @if (isEditable) {
                    <div class="inline-flex flex-middle p-b-3 p-b-2-s no-wrap"
                         [class.p-t-2]="!isSalable">
                        <div class="inline-block">
                            <action class="button-inline"
                                    [status]="removeStatus"
                                    (click)="checkoutRemove()">
                                <span class="flex flex-middle">
                                    <i class="icon-trash fs-7 fs-8-s"
                                       [style.line-height.px]="0"
                                       aria-hidden="true"></i>
                                    @if (!app.client.isS) {
                                    <span class="m-l-1 p2">{{'Remove' | translate}}</span>
                                    }
                                </span>
                            </action>

                            <result class="block col-2 lh-1"
                                    [status]="removeStatus" />
                        </div>
                        <div class="inline-block p-l-2 p-l-4-s">
                            <wishlist-move-from-basket [displayLabel]="true"
                                                       [basketItems]="item" />
                        </div>
                        <!-- removed message-->
                        @if (removed) {
                        <div class="fill z-2 bg-col-w flex flex-middle flex-justify-center">
                            <div class="b-radius-basket w-12-s center bg-col-34 p-a-2 p-l-10 p-r-10 p1">{{'Item removed'
                                | translate}}</div>
                        </div>
                        }
                    </div>
                    }
                    <!-- stock info -->
                    @if (!isSalable) {
                    <p class="p-t-8px col-2 s1">{{'Out of stock' | translate}}</p>
                    }
                </div>
                }
            </div>
        </div>
        <div class="w-4 w-3-m w-2-s flex flex-column-s p-l-2-s">
            <!-- total product price / X, L, M -->
            <div class="w-12 flex flex-column flex-justify-between pos-relative ta-right">
                <div class="m-b m-t-1 m-t-0-s flex-s flex-column-s flex-column-reverse-s">
                    @if (rowOrgPrice) {
                    <price class="line-through col-13 price p1"
                           [value]="rowOrgPrice" />
                    }
                    <price class="inline-block block-s p-l-2 price p1"
                           [ngClass]="{'col-2': item.orgPrice}"
                           [value]="rowPrice" />
                </div>
                @if (giftboxAvailable && app.client.sizeId>0) {
                <div class="pos-relative">
                    <div class="pos-absolute bottom-2 right-0">
                        <div class="b-radius-max bg-col-w p-l-2 p-r-2 p-a-1 cursor-pointer flex flex-middle"
                             style="box-shadow:0px 1px 6px rgba(0, 0, 0, 0.15)"
                             [clickEvent]="boxItemsCount ? 'giftboxformtray.toggle.request' : 'giftboxintroductiontray.toggle.request'">
                            <div class="circle-wrapper fs-6 bg-col-23">
                                <i class="icon-gift fs-4"
                                   style="padding-bottom:0.1rem;"
                                   aria-hidden="true"></i>
                            </div>
                            <div class="p-l-1 p2">{{'Create a gift box' | translate}}</div>
                        </div>
                    </div>
                </div>
                }
            </div>
        </div>
    </div>
</div>
}
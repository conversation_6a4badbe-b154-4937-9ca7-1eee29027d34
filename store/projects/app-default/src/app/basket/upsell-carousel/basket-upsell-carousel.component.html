@if (app.client.isCms || !hasGiftBox) {
<div class="flex flex-justify-between p-l p-r">
    <div>
        <span class="h3 m-r-2"
              *iCms="'title' of component"></span>
        @if (component?.link) {
        <href [hrefClass]="'p2 col-13'"
              [link]="component.link">{{'View all' | translate}}</href>
        }
    </div>
    <div>
        <button class="m-r-2"
                [ngClass]="'_previous' + uid"
                [attr.aria-label]="'Previous slide' | translate">
            <i class="icon-chevron-left fs-3"
               aria-hidden="true"></i>
        </button>
        <button [ngClass]="'_next' + uid"
                [attr.aria-label]="'Next slide' | translate">
            <i class="icon-chevron-right fs-3"
               aria-hidden="true"></i>
        </button>
    </div>
</div>
<carousel class="m-t-2"
          [slidesPerView]="'auto'"
          [spaceBetween]="4"
          [navigation]="{
                nextEl: '._next' + uid,
                prevEl: '._previous' + uid
            }">
    @for (item of models; track item?.data.id) {
    <ng-template carouselSlide>
        <product [model]="item"
                 [disableCarousel]="true" />
    </ng-template>
    }
</carousel>
}
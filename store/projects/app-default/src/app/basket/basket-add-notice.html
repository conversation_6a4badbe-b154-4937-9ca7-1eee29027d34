@if (addedItem) {
<div class="bg-col-w b-b b-w-2 overflow-y-scroll"
     style="max-height: 100dvh">
    <button class="pos-absolute z-10 right-5 top-4 cursor-pointer"
            [attr.aria-label]="'Close' | translate"
            (click)="close()">
        <i class="icon-close col-11 fs-3"
           aria-hidden="true"></i>
    </button>
    <div class="p-a">
        <h3 class="h3 m-b">{{'Added to your bag' | translate}}</h3>
        <div class="flex">
            <div class="w-3 p-r-5">
                <img class="w-12"
                     [dfImage]="addedItem.product?.data?.['image']"
                     [ratio]="'3/4'">
            </div>
            <div class="w-9 m-l--2">
                <p class="p2">{{addedItem.product?.data?.['name']}}</p>
                @for (option of addedItem.configurableOptions; track option.code) {
                <span class="c1 col-13">
                    {{($first ? '' : ' | ') + (option.code === 'size' ? 'Size ': '') + (option.value | optionLabel :
                    option.code)}}
                </span>
                }
                <p class="c1 m-t-2">
                    <price class="inline-block block-s price p1"
                           [ngClass]="{'col-2': orgPrice}"
                           [value]="price" />
                    @if (orgPrice) {
                    <price class="line-through col-13 price p1"
                           [value]="orgPrice" />
                    }
                </p>
            </div>
        </div>
    </div>
    <!-- gift box -->
    @if (source && source.hasGiftBox) {
    <div class="w-12 p-r p-l pos-relative">
        @if (addStatus.busy) {
        <div class="fill z-2 flex flex-middle flex-justify-center bg-col-6">
            <i class="icon-loading"
               aria-hidden="true"></i>
        </div>
        }
        <product-gift-box [source]="source"
                          [(giftBoxSelection)]="giftBoxSelection"
                          (giftBoxSelectionChange)="giftBoxSelectionChangeHandle($event)" />
        <result class="pos-absolute c1"
                [status]="addStatus"
                [success]="'Gift box was added' | translate" />
    </div>
    }
    <div class="w-12 flex b-t m-t p-a">
        <button class="w-6 button-1 m-r-1"
                (click)="close()">
            <span>{{'Continue shopping' | translate}}</span>
        </button>

        <div class="w-6 m-l-1">
            <a class="button w-12"
               [routerLink]="'/checkout'">
                <span>{{'Go to Checkout' | translate}}</span>
            </a>
        </div>
    </div>

    @if(addedItem.product?.parent?.data?.upsell_carousel || addedItem.product?.data?.upsell_carousel) {
    <icms-outlet-block [name]="'Upsell Carousel Block'"
                       [variant]="'upsell-carousel' + (addedItem.product?.parent?.data?.upsell_carousel || addedItem.product?.data?.upsell_carousel)"
                       [allowOnly]="['basket-upsell-carousel']"
                       [options]="{context: {hasGiftBox: source?.hasGiftBox}}" />
    }

    @if (addedItem.product?.data?.category_ids?.includes(757)) {
    <icms-outlet-block [name]="'Straps Upsell Carousel Block'"
                       [variant]="'straps-upsell-carousel'"
                       [allowOnly]="['basket-straps-upsell-carousel']" />
    }
</div>
}

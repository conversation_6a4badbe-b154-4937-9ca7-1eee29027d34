<form class="block p-a-3"
      method="POST"
      [ngClass]="{'p-t-0': !coupon}"
      #form="ngForm">
    @if (!coupon) {
    <!-- open -->
    <div class="flex">
        <!-- coupon code -->
        <input-wrap class="w-8">
            <input class="input"
                   type="text"
                   name="coupon"
                   required
                   [muiInput]="'Enter promotional code' | translate"
                   (keydown.enter)="submit()"
                   [(ngModel)]="data.coupon">
        </input-wrap>
        <div class="w-4 p-l-3 pos-relative mui-input-padding-top">
            <!-- submit -->
            <action class="w-12 button"
                    [status]="status"
                    (click)="submit()">
                {{'Apply' | translate}}
            </action>
        </div>
    </div>
    <result class="m-t-1 fs-3 block"
            [status]="status" />
    } @else {
    <p class="m-b-2">
        {{'Voucher' | translate}} <span class="s1">{{couponLabel}}</span> {{'has been applied' | translate}}
    </p>
    <!-- remove coupon -->
    <action class="button-inline underline tt-none p2"
            [status]="removeStatus"
            (click)="remove()">{{'Click here to remove' | translate}}</action>
    <result class="block col-2 fs-3 m-t-1"
            [status]="removeStatus" />
    }
</form>
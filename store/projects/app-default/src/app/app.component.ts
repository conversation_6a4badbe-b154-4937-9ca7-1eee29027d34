import { DOCUMENT } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    ElementRef,
    inject,
    type OnInit,
    Renderer2
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NavigationEnd, NavigationError, Router, RouterOutlet } from '@angular/router';
import { App } from '@df/core/app';
import { Client } from '@df/core/service/client';

@Component({
    selector: 'app',
    styles: [
        `
            :host {
                height: inherit;
            }
        `
    ],
    template: '<router-outlet />',
    standalone: true,
    imports: [RouterOutlet],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppComponent implements OnInit {
    protected client = inject(Client);
    protected elementRef = inject(ElementRef<HTMLElement>);
    protected changeDetectorRef = inject(ChangeDetectorRef);
    protected router = inject(Router);
    protected renderer2 = inject(Renderer2);
    protected app = inject(App);
    protected document = inject(DOCUMENT);
    protected destroyRef = inject(DestroyRef);

    constructor() {
        this.app.hasSSRCode = (this.elementRef.nativeElement as HTMLElement).hasAttribute('ng-server-context');

        this.client.isCms$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(isCms => {
            if (isCms) {
                this.setAsRoot(this.elementRef.nativeElement);
            } else {
                this.setAsRoot((this.document.scrollingElement || this.document.body) as HTMLElement);
            }
        });
    }

    ngOnInit() {
        this.router.events.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(event => {
            if (event instanceof NavigationEnd || event instanceof NavigationError) {
                this.detectChanges();
            }
        });
    }

    setAsRoot(element: HTMLElement) {
        const currentRoot = this.client.getRootElement();

        if (currentRoot !== element) {
            if (currentRoot) {
                currentRoot.classList.remove('app-root');
            }
            // appRoot class is set in global.scss
            element.classList.add('app-root');

            this.client.setRootElement(element);
        }
    }

    private detectChanges() {
        this.changeDetectorRef.detectChanges();
    }
}

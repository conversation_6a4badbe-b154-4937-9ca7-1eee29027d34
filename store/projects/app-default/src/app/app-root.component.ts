import { CommonModule, DOCUMENT } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, OnInit, inject, viewChild } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { RouterModule } from '@angular/router';
import { App } from '@df/core/app';
import { Events } from '@df/core/service/event';
import { ElementHeightDirective } from '@df/dom/element-height/element-height.directive';
import { AfterContentInitDirective, AfterContentInitDirectiveService } from '@df/ui/common/after-content-init.directive';
import { IfSizeDirective } from '@df/ui/common/if-size.directive';
import { ToggleModule } from '@df/ui/toggle/toggle.module';
import { ToggleService } from '@df/ui/toggle/toggle.service';
import { UiComponent } from '@df/ui/ui.component';
import { BehaviorSubject } from 'rxjs';
import { BasketAddNoticeComponent } from './basket/basket-add-notice.component';
import { MinibasketComponent } from './basket/minibasket/minibasket.component';
import { OffCanvasComponent } from './layout/off-canvas/off-canvas.component';
import { NOffCanvas } from './layout/off-canvas/off-canvas.interface';
import { PageFooterComponent } from './layout/page/footer/page-footer.component';
import { PageHeaderComponent } from './layout/page/header/page-header.component';
import { NPageHeader } from './layout/page/header/page-header.interface';
import { SEARCH_OUTLET_TOGGLE_ID } from './search/outlet/search-outlet.component';
import { IfCheckoutProcessService } from './ui/common/if-checkout-process.service';
import { OverlayComponent } from './ui/common/overlay/overlay.component';
import { AppBusyStateComponent } from './ui/display/busy-state/app-busy-state.component';
import { RouterAppBusyStateComponent } from './ui/display/busy-state/router-app-busy-state.component';

export const PAGE_HEADER_WRAPPER_ELEMENT = 'page-header-wrapper';

@Component({
    selector: 'app-root',
    templateUrl: './app-root.component.html',
    styleUrl: './app-root.component.scss',
    standalone: true,
    imports: [
        AfterContentInitDirective,
        AppBusyStateComponent,
        BasketAddNoticeComponent,
        CommonModule,
        ElementHeightDirective,
        IfSizeDirective,
        MinibasketComponent,
        OffCanvasComponent,
        OverlayComponent,
        PageFooterComponent,
        PageHeaderComponent,
        RouterAppBusyStateComponent,
        RouterModule,
        ToggleModule
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppRootComponent extends UiComponent implements OnInit {
    /**
     * Header element id used withe element height
     */
    readonly headerElementId = PAGE_HEADER_WRAPPER_ELEMENT;
    readonly meganavToggleGroupId = NPageHeader.MEGANAV_GROUP_ID;
    readonly searchOutletToggleId = SEARCH_OUTLET_TOGGLE_ID;
    readonly offCanvasState = new BehaviorSubject<NOffCanvas.EOpenState>(NOffCanvas.EOpenState.closed);

    protected hasHeader = inject(App).hasSSRCode;
    protected isAfterContentInit = false;

    protected toggleService = inject(ToggleService);
    protected afterContentInitDirectiveService = inject(AfterContentInitDirectiveService);
    protected destroyRef = inject(DestroyRef);
    protected isCheckoutProcess$ = toObservable(inject(IfCheckoutProcessService).hasView);
    protected events = inject(Events);
    protected app = inject(App);
    protected document = inject(DOCUMENT);

    protected pageHeaderRef = viewChild<PageHeaderComponent>('pageHeaderRef');
    protected pageHeaderRef$ = toObservable(this.pageHeaderRef);

    get overlayVisible() {
        return this.toggleService.getState([this.searchOutletToggleId, 'header']);
    }

    override ngOnInit() {
        if (!this.app.hasSSRCode) {
            this.afterContentInitDirectiveService.lock(this);
        }

        super.ngOnInit();

        this.pageHeaderRef$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(value => {
            console.log('%c AppRootComponent: pageHeaderRef$', 'color: #00f', !value, this.hasHeader);
            if (!value || this.hasHeader) {
                return;
            }

            const subs = value.hasContent$.subscribe(hasContent => {
                console.log('%c AppRootComponent: pageHeaderRef$', 'color: #00f', { hasContent, hasHeader: this.hasHeader });
                if (!this.hasHeader && hasContent) {
                    this.hasHeader = hasContent;
                    this.detectChanges();

                    this.timeoutService.setTimeout(() => {
                        subs.unsubscribe();

                        this.afterContentInitDirectiveService.unlock(this);
                    }, 0);
                }
            });

            const sub2 = this.afterContentInitDirectiveService.ready.subscribe(ready => {
                if (ready) {
                    this.isAfterContentInit = true;
                    this.detectChanges();
                }

                this.timeoutService.setTimeout(() => {
                    sub2.unsubscribe();
                }, 0);
            });
        });

        this.isCheckoutProcess$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
            this.detectChanges();
        });

        if (this.app.isPlatformBrowser) {
            this.subscriptions.add(
                this.events.on('gocertify.popup').subscribe((event: string) => {
                    if (typeof event === 'string' && window && typeof window['gocertify'] !== 'undefined') {
                        window['gocertify'].popup(event);
                    }
                })
            );

            if (!window['gocertify']) {
                const el = this.document.createElement('script');
                el.setAttribute('src', '//assets.gocertify.me/assets/gocertify.js');
                el.setAttribute('data-brand', 'oliver-bonas');
                el.setAttribute('defer', 'true');
                this.document.body.appendChild(el);
            }
        }
    }
}

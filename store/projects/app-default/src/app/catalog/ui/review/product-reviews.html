@if (status.busy) {
<!-- busy -->
<div class="center p-t p-b">
    <i class="icon-loading"
       aria-hidden="true"></i>
</div>
} @else {
<!-- no reviews -->
@if (!reviews?.length) {
<p>{{'This product has no reviews yet.' | translate}}</p>
} @else {
<div class="flex flex-middle flex-justify-between">
    <div class="flex">
        <span class="s1">
            {{(averageRating / 20) | number:'1.1-1'}} {{'Overall rating' | translate}}:</span>
        <rating class="col-1 m-l-2 flex flex-middle flex-justify-end"
                [value]="averageRating / 20" />
    </div>

    <p class="p2">
        {{reviews?.length}} {{(reviews?.length === 1 ? 'review' : 'reviews') |
        translate}}
    </p>
</div>
@if (averageRatingNumber > 0) {
<div class="bg-col-4 p-a-2 m-t-3 b-radius-5">
    <p class="s1 m-b-1">{{'Customers size review' | translate}}</p>
    <div class="flex flex-justify-between pos-relative">
        @for (i of [1, 2, 3, 4, 5]; track $index) {
        <div class="bg-col-w b-radius-max _custom-button"
             [class._active-button]="i === averageRatingNumber"></div>
        }
        <div class="bg-col-w w-12 pos-absolute _custom-bar"></div>
    </div>
    <div class="flex flex-justify-between">
        <div class="p2">{{'Too small' | translate}}</div>
        <div class="p2">{{'Just right' | translate}}</div>
        <div class="p2">{{'Too big' | translate}}</div>
    </div>
</div>
}
<input-wrap class="m-t-3">
    <select class="select b-col-4"
            [(ngModel)]="selectedSort"
            [muiInput]="'Sort By:' | translate"
            (change)="sortReviews()">
        <option value="mostRecent">Most Recent</option>
        <option value="oldest">Oldest</option>
        <option value="highestRating">Highest Review</option>
        <option value="lowestRating">Lowest Review</option>
    </select>
</input-wrap>
<!-- reviews -->
<div class="center p-t p-t-6-m p-t-6-s overflow-y-auto custom-scrollbar"
     id="reviews">
    <div class="left">
        <ng-content />
        @for (review of reviews; track review.id) {
        @if (review.data) {
        <div class="m-b-3 m-b-5-m m-b-5-s p-b-3 p-b-5-m p-b-5-s b-b">
            <div class="flex flex-top flex-justify-middle m-b-3 m-b-5-m m-b-5-s">
                <rating class="w-6"
                        [value]="review.data.rating / 20" />
                <div class="w-6 right">
                    @if (review.data?.author !== 'Anonymous') {
                    <span class="c1">{{review.data?.author}}</span>&nbsp;
                    }
                    @else {
                    <span class="c1">{{'Oliver Bonas Customer' | translate}}</span>
                    }
                    <span class="c1">{{review.data.date | date: 'dd/MM/YY'}}</span>
                </div>
            </div>
            @if(review.data?.content) {
            <span class="w-12 p2 m-b-2"
                  [innerHtml]="review.data.content"></span>
            }

            @if (getRatingValueById(review.data.ratings, 6334) && getRatingValueById(review.data.ratings, 6335)) {
            <div class="bg-col-33 p-a-2 m-b b-radius-5">
                <p class="s1">{{'Did you buy your normal size?' | translate}}</p>
                <p class="m-t-1 p2">{{getRatingValueById(review.data.ratings, 6334) | translate}}</p>
                <p class="s1 m-t-1 m-b-1">{{'True to size' | translate}}</p>
                <div class="flex flex-justify-between pos-relative">
                    @for (i of [1, 2, 3, 4, 5]; track $index) {
                    <div class="bg-col-4 b-radius-max _custom-button"
                         [class._active-button]="i === getRatingValueById(review.data.ratings, 6335)"></div>
                    }
                    <div class="bg-col-4 w-12 pos-absolute _custom-bar"></div>
                </div>
                <div class="flex flex-justify-between">
                    <div class="p2">{{'Too small' | translate}}</div>
                    <div class="p2">{{'Just right' | translate}}</div>
                    <div class="p2">{{'Too big' | translate}}</div>
                </div>
                @if (getRatingValueById(review.data.ratings, 6336)) {
                <p class="s1 m-t-1 m-b-1">{{'Extra size comments' | translate}}</p>
                <p class="p2">{{getRatingValueById(review.data.ratings, 6336)}}</p>
                }
            </div>
            }
            @for (reply of review.data?.replies; track $index) {
            <div class="pos-relative bg-col-33 p-a-2 p-a-3-m p-a-3-s p-t-4-m p-t-4-s p-t-3 m-t-2">
                <span
                      class="_custom-logo c1 pos-absolute left-25 p-a-1 col-12 bg-col-w b-a b-col-33 b-radius-max">OB</span>
                <span class="p2"
                      [innerHtml]="reply.message"></span>
            </div>
            }
        </div>
        }
        }
    </div>
</div>
}
}
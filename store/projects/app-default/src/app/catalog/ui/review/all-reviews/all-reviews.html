<!-- review -->
<div class="center"
     [style.opacity]="status.busy?0.5:1">
    <div class="w-9 w-12-m w-12-s">
        @for (review of reviews; track review.id) {
        <div class="left wrap-x wrap-l wrap-m p-t-7 p-b-7 p-t-6-x p-b-6-x b-b">
            <div class="c-3-set c-12-set-s p-l p-l-0-s">
                <!-- rating -->
                @if (review.data.rating) {
                <rating class="fs-7 fs-5-m"
                        [value]="review.data.rating / 20" />
                }
                <!-- author -->
                <div class="p-t-2 p1">
                    @if (review?.data?.author) {
                    @if (review.data.author === 'Anonymous') {
                    <PERSON>
                    } @else {
                    {{review.data.author}},&nbsp;
                    }
                    }
                    @if (review?.data?.date) {
                    {{review.data.date | date}}
                    }
                </div>
            </div>

            <!-- content -->
            <div class="c-8-set c-12-set-s m-b-0">
                @if (review?.data?.content) {
                <p class="p1 p-l-6 p-l-0-s p-t-3-s"
                   [innerHtml]="review.data.content"></p>
                }
                @for (reply of review?.data?.replies; track $index) {
                <div
                     class="pos-relative bg-col-33 p-a-2 p-a-3-m p-a-3-s p-t-4-m p-t-4-s p-t-3 m-l-6-x m-l-6-l m-r-2-x m-r-2-l m-t-5-x m-t-6 m-t-7-m m-t-8-s">
                    <span
                          class="_custom-logo c1 pos-absolute left-25 p-a-1 col-12 bg-col-w b-a b-col-33 b-radius-max">OB</span>
                    <span class="p2"
                          [innerHtml]="reply.message"></span>
                </div>
                }
            </div>
        </div>
        }
    </div>
</div>

<!-- nav -->
@if (meta && meta.count > pagerSize) {
<reviews-pager [total]="meta.count"
               [pageSize]="pagerSize"
               [pagerSize]="pagerSize"
               [page]="page"
               (pagerChange)="onPagerChange($event)"
               #pager />
}

<!-- status -->
@if (status.busy) {
<p class="center m-t">{{'Loading reviews...' | translate}}</p>
}

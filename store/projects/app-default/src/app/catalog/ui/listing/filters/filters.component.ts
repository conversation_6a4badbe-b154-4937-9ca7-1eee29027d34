import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    TemplateRef,
    ViewChild,
    ViewEncapsulation,
    inject
} from '@angular/core';
import { ProductListing } from '@df/catalog-ui/listing/product-listing';
import { Client } from '@df/core/service/client';
import { Events } from '@df/core/service/event';
import { GtmService } from '@df/module-gtm/gtm.service';
import { GridModule } from '@df/ui/atomic/atom/grid/grid.module';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { ModalRef } from '@df/ui/modal/modal-ref';
import { ModalService } from '@df/ui/modal/modal.service';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { Subscription } from 'rxjs/internal/Subscription';
import { ActionComponent } from '../../../../ui/api/action.component';
import { FilterAttributeOptionComponent } from '../filter/attribute-option/filter-attribute-option.component';
import { FilterAttributeRangeComponent } from '../filter/attribute-range/filter-attribute-range.component';
import { FilterStockComponent } from '../filter/stock/filter-stock.component';

@Component({
    selector: 'filters',
    templateUrl: './filters.component.html',
    styleUrl: './filters.component.scss',
    encapsulation: ViewEncapsulation.None,
    standalone: true,
    imports: [
        ActionComponent,
        CommonModule,
        FilterAttributeOptionComponent,
        FilterAttributeRangeComponent,
        FilterStockComponent,
        GridModule,
        SizeClassDirective,
        TranslatePipe
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class FiltersComponent implements OnInit, OnDestroy {
    protected subscriptions = new Subscription();
    protected events = inject(Events);
    readonly client = inject(Client);
    private gtmService = inject(GtmService);

    @ViewChild('modalContent')
    modalContent!: TemplateRef<any>;

    /**
     * Listing setter
     */
    @Input()
    set listing(listing: ProductListing) {
        this.unsubscribeListing();

        this._listing = listing;

        this.listingSubscription = listing?.subscribe(() => this.detectChanges());
    }
    get listing(): ProductListing {
        return this._listing;
    }

    @Output()
    filtersOpen = new EventEmitter();

    @Output()
    filtersClose = new EventEmitter();

    private initiated = false;
    private _modalRef!: ModalRef<TemplateRef<any>>;

    private listingSubscription?: Subscription;

    /**
     * Listing model
     */
    private _listing?: ProductListing;

    constructor(
        protected modalService: ModalService,
        protected cd: ChangeDetectorRef
    ) {
        cd.detach();

        this.subscriptions.add(this.events.on('search.open').subscribe(() => this.closeModal()));
    }

    ngOnInit(): void {
        this.initiated = true;
        this.detectChanges();

        if (this.client.isPrerender) {
            this.openModal(true);
        }
    }

    ngOnDestroy(): void {
        this.unsubscribeListing();
        this.subscriptions.unsubscribe();
    }

    getFilters() {
        return this.listing.getFiltersWithManyCurrentMatches();
    }

    openModal(visuallyHidden = false) {
        if (this.modalContent) {
            this._modalRef = this.modalService.open(
                this.modalContent,
                {
                    minWidth: undefined,
                    maxWidth: undefined,
                    width: '480px',
                    position: {
                        top: '0',
                        right: '0',
                        bottom: '0'
                    },
                    modalClass: 'filters-modal',
                    panelClass: visuallyHidden ? 'visually-hidden' : '',
                    hasBackdrop: !visuallyHidden,
                    scrollStrategy: visuallyHidden || (this.client.isS && !this.client.isCms && !visuallyHidden) ? 'noop' : 'block',
                    onClose: () => {
                        this.filtersClose.emit();
                    }
                },
                'right'
            );

            this.sendGtmEvent('openFilters');

            this.filtersOpen.emit();
        }
    }

    closeModal() {
        this._modalRef?.close();
        this.sendGtmEvent('closeFilters');
    }

    clearFilters() {
        this.listing.resetFilters();
        this.closeModal();
    }

    private detectChanges() {
        if (this.initiated) {
            this.cd.detectChanges();
        }
    }

    private sendGtmEvent(event: string): void {
        this.gtmService.push({ event });
    }

    /**
     * Unsubscribes listing changes if subscription exists
     */
    protected unsubscribeListing() {
        if (this.listingSubscription) {
            this.listingSubscription.unsubscribe();
        }
    }
}

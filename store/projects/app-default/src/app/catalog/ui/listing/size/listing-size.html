<div class="flex flex-middle flex-justify-end _listing-filters-button b-r b-l b-col-4">
    @for (option of size.getOptions(); track option.size) {
    <div class="flex flex-middle cursor-pointer b-col-4 h-100 _custom-option"
         [class.b-r]="!$last"
         [ngClass]="size.isSelected(option) ? 'col-1' : 'col-13'"
         (click)="size.setState(option.size)">
        <i class="switch-grid-icon"
           [ngClass]="'icon-' + option.size + '-columns'">
        </i>
    </div>
    }
</div>

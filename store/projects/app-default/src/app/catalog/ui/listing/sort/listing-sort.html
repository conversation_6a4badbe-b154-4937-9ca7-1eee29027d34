<!-- sort by -->
<label class="p2 col-11 block sort-label cursor-pointer"
       (click)="openModal()">{{'Sort by:' | translate}}&nbsp;{{selectedLabel}}</label>

<ng-template #modalContent>
    <div
         class="pos-relative flex-column flex-justify-between overflow-hidden bg-col-w height-100-vh modal-right-container-inner">
        <div
             class="w-12 pos-absolute top-0 modal-right-container-inner-icon b-b b-col-4 p-r-3 p-l-3 p-r-0-m p-r-0-s p-l-4-m p-l-4-s flex flex-justify-between">
            <span class="h3">{{'Sort by' | translate}}</span>

            <!-- close -->
            <button class="p-a-4-m p-a-4-s"
                    type="button"
                    aria-label="Close filters"
                    (click)="closeModal()">
                <i class="icon-close fs-4 fs-3-s"
                   aria-hidden="true"></i>
            </button>
        </div>

        <div class="flex-column">
            @for (option of sort.getOptions(); track $index) {
            <button class="p1 col-12 left sort-option b-t b-col-4 p-l-3 p-t-5 p-b-3 cursor-pointer"
                    [ngClass]="{'b-b': $last, 'bg-col-33': selectedLabel === option.label}"
                    (click)="handleSortBy(option)">
                {{option.label}}
            </button>
            }
        </div>
    </div>
</ng-template>

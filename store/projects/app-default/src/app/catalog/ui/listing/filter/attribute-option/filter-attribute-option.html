<div class="p-l-3 p-l-4-m p-l-4-s p-a-3 p-t-5 p-b-3 b-b">
    <!-- label -->
    <div class="flex flex-justify-between flex-middle"
         [toggle]="['filters', filter.attribute.id]">
        <div class="flex-grow w-12">
            <label class="p1 col-12 block">
                {{filter.attribute?.data?.label || ('Out of stock items' | translate)}}
            </label>
        </div>
        <!-- icon -->
        <i [toggleClass]="'!icon-chevron-down icon-chevron-up'"
           [toggleClassBy]="['filters', filter.attribute.id]"
           [toggleClassActive]="filter.isDefaultOpen"
           [style.font-size.em]="0.8"></i>
    </div>
    @if (getSelectedOptionsForFilter()) {
    @for (optionId of filter.selection; track $index) {
    <button class="p-b-2 m-r-2 m-r-3-m m-r-3-s m-t-1 b-a b-col-4 bg-col-5 b-radius-max cursor-pointer option"
            (click)="filter.unselect(optionId)">
        <span class="flex flex-middle">
            <span class="col-11 p2">
                {{filter.getOptionLabel(optionId)}}&nbsp;({{filter.matchesCountsCurrent[optionId]}})
            </span>
            <i class="inline-flex m-l-2 icon-close"
               [style.font-size.em]="0.8"
               aria-hidden="true"></i>
        </span>
    </button>
    }
    }
</div>
<div [hideUp]="['filters', filter.attribute.id]">
    <!-- options list -->
    @if (!filter.attribute.data.isSwatch) {
    <ul class="p-a-3 p-a-4-s p-a-4-m bg-col-33">
        @for (option of filter.optionsWithCurrentMatches; track option.value) {
        <li class="w-6 m-b-3 m-b-5-m m-b-5-s"
            (click)="filter.toggle(option)">
            <href class="w-12 flex"
                  [hrefClass]="'w-12 flex'"
                  [link]="option | facetUrl : $any(filter)">
                <!-- checkbox -->
                <div class="checkbox-tick va-m">
                    <label [ngClass]="{'is-checked': filter.isSelected(option)}">
                        <i
                           [ngClass]="filter.isSelected(option) ? 'icon-checkbox-checked' : 'icon-checkbox-unchecked'"></i>
                    </label>
                </div>
                <div>
                    <!-- label -->
                    <span class="p2 va-m">{{option.label}}</span>
                    <span class="col-11 p2">&nbsp;({{filter.getCurrentMatchesCount(option)}})</span>
                </div>
            </href>
        </li>
        }
    </ul>
    }
    <!-- swatches list -->
    @if (filter.attribute.data.isSwatch) {
    <ul class="p-a-2 p-a-4-s p-a-4-m flex flex-wrap bg-col-33">
        <div [grid]="3"
             [gridColumnGap]="4"
             [gridRowGap]="8">
            @for (option of filter.optionsWithCurrentMatches; track option.value) {
            <li class="flex flex-middle filter-option-color"
                (click)="filter.toggle(option)">
                <href class="cursor-pointer b-radius-max b-a m-r-1 filter-option-color-item"
                      [link]="option | facetUrl : $any(filter)">
                    <div class="ratio-1-1 b-radius-max overflow-hidden filter-option-color-bg"
                         [ngClass]="{'selected': filter.isSelected(option)}">
                        <!-- background color -->
                        @if (!option.hasImage) {
                        <i class="fill"
                           [style.background-color]="option.swatch"></i>
                        }
                        <!-- background image -->
                        @if (option.hasImage) {
                        <img [dfImage]="option.swatch"
                             [dfSrcset]="'32w, 64w'"
                             [ratio]="'1/1'"
                             fill />
                        }
                    </div>
                </href>
                <div class="cursor-pointer filter-option-color-name">{{option.label}}</div>
                <span class="col-11 p2">&nbsp;({{filter.getCurrentMatchesCount(option)}})</span>
            </li>
            }
        </div>
    </ul>
    }
</div>
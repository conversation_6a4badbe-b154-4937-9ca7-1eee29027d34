<div class="p-l-3 p-l-4-m p-l-4-s p-a-3 p-t-5 p-b-3 b-b">
    <div class="flex flex-justify-between flex-middle"
         [toggle]="['filters', filter.attribute.id]">
        <!-- label -->
        <label class="flex-grow w-12 p1 col-12 block">
            {{filter.attribute?.data?.label || ('Out of stock items' | translate)}}
        </label>
        <!-- icon -->
        <i [style.font-size.em]="0.8"
           [toggleClass]="'!icon-chevron-down icon-chevron-up'"
           [toggleClassBy]="['filters', filter.attribute.id]"></i>
    </div>
    @for (option of filter.options; track option.value) {
    @if (filter.isSelected(option)) {
    <button class="bg-col-5 p-t-2 p1 p-b-2 m-r-2 m-r-3-m m-r-3-s b-a b-col-4 m-t-1 b-radius-max cursor-pointer option"
            type="button"
            (click)="filter.unselect(option.value)">
        <span class="flex flex-middle">
            <span>{{option.label}}</span>
            <i class="inline-flex icon-close m-l-2"
               aria-hidden="true"
               [style.font-size.em]="0.8"></i>
        </span>
    </button>
    }
    }
</div>
<div [hideUp]="['filters', filter.attribute.id]">
    <div class="p-a-3 p-a-4-s p-a-4-m bg-col-33">
        @for (option of filter.options; track option.value) {
        <button class="m-r-2 m-r-3-m m-r-3-s b-a b-col-4 b-radius-max cursor-pointer option"
                [class.bg-col-5]="filter.isSelected(option) || (!filter.hasSelection && $first)"
                (click)="filter.select(option, true)">
            {{option.label}}
        </button>
        }
    </div>
</div>

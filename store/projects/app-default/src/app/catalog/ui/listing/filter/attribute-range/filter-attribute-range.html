<div class="p-l-3 p-l-4-m p-l-4-s p-a-3 p-t-5 p-b-3 b-b">
    <div class="flex flex-justify-between flex-middle"
         [toggle]="['filters', filter.attribute.id]">
        <!-- label -->
        <label class="flex-grow w-12 p1 col-12">{{filter.attribute.data.label}}</label>
        <!-- icon -->
        <i aria-hidden="true"
           [style.font-size.em]="0.8"
           [toggleClass]="'!icon-chevron-down icon-chevron-up'"
           [toggleClassBy]="['filters', filter.attribute.id]"></i>
    </div>
    @if (filter.selection.min || filter.selection.max) {
    <button class="inline-block p1 p-b-2 m-r-2 m-r-3-m m-r-3-s b-a b-col-4 bg-col-5 m-t-1 b-radius-max cursor-pointer option"
            (click)="filter.reset()">
        @if (filter.selection.min && filter.selection.max) {
        <price [value]="filter.selection.min" />
        &ndash;&nbsp;
        <price [value]="filter.selection.max" />
        }
        @if (!filter.selection.min && filter.selection.max) {
        {{'Up to' | translate}}
        <price [value]="filter.selection.max" />
        }
        @if (filter.selection.min && !filter.selection.max) {
        {{'From' | translate}}
        <price [value]="filter.selection.min" />
        }
        <span class="col-11 p2">&nbsp;({{filter.listing.total}})</span>
        <i class="inline-flex icon-close m-l-2"
           [style.font-size.em]="0.8"
           aria-hidden="true"></i>
    </button>
    }
</div>
<div [hideUp]="['filters', filter.attribute.id]">
    <div class="bg-col-33 p-a-3 p-a-4-m p-a-4-s">
        <div [grid]="2"
             [gridColumnGap]="8">
            <ng-container *ngTemplateOutlet="itemCol; context: { $implicit : filterMin }" />
            <ng-container *ngTemplateOutlet="itemCol; context: { $implicit : filterMax }" />
        </div>
    </div>
</div>
<ng-template #itemCol
             let-entry>
    <div class="w-12 b-a b-col-12 pos-relative">
        <label class="c1 pos-absolute left-1 filter-price-label">{{entry.label | translate}}</label>
        <span class="pos-absolute left-1 top-3 top-4-m top-4-s p2">{{app.multisite.current.currency_symbol}}</span>
        <input class="p-t-3 p-t-4-m p-t-4-s p-b-1 p-b-2-m p-b-2-s p-l-3 p2 filter-input-range"
               type="number"
               step="1"
               min="0"
               [(ngModel)]="entry.value"
               (blur)="entry.apply()"
               (keyup.enter)="entry.apply()">
        @if (entry.canClear()) {
        <i class="icon-close-circle col-13 pos-absolute cursor-pointer filter-price-clear"
           aria-hidden="true"
           (click)="entry.clear()"></i>
        }
    </div>
</ng-template>

<!-- open modal filters -->
<button class="w-12 flex flex-middle flex-justify-center _listing-filters-button"
        (click)="openModal()">
    <span class="p2">{{'Filters' | translate}}</span>
    @if (!!listing?.filterSelection?.length) {
    <span class="flex flex-middle flex-justify-center b-radius-max bg-col-5 m-l-1 filters-component-count">
        {{listing?.filterSelection?.length}}</span>
    }
</button>

<!-- modal content -->
<ng-template #modalContent>
    <div
         class="pos-relative flex-column flex-justify-between overflow-hidden bg-col-w height-100-vh modal-right-container-inner">

        <div
             class="w-12 pos-absolute top-0 modal-right-container-inner-icon p-r-3 p-l-3 p-r-0-m p-r-0-s p-l-4-m p-l-4-s flex flex-justify-between b-b">
            <span class="h3">{{'Filters' | translate}}</span>

            <!-- close -->
            <button class="p-a-4-m p-a-4-s"
                    type="button"
                    aria-label="Close filters"
                    (click)="closeModal()">
                <i class="icon-close fs-4 fs-3-s"
                   aria-hidden="true"></i>
            </button>
        </div>

        <div class="overflow-y-auto flex-grow">
            <!-- filter listing-->
            @if (getFilters().length) {
            @for (filter of getFilters(); track filter.id) {
            @switch (filter.filterType) {
            @case ('attribute-option') {
            <filter-attribute-option [filter]="$any(filter)" />
            }
            @case ('attribute-range') {
            <filter-attribute-range [filter]="$any(filter)" />
            }
            @case ('stock') {
            <filter-stock [filter]="$any(filter)" />
            }
            }
            }
            }
        </div>

        <!-- filter buttons -->
        <div class="w-12 p-a-3 p-a-4-s p-a-4-m"
             [sizeClass]="'SM:form-size-1'"
             [grid]="2"
             [gridColumnGap]="7">

            <!-- clear -->
            <div>
                <action class="button-1 w-12"
                        (click)="listing.resetFilters()">
                    {{'Clear all filters' | translate}}
                </action>
            </div>

            <!-- close -->
            <div>
                <action class="button w-12"
                        (click)="closeModal()">
                    @if (listing.isFiltered()) {
                    {{listing.getTotal() > 1 ?
                    'See all ' + listing.getTotal() + ' items' : 'See ' + listing.getTotal() + ' item' |
                    translate}}
                    } @else {
                    {{'See all items' | translate}}
                    }
                </action>
            </div>
        </div>
    </div>
</ng-template>
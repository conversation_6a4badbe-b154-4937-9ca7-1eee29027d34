@use '@tomandco/atomic/scss/component';
@use '~@tomandco/atomic/scss/atom/color';
@use '~@tomandco/atomic/scss/core';
@use 'styles/icons';

:host {
    .button-2 {
        width: 100%;
        height: 28px;
        padding-right: 0 !important;
        padding-left: 0 !important;
        background-color: transparent;
        color: var(--atomic-color-12);
        pointer-events: auto;
        border-color: transparent !important;
        background-color: transparent !important;
    }

    result._busy-overlay {
        display: flex;
        align-items: center;
        justify-content: center;

        .hidden {
            pointer-events: none;
        }

        @include component.state-busy {
            &::before {
                position: absolute;
                top: 1px;
                bottom: 1px;
                left: 1px;
                width: 0;
                height: calc(100% - 2px);
                margin: 0 !important;
                animation: progress 12000ms forwards;
                background-color: #fff;
                content: '';
                opacity: 0;
            }

            &::after {
                position: absolute;
                top: 50%;
                right: 1px;
                left: 1px;
                font-size: core.px-to-rem(13);
                color: var(--atomic-color-12);
                content: 'Adding...';
                text-align: center;
                transform: translate(0, -50%);
            }

            &.hidden {
                visibility: visible !important;
            }
        }

        @include component.state-success {
            color: var(--atomic-color-1);

            &::after {
                @extend .icon-thick, :before;
                @include icons.icons;
                margin-left:14px;
                font-size: core.px-to-rem(14);
                color: var(--atomic-color-21);
            }

            &.hidden {
                visibility: visible !important;
            }
        }

        &.success,
        &.error {
            background-color: #fff;
            border: 1px solid color.get(5);
            &.hidden {
                visibility: visible !important;
            }
        }
    }
}

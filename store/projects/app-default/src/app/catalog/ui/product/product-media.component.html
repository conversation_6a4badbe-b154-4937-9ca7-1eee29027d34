<div class="pos-relative m-b m-b-0-s">
    <div class="fill">
        <div class="p-l-2 _bullets">
            @for (bullet of media(); track $index) {
            <button class="cursor-pointer pe-auto _bullet"
                    type="button"
                    [attr.id]="'scroll-to-section-' + $index"
                    [attr.aria-label]="'Scroll to section %' | translate : $index"
                    (click)="scrollToSection('#section-' + $index)">
                <span class="b-radius-max b-a b-col-w"></span>
            </button>
            }
        </div>
    </div>
    @for (item of media(); track $index) {
    <div class="overflow-hidden"
         [ngClass]="[item.landscape ? 'ratio-9-4' : 'ratio-4-5', 'photoswipe-galleryuid-' + parent().id]"
         [attr.id]="'section-' + $index"
         [attr.data-index]="$index"
         #sectionRef>
        @if (!item.video) {
        <img [dfImage]="item.image"
             [ratio]="item.landscape ? 'ratio-9-4' : 'ratio-4-5'"
             [sizes]="'100vw'"
             [priority]="$index === 0"
             [alt]="product()?.data?.name || ''"
             [photoswipe]="item.image"
             [photoswipeUid]="item.image + '.' + parent().id + '.' + $index"
             [photoswipeOptions]="{
                    index: parent().id + '.' + $index,
                    galleryUID: parent().id,
                    canPreload: true,
                    loadAfterOpen: false,
                    imageOptions: {
                        width: 4096,
                        height: 4096
                    }
                }"
             fill>
        }

        <!-- video -->
        @if (item.video) {
        <local-vimeo-player class="bg-col-w"
                            [video-url]="item.video"
                            [background]="true"
                            [autoplay-in-viewport]="true"
                            [loop]="true" />
        }
    </div>
    } @empty {
    <div class="overflow-hidden ratio-4-5">
        <shell />
    </div>
    }
</div>
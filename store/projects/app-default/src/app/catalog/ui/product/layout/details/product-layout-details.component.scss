:host {
    display: block;
    text-align: center;

    .is-out-of-stock {
        opacity: 0.5;
    }

    sticky-free ::ng-deep .inner {
        height: var(--element-height-product-view, 0) !important;
    }

    :host-context(.page-header-transparent) {
        sticky-free ::ng-deep .content {
            padding-top: var(--page-header-height, var(--element-height-page-header-height, 0)) !important;
        }
    }

    breadcrumbs {
        padding-top: 12px;
        padding-bottom: 8px;
    }
}

<div class="p-t-2 p-t-0-s p-t-0-m p-b-3 p-b-2-s m-b-1-s">
    <!-- guest wishlist warning -->
    @if (client.sizeId > 1) {
    <product-view-wishlist [product]="product"
                           [color]="product.data.color" />
    }

    @if (product.label || client.isS || client.isM) {
    <!-- label -->
    @if (product.label) {
    <div class="m-b-1 inline-block p2 left uppercase col-12"
         [style.color]="product.labelColor || null"
         [htmlBinding]="product.label | optionLabel : 'listing_label'"></div>
    }
    } @else {
    <product-view-reviews [product]="product"
                          [parent]="parent"
                          [parentView]="parentView"
                          [top]="true" />
    }

    <!-- product name row -->
    <div class="pos-relative flex flex-top flex-justify-between p-r-6-x p-r-8">
        <h1 class="inline-block h3 left">{{product.data.name}}</h1>

        <wishlist-toggle class="pos-absolute top--2 top--3-s right--2 right--3-s"
                         [product]="product"
                         [selection]="parentView?.configurableSelection" />
    </div>

    <!-- price -->
    <div class="left m-t-2 m-t-1-s">
        <!-- price -->
        <span class="s1">
            @if (product.orgPrice) {
            <product-price class="m-r-2 inline-block line-through col-13 p1 price td-line-through"
                           [product]="product"
                           [orgPrice]="true" />
            }

            <product-price class="inline-block price p1 fw-bold"
                           [product]="product" />
        </span>

        <!-- price note (CMS managed) -->
        <span class="p2 col-13 p-l-2"
              *iCms="'priceNote' of content"></span>
    </div>

    <!-- Klarna note -->
    @if (showKlarna) {
    <p class="p2 m-t-1"
       [innerHTML]="'Or % per month over 12 months. *Minimum spend £300' | translate : (product.price/12 | price)">
    </p>
    }

    <!-- delivery messages -->
    @if (client.isS) {
    <product-delivery-messages [backorderDeliveryTime]="product.backorderDeliveryTime"
                               [deliveryFlag]="product.deliveryFlag" />
    }

    @if ((client.isM || client.isS) && product.data?.category_ids?.includes(757)) {
    <div class="bg-col-34 m-t-3 _custom-radius"
         *abIf="abShowCondition">
        <div class="s1 p-l-3 p-t-2">{{'Customise your bag with mix & match straps' | translate}}</div>
        <div class="p2 p-l-3 p-b-2"> {{'Add your bag to basket then explore our styles' | translate}}</div>
    </div>
    }

    @if (client.isS || client.isM) {
    <product-view-reviews [product]="product"
                          [parent]="parent"
                          [parentView]="parentView"
                          [top]="true" />
    }
</div>
:host {
    ._colors {
        min-height: 24px;
    }

    :host-context(body:not(.is-touchy)) {
        wishlist-toggle,
        wishlist-message,
        product-configurable-options-wishlist,
        .colors-options,
        .colors-label,
        ._carousel,
        .wishlist-carousel-item {
            transition: opacity 400ms ease-out;
            will-change: opacity;
        }

        wishlist-message,
        product-configurable-options-wishlist,
        .colors-options,
        ._carousel,
        .wishlist-carousel-item {
            opacity: 0;
            pointer-events: none;
        }

        .colors-label {
            opacity: 1;
        }
    }

    &:hover,
    :host-context(body.is-touchy) {
        wishlist-toggle,
        wishlist-message,
        product-configurable-options-wishlist,
        .colors-options,
        .colors-label,
        ._carousel,
        .wishlist-carousel-item {
            transition: opacity 400ms ease-in;
        }

        wishlist-message,
        product-configurable-options-wishlist._visible,
        .colors-options,
        ._carousel,
        .wishlist-carousel-item {
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        .colors-label {
            opacity: 0 !important;
        }

        ::ng-deep {
            .carousel-nav,
            .carousel-dots {
                opacity: 1 !important;
                transition: opacity 400ms 400ms ease-in;
            }
        }
    }

    ._carousel {
        opacity: 0;

        ::ng-deep {
            swiper-container {
                height: 100%;

                &::part(container) {
                    &:not(.swiper-initialized) {
                        opacity: 0;
                    }
                }
            }

            .swiper-slide {
                border-right: 1px solid #fff;
                border-left: 1px solid #fff;
            }

            .carousel-dots {
                position: absolute;
                z-index: 5;
                top: 1em;
                left: 1em;
                padding: 0;
                opacity: 0;

                .is-touchy & {
                    opacity: 1;
                }

                .carousel-dots-inner {
                    --carousel-dot-height: 7px;
                    --carousel-dot-width: 14px;
                    --carousel-dot-underbar-height: 14px;
                    --carousel-dot-margin: -4px;
                    --carousel-dots-background: var(--atomic-background-color-w);
                    --carousel-dots-background-hover: var(--atomic-background-color-4);
                    --carousel-dots-background-active: var(--atomic-background-color-13);

                    height: 12px;
                    justify-content: flex-start;
                }

                .carousel-dot {
                    height: var(--carousel-dot-width) !important;
                    margin-right: var(--carousel-dot-margin);

                    @for $i from 1 through 10 {
                        &:nth-of-type(#{$i}).is-active ~ .carousel-dots-underbar {
                            left: calc((var(--carousel-dot-width) + var(--carousel-dot-margin)) * #{$i - 1}) !important;
                        }
                    }

                    &::after {
                        width: 6px !important;
                        height: 6px !important;
                        border-radius: 100%;
                    }
                }

                .carousel-dots-underbar {
                    width: var(--carousel-dot-width) !important;
                    height: var(--carousel-dot-width) !important;

                    &::after {
                        width: 50% !important;
                        border-radius: 100%;
                    }
                }
            }
        }

        .carousel-nav {
            position: absolute;
            z-index: 5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            inset: 0;
            opacity: 0;
            pointer-events: none;

            .carousel-next,
            .carousel-prev {
                pointer-events: auto;
            }

            .is-touchy & {
                opacity: 1;
            }
        }
    }

    ._bottom-section {
        padding-right: 10px;
        padding-left: 10px;

        .rate-empty,
        .rate-half,
        .rate-full {
            margin-right: 10px;

            .size-s &,
            .size-m & {
                margin-right: 4px;
                font-size: 11px !important;
            }
        }

        &-inner {
            max-width: calc(100% - 20px);
            width: calc(100% - 20px);
            .size-s & {
                max-width: calc(100% - 24px);
                width: calc(100% - 24px);
            }
        }
    }

    ._add-to-basket {
        display: inline-block;
        width: 0%;
        min-width: 76px;
        min-height: 35px;
        text-align: center;
        transition: width 0.5s ease-in-out;

        .size-s &,
        .size-m & {
            min-width: 30px;
            min-height: 28px;
        }

        &-1 {
            display: inline-block;
            width: 100%;
            height: 100%;
            animation: fade-in-block 0.75s;
        }

        &-2 {
            display: none;
        }

        &.active {
            width: 100%;

            ._add-to-basket-1 {
                display: none;
            }

            ._add-to-basket-2 {
                display: inline-block;
                animation: fade-in-block 0.75s;
            }
        }
    }

    ._product-name {
        text-overflow:ellipsis;
        white-space:nowrap;
        overflow:hidden;
    }
}

@keyframes fade-in-block {
    0% {
        opacity: 0;
    }

    50% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

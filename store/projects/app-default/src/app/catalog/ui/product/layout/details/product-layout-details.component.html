<div class="pos-center left"
     [class]="split() ? 'w-10 w-12-m w-12-s flex-l flex-x flex-justify-between p-b-9' : 'w-12'"
     id="product-details">
    <div [class]="split() ? 'c-5-x c-5-l w-12-m w-12-s p-t-2 p-t-3-m p-t-3-s' : 'w-12'">
        <div class="p-b-1">
            <product-layout-details-name class="w-12 wrap-m wrap-s p-t-0-s p-t-0-m"
                                         [class.p-t-2]="!split()"
                                         [product]="product()"
                                         [content]="content()"
                                         [productViewLayout]="productViewLayout"
                                         #productNameRef />

            <!-- delivery messages -->
            @if (client.isL || client.isX) {
            <product-layout-delivery-messages class="block wrap-m wrap-s"
                                              [product]="product()" />
            }
        </div>
        <!-- configurable options -->
        @if (productViewLayout.parent.isConfigurable) {
        <product-layout-configurable-options class="block wrap-m wrap-s no-m no-s"
                                             [productViewLayout]="productViewLayout" />
        }
        <!-- gift box -->
        @if (product().hasGiftBox && (client.isL || client.isX)) {
        <product-gift-box class="block wrap-m wrap-s m-t-2"
                          [source]="product()"
                          [giftBoxSelection]="shouldAddGiftBoxProduct()"
                          (giftBoxSelectionChange)="giftBoxSelectionChangeHandle($event)" />
        }
        @if (!!productViewLayout.sizeGuideVariant && (client.isS || client.isM)) {
        <div class="right p-r p-b-3">
            <button class="b-b b-col-1 p2"
                    type="button"
                    (click)="productViewLayout.openSidebar('size-guide')">
                {{'Size guide' | translate}}
            </button>
        </div>
        }
        @if (product().isOutOfStock && !productViewLayout.parent.isConfigurable) {
        <div class="wrap-m wrap-s">
            <button class="w-12 button is-out-of-stock"
                    (click)="productViewLayout.openStockNotification()">
                <span class="flex flex-middle flex-justify-center">
                    <span class="flex flex-middle right m-l-2 m-l-4-s"
                          [sizeClass]="'M: no-wrap'">{{'Out of stock - notify me' | translate}}</span>
                    <i class="icon-email fs-9 m-t-1"></i>
                </span>
            </button>
        </div>
        } @else {
        <product-layout-add-to-basket class="block wrap-m wrap-s"
                                      [class.m-t-2]="product().hasGiftBox"
                                      [productViewLayout]="productViewLayout"
                                      [showPalette]="true" />
        }
    </div>
    <div [class]="split() ? 'c-5-x c-5-l w-12-m w-12-s p-t-9 p-t-0-m p-t-0-s' : 'w-12'">
        @if (product().data?.category_ids?.includes(757)) {
        <icms-outlet-block [name]="'Straps Upsell Carousel Block'"
                           [variant]="'straps-upsell-carousel'"
                           [allowOnly]="['basket-straps-upsell-carousel']" />
        }
        @if (showCountdownBanner() && !client.isS) {
        <countdown-banner />
        }
        <p class="p-t-3 p-b-6 center p4"
           *iCms="'delivery_info' of content; content: 'html'; skipEmpty: true">
        </p>
        @if (product().productDepartmentVariant || client.isCms) {
        <product-layout-usp-block [variant]="product().productDepartmentVariant" />
        }
        <!-- general information  -->
        <product-layout-summary class="w-12 m-t-3"
                                [product]="product()"
                                [productViewLayout]="productViewLayout"
                                [content]="content()" />
        <breadcrumbs class="block wrap-m wrap-s b-t b-t-0-m b-t-0-s b-col-5" />
        <product-layout-share [product]="product()" />
    </div>
</div>
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, inject, input, type OnInit, output, signal } from '@angular/core';
import { untracked } from '@angular/core/primitives/signals';
import { type IProductDataV3 } from '@df/catalog/product/product';
import { DfImageDirective } from '@df/module-image/df-image.directive';
import { CarouselSlideDirective } from '@df/ui-carousel/carousel-slide.directive';
import { CarouselComponent } from '@df/ui-carousel/carousel.component';
import { Carousel } from '@df/ui-carousel/carousel.interface';
import { GridColumnDirective } from '@df/ui/atomic/atom/grid/grid-column.directive';
import { GridDirective } from '@df/ui/atomic/atom/grid/grid.directive';
import { injectElementRef } from '@df/ui/internal/element-ref';
import { injectExitAnimationManager, provideExitAnimationManager } from '@df/ui/internal/exit-animation/exit-animation-manager';
import { PhotoswipeDirective } from '@df/ui/photoswipe/photoswipe.directive';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { UiComponent } from '@df/ui/ui.component';
import { Nexus } from '@tomandco/nexus-api-types';
import { Swiper } from 'swiper/types';
import { LocalVimeoPlayerComponent } from '../../../../../ui/display/media/vimeo-player.component';
import { ProductDecorator } from '../../../../model/product/product.decorator';
import { ProductLayoutComponent } from '../product-layout.component';
import { NProductLayout } from '../product-layout.interface';
import { ProductLayoutMediaLightboxComponent } from './lightbox/product-layout-media-lightbox.component';

@Component({
    selector: 'product-layout-media',
    templateUrl: './product-layout-media.component.html',
    styleUrl: './product-layout-media.component.scss',
    imports: [
        CarouselComponent,
        CarouselSlideDirective,
        DfImageDirective,
        GridDirective,
        GridColumnDirective,
        LocalVimeoPlayerComponent,
        NgTemplateOutlet,
        PhotoswipeDirective,
        ProductLayoutMediaLightboxComponent,
        TranslatePipe
    ],
    providers: [provideExitAnimationManager()],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductLayoutMediaComponent extends UiComponent implements OnInit {
    readonly product = input.required<ProductDecorator<IProductDataV3>>();
    readonly media = input.required<Nexus.Lib.Catalog.Type.IProductMediaItem[]>();
    readonly content = input.required<NProductLayout.IContent>();

    readonly mediaLayoutChange = output<NProductLayout.EMediaLayout>();

    readonly parent = computed(() => this.product().parent);
    readonly gridMedia = computed(() => {
        const media = [...this.media()];
        const mediaLayoutGrid = this.productViewLayout.mediaLayoutGrid();

        if (!mediaLayoutGrid) {
            return media;
        }

        const mediaLength = media.length;

        if (mediaLength <= 4) {
            return media.slice(0, 2);
        } else if (mediaLength <= 7) {
            return media.slice(0, 5);
        }

        return media.slice(0, 8);
    });
    readonly mediaLayout = NProductLayout.EMediaLayout;
    readonly swiper = signal<Swiper | null>(null);

    readonly nativeElement = injectElementRef().nativeElement;
    readonly productViewLayout = inject(ProductLayoutComponent);

    protected override canDetach = false;

    private readonly exitAnimationManager = injectExitAnimationManager();

    constructor() {
        super();

        effect(() => {
            const _media = this.media();

            untracked(() => {
                this.swiper()?.slideToLoop(0, 0);
            });
        });
    }

    getGridColSpan(index: number): number {
        const mediaLength = this.gridMedia().length;

        switch (mediaLength) {
            case 2:
                return 2;
            case 5:
                if (index === 0) {
                    return 2;
                }

                return 1;
            default:
                return 1;
        }
    }

    getGridRowSpan(index: number): number {
        const mediaLength = this.gridMedia().length;

        switch (mediaLength) {
            case 2:
                return 2;
            case 5:
                if (index === 0) {
                    return 2;
                }

                return 1;
            default:
                return 1;
        }
    }

    getImageSizes(index: number): string[] | undefined {
        if (this.client.isS || this.client.isM) {
            return undefined;
        }

        const mediaLength = this.gridMedia().length;

        switch (mediaLength) {
            case 2:
                return ['100vw', '100vw', '50vw', '50vw'];
            case 5:
                if (index === 0) {
                    return ['100vw', '100vw', '50vw', '50vw'];
                }

                return ['100vw', '100vw', '25vw', '25vw'];
            default:
                return ['100vw', '100vw', '50vw', '50vw'];
        }
    }

    handleAfterInit(eventsParams: Carousel.EventsParams['afterInit']): void {
        const [swiper] = eventsParams;

        this.swiper.set(swiper);

        this.swiper()?.slideToLoop(0, 0);
    }

    async handleMediaLayoutChange(): Promise<void> {
        await this.exitAnimationManager.exit();

        if (this.productViewLayout.mediaLayoutGrid()) {
            this.mediaLayoutChange.emit(this.mediaLayout.scroll);
        } else {
            this.mediaLayoutChange.emit(this.mediaLayout.grid);
        }
    }

    protected override onBreakpoint() {
        this.swiper()?.update();

        super.onBreakpoint();
    }
}

<div class="flex flex-top flex-justify-between flex-column-s p-a p-t-0-s p-b-8-s overflow-x-auto custom-scrollbar">
    @for (column of content?.column; track $index) {
    <div class="center left-s p-l p-l-0-s p-r p-r-0-s w-12-s flex-s flex-middle-s b-t-s"
         [class.b-b-s]="$last">
        <!--image-->
        <href [link]="column.url">
            <div class="inline-block"
                 [ngStyle]="client.isS ? {'width': '4.5em'} : {'width': '7em'}">
                <img class="w-12"
                     [dfImage]="column.image"
                     [ratio]="client.isS ? '1/1' : '10/11'">
            </div>
        </href>

        <div class="p-l-2-s flex-s flex-middle-s flex-justify-between-s flex-grow-s">
            <div>
                <!--title-->
                <p class="p2 p-t-1"
                   *iCms="'title' of column"></p>

                <!--text-->
                <p class="p2 col-12 m-t-1-s"
                   *iCms="'text' of column"></p>
            </div>

            <!-- icon / S -->
            @if (column.url) {
            @if (client.isS) {
            <href [link]="column.url">
                <i class="icon-right col-12 fs-6"
                   aria-hidden="true"></i>
            </href>
            } @else {
            <href [link]="column.url">
                <div class="m-t-3 p2">
                    <span class="button-inline-extra-small underline">{{'Learn more' | translate}}</span>
                </div>
            </href>
            }
            }
        </div>
    </div>
    }
</div>
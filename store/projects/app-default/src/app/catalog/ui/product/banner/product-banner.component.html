<div class="ratio-product-image"
     [style.aspectRatio]="model.isLandscapeListingView | aspectRatio : model.colSpan : model.rowSpan ">
    <product-banner-block class="block fill z-2"
                          [ngClass]="hasContentImage === -1 ? '_shell' : hasContentImage > 0 ? 'bg-col-w' : ''"
                          [variant]="model.bannerVariant"
                          [image]="model.data.image"
                          [priority]="priority()"
                          [ratio]="model.isLandscapeListingView | aspectRatio : model.colSpan : model.rowSpan"
                          (contentChange)="handleBannerContentChange($event)" />
    <!-- single block-->
    @if (model.isSingleBlockBanner) {
    <href [link]="model.data.banner_link"
          [hrefClass]="'fill'">
        <div class="hidden w-12 fill overflow-hidden"
             [class.hidden]="hasContentImage > 0">
            <img [dfImage]="model.data.image"
                 [alt]="model.productName"
                 [ratio]="model.isLandscapeListingView | aspectRatio : model.colSpan : model.rowSpan"
                 [priority]="priority()"
                 [decoding]="priority() ? 'auto' : 'async'"
                 fill>
            @if (model.data.banner_title || model.data.banner_button_text) {
            <div class="fill p-a">
                <div class="vc-outer">
                    <div [ngClass]="'vc-inner-' + model.bannerVerticalAlign">
                        @if (model.data.banner_title) {
                        <h4 class="h2 m-b-2 fw-thin ta-center col-1">{{model.data.banner_title}}</h4>
                        }
                        @if (model.data.banner_button_text) {
                        <span [ngClass]="model.bannerCtaType === 'button' ? 'button-1' : 'link'">
                            <span>{{model.data.banner_button_text}}</span>
                        </span>
                        }
                    </div>
                </div>
            </div>
            }
        </div>
    </href>
    } @else if (model.isQuoteBanner) {
    <!-- quote -->
    <href [link]="model.data.banner_link"
          [hrefClass]="'fill'">
        <div class="hidden w-12 fill"
             [class.hidden]="hasContentImage === -1">
            <div class="fill flex flex-middle flex-justify-center">
                <div class="w-7 w-9-s">
                    <i class="fs-16 fs-10-s col-23 icon-quote"
                       aria-hidden="true"></i>
                    <div class="p-t-3 p-b"
                         [sizeClass]="'S:h2,!S:h1'"
                         [innerHTML]="model.data.quote"></div>
                    <div class="p1">{{model.data.quote_author}}</div>
                </div>
            </div>
        </div>
    </href>
    }
</div>
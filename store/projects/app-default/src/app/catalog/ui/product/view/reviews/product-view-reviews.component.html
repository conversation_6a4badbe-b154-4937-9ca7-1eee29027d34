@if (canShow) {
<div class="flex flex-justify-between flex-middle cursor-pointer product-details__item"
     [ngClass]="top ? 'p-t-2-m p-t-1-s p-b-1 p-b-2-m p-b-1-s' : 'p-t-2 p-t-4-s p-b-4-s b-b-s'"
     (click)="parentView.openSidebar('reviews')">
    @if (top) {
    <div class="flex flex-middle">
        <rating class="col-12 flex flex-middle flex-justify-end"
                [value]="parent.data.reviews?.rating / 20" />
        <p class="m-l-2 p1 col-12">
            {{parent.data.reviews?.count}} {{(parent.data.reviews?.count === 1 ? 'review' : 'reviews') |
            translate}}
        </p>
    </div>
    } @else {
    <div class="flex flex-middle">
        <p class="m-r-1 m-l-4-s"
           [sizeClass]="'!S:s2,S:s1'">{{'Reviews' | translate}}</p>
        <p class="m-r-3 p4 col-13">({{parent.data.reviews?.count}})</p>
        <rating class="col-12 fs-3 fs-4-s flex flex-middle flex-justify-end"
                [value]="parent.data.reviews?.rating / 20" />
    </div>
    @if (client.isS) {
    <i class="m-r-3 col-12 icon-right"
       aria-hidden="true"></i>
    } @else {
    <button class="m-r-3 button-inline has-icon is-visible"
            type="button"></button>
    }
    }
</div>
@if (client.isPrerender) {
<product-reviews class="ng-hide"
                 [product]="parent.familyIds" />
}
}

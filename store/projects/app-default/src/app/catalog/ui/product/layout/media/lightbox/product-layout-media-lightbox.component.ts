import { ChangeDetectionStrategy, Component, inject, input, OnInit, Renderer2 } from '@angular/core';
import { IProductDataV3 } from '@df/catalog/product/product';
import { DfImageDirective } from '@df/module-image/df-image.directive';
import { PhotoSwipeModule } from '@df/ui/photoswipe/photoswipe.module';
import { PhotoswipeItem, PhotoswipeOptions } from '@df/ui/photoswipe/photoswipe.types';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { UiComponent } from '@df/ui/ui.component';
import { ProductDecorator } from '../../../../../model/product/product.decorator';

@Component({
    selector: 'product-layout-media-lightbox',
    templateUrl: './product-layout-media-lightbox.component.html',
    styleUrl: './product-layout-media-lightbox.component.scss',
    imports: [DfImageDirective, PhotoSwipeModule, TranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductLayoutMediaLightboxComponent extends UiComponent implements OnInit {
    readonly product = input.required<ProductDecorator<IProductDataV3>>();
    readonly nativeElement = input.required<HTMLElement>();

    photoswipeOptions!: Partial<PhotoswipeOptions>;

    private readonly renderer = inject(Renderer2);

    override ngOnInit(): void {
        super.ngOnInit();

        this.photoswipeOptions = {
            showAnimationDuration: 250,
            hideAnimationDuration: 250,
            pinchToClose: false,
            closeOnScroll: false,
            closeOnVerticalDrag: false,
            closeOnHistoryBack: true,
            tapToToggleControls: false,
            counterEl: false,
            barsSize: { top: 0, bottom: 'auto' },
            initialZoom: 1,
            followMouse: true,
            canPreload: false,
            loadAfterOpen: false,
            imageOptions: {
                width: 4096, // max supported size by edge io
                height: 4096
            },
            getDoubleTapZoom: (isMouseClick: boolean, item: PhotoswipeItem) => {
                let zoomLevel = item.zoomLevel;
                const initialZoomLevel = item.initialZoomLevel ?? 1;

                if (!zoomLevel) {
                    zoomLevel = initialZoomLevel;
                }

                let res: number;

                if (zoomLevel && zoomLevel < 0.5 && (this.client.isM || this.client.isS)) {
                    res = 0.5;

                    this.renderer.addClass(this.nativeElement(), 'photoswipe--zoom-50');
                    this.renderer.removeClass(this.nativeElement(), 'photoswipe--zoom-100');
                } else if (zoomLevel && zoomLevel < 1) {
                    res = 1;

                    this.renderer.addClass(this.nativeElement(), 'photoswipe--zoom-100');
                    this.renderer.removeClass(this.nativeElement(), 'photoswipe--zoom-50');
                } else {
                    res = initialZoomLevel;

                    this.renderer.removeClass(this.nativeElement(), 'photoswipe--zoom-50');
                    this.renderer.removeClass(this.nativeElement(), 'photoswipe--zoom-100');
                }

                item.zoomLevel = res;

                return res;
            }
        };
    }

    onPhotoswipeStateChange(state: boolean) {
        if (state) {
            this.renderer.addClass(this.nativeElement(), 'photoswipe--active');
            this.renderer.removeClass(this.nativeElement(), 'photoswipe--inactive');
        } else {
            this.renderer.addClass(this.nativeElement(), 'photoswipe--inactive');
            this.renderer.removeClass(this.nativeElement(), 'photoswipe--active');
        }

        this.timeoutService.setTimeout(() => {
            this.renderer.removeClass(this.nativeElement(), 'photoswipe--inactive');
        }, 500);
    }
}

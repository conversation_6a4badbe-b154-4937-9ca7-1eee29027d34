<div class="pos-relative p-l product-details-carousel"
     [ngClass]="'photoswipe-galleryuid-' + product()?.parent?.id || 'product_details_carousel'">
    <div class="pos-relative _title">
        <ng-content />
        <div class="pos-absolute top-0 right-0 p-r">
            <button class="m-r-2"
                    [ngClass]="'product-prev-' + product()?.parent?.id"
                    type="button"
                    [attr.aria-label]="'Previous slide' | translate">
                <i class="icon-chevron-left fs-3"
                   aria-hidden="true"></i>
            </button>
            <button [ngClass]="'product-next-' + product()?.parent?.id"
                    type="button"
                    [attr.aria-label]="'Next slide' | translate">
                <i class="icon-chevron-right fs-3"
                   aria-hidden="true"></i>
            </button>
        </div>
    </div>
    <carousel class="slides-per-view-auto"
              [class.swiper-overflow-visible]="client.isS || client.isM"
              [style.--carousel-slides-per-view-round]="slidesPerViewRound"
              [style.--carousel-slides-per-view]="slidesPerView"
              [style.--carousel-space-between.px]="spaceBetween"
              [slidesPerView]="slidesPerView"
              [slidesPerGroup]="1"
              [spaceBetween]="spaceBetween"
              [watchSlidesProgress]="true"
              [freeMode]="true"
              [loop]="true"
              [navigation]="{
                nextEl: '.product-next-' + product()?.parent?.id,
                prevEl: '.product-prev-' + product()?.parent?.id
            }"
              [pagination]="{
                el: '.product-details-carousel-pagination'
            }"
              [speed]="600"
              #carouselRef>
        @for (item of media(); track $index) {
        <ng-template carouselSlide>
            <div class="m-b-3 ratio-3-4">
                <custom-cursor class="fill pos-absolute-x pos-absolute-l pos-absolute-m pos-absolute-s"
                               [disabled]="client.isTouchy || !!item.video">
                    <ng-template #customCursorContent>
                        <shell />
                        @if (!item.video) {
                        <img [dfImage]="item.image"
                             [loading]="'lazy'"
                             [ratio]="'3/4'"
                             [alt]="product()?.data?.name || ''"
                             [photoswipe]="item.image"
                             [photoswipeUid]="item.image + '.pdc.' + $index"
                             [photoswipeOptions]="{
                                 index: 'pdc.' + $index,
                                 galleryUID: product()?.parent?.id || 'product_details_carousel',
                                 canPreload: true,
                                 loadAfterOpen: false,
                                 imageOptions: {
                                    width: 4096,
                                    height: 4096
                                 }
                              }"
                             fill>
                        } @else {
                        <!-- vimeo -->
                        <div class="rf"
                             [attr.src]="item.image"
                             #imageRef>
                            <local-vimeo-player [video-url]="item.video"
                                                [autoplay-in-viewport]="true"
                                                [background]="true" />
                        </div>
                        }
                    </ng-template>
                    <ng-template #customCursorIcon>
                        <svg xmlns="http://www.w3.org/2000/svg"
                             width="64"
                             height="64"
                             viewBox="0 0 16.933 16.933">
                            <path d="M63.95 32A31.95 31.95 0 0132 63.95 31.95 31.95 0 01.05 32 31.95 31.95 0 0132 .05 31.95 31.95 0 0163.95 32z"
                                  transform="scale(.26458 .26459)"
                                  fill="#fff"
                                  fill-opacity=".94"
                                  stroke="#999"
                                  stroke-width=".1" />
                            <path d="M7.988 10.25V8.858H5.292v-.783h2.696V5.292h.87v2.783h2.784v.783H8.858v2.784h-.87z"
                                  fill="#999" />
                        </svg>
                    </ng-template>
                </custom-cursor>
            </div>
            <h3 class="m-b-1 left"
                [sizeClass]="'XLM:h3,S:p1'">{{$any(item).image_title}}</h3>
            @if (!client.isS) {
            <p class="p2 left">{{$any(item).image_subtitle}}</p>
            }
        </ng-template>
        }
    </carousel>
    <carousel-dots class="m-t m-b"
                   [carouselRef]="carouselRef"
                   [paginationClass]="'product-details-carousel-pagination'" />
</div>
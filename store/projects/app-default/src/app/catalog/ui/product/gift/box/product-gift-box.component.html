@if (model.data) {
<div class="flex flex-middle p-t-2 p-r p-b-2 p-l-2 bg-col-5 b-radius-4 cursor-pointer"
     tabindex="0"
     (click)="clickHandle()"
     (keypress)="clickHandle()">
    <checkbox class="pe-none is-dot _radio"
              [name]="'giftBox'"
              [(ngModel)]="giftBoxSelection"
              [ngModelOptions]="{ standalone: true }" />
    <img class="w-12 _image"
         [dfImage]="model.data.image"
         [ratio]="'ratio-5-4'"
         alt="Gift Box">
    <div class="flex-grow _content">
        <p class="flex s1">
            <span class="flex-grow">{{model.data.name}}</span>
            <span class="right _price">
                @if (model.orgPrice) {
                <price class="block line-through col-13"
                       [value]="model.orgPrice" />
                }
                <price [class.col-2]="model.orgPrice"
                       [value]="model.price" />
            </span>
        </p>
        <div class="text p2"
             [innerHtml]="model.data.short_description"></div>
    </div>
</div>
}
:host {
    ._option {
        &.is-picked {
            border-color: var(--atomic-border-color-1);
        }

        &.is-disabled {
            background-color: var(--atomic-background-color-5);
            color: var(--atomic-color-13);
            opacity: 1 !important;
            pointer-events: auto !important;

            &:not(.is-picked) {
                border-color: var(--atomic-border-color-5) !important;
            }
        }
    }
}

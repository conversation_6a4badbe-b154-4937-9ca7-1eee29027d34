@if (options) {
@for (option of options; track option.attribute.id) {
<!-- title line -->
<div class="flex flex-middle flex-justify-between"
     [class]="modal() ? !$first ? 'b-t b-col-5 p-t' : 'p-t-3' : 'b-t b-col-5 p-t-1 _label'">
    <h6>
        @if (configuration.hasSelection(option.attribute.id)) {
        <span class="s2">{{'Select %' | translate : option.attribute.data!.label}}:&nbsp;</span>
        <span class="p2">
            @let selectedProduct = productConfigurationPlugin.getSelectionProduct(option.attribute.id);
            @if (selectedProduct && option.attribute.id !== 'color') {
            <ng-template [productStatus]="selectedProduct"
                         let-status="status">
                @if (!status.isSalable) {
                <span class="m-l-1 uppercase col-2">{{'Out of stock' | translate}}</span>
                }
                @if (status.isLowQty) {
                <span class="m-l-1">{{'Only % remaining' | translate : status.maxQty}}</span>
                }
            </ng-template>
            } @else {
            {{configuration.getSelection(option.attribute.id) | optionLabel : option.attribute.id}}
            }
        </span>
        } @else {
        <!-- unselected -->
        <span class="s2">{{'Select %' | translate : option.attribute.data!.label}}</span>
        }
    </h6>
    @if (!!productViewLayout().sizeGuideVariant && option.attribute.id === 'size') {
    <button class="m-r-1 b-b b-col-1 p2"
            type="button"
            (click)="productViewLayout().openSidebar('size-guide')">
        {{'Size guide' | translate}}
    </button>
    }
</div>
@if (option.attribute.data?.isSwatch) {
<div class="flex flex-wrap _options is-swatch"
     [class]="[modal() ? 'm-t-2' : 'm-t-2 m-t-1-m m-t-1-s']">
    @for (item of option.values; track item.value) {
    <button class="cursor-pointer"
            [item]="item"
            [value]="item.value"
            [isSelected]="configuration.isSelected(option.attribute.id, item.value)"
            [isDisabled]="!!item.error"
            uiButton
            productLayoutConfigurableOptionsSwatch
            [bgContrast]="getOptionSwatch(item)"
            [bgContrastTolerance]="250"
            (click)="select(option.attribute.id, item.value)">&nbsp;</button>
    }
</div>
} @else {
@let maxGrid = modal() ? 4 : 7;
@if (option.values.length > maxGrid) {
<div class="_options is-size"
     [class]="modal() ? `m-t-3 ${!$last ? 'p-b-3' : 'p-b-2'}` : `m-t-1 ${!$last ? 'p-b' : 'p-b-2'}`"
     [grid]="maxGrid"
     [gridColumnGap]="modal() ? '8px' : '11px'"
     [gridRowGap]="modal() ? '12px' : '8px'">
    @for (item of option.values; track item.value) {
    <ng-container *ngTemplateOutlet="sizeTpl; context: { $implicit: item }" />
    }
</div>
} @else {
<div class="flex flex-wrap p-b-2 _options is-size"
     [class]="modal() ? `m-t-3 ${!$last ? 'p-b-3' : 'p-b-2'}` : `m-t-1 ${!$last ? 'p-b' : 'p-b-2'}`">
    @for (item of option.values; track item.value) {
    <ng-container *ngTemplateOutlet="sizeTpl; context: { $implicit: item }" />
    }
</div>
}

<ng-template #sizeTpl
             let-item>
    <button class="p2 cursor-pointer"
            [item]="item"
            [value]="item.value"
            [isSelected]="configuration.isSelected(option.attribute.id, item.value)"
            [isDisabled]="!!item.error"
            [title]="getTooltip(item.error)"
            uiButton
            productLayoutConfigurableOptionsSwatch
            [bgContrast]="getOptionSwatch(item)"
            [bgContrastTolerance]="250"
            (click)="select(option.attribute.id, item.value)">
        {{item.value | optionLabel : option.attribute.id}}
    </button>
</ng-template>
}
}
}
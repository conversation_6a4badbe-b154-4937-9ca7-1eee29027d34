@for (section of sections; track $index) {
<div class="m-t-3">
    <p class="s1 fw-bold m-b-2">{{section.heading}}</p>

    <!-- attrs list -->
    <ul [class.list-style-disc]="section.bulletpoints">
        @for (attribute of section.attributes; track $index) {
        @if (attribute.valuesLabels?.length) {
        <li>
            @if (attribute.mode === EMode.cms) {
            <!-- CMS content blocks -->
            @for (value of attribute.values; track $index) {
            <product-general-details-item class="block"
                                          [variant]="[attribute.code, value].join('-')" />
            }
            } @else {
            <!-- static list -->
            <div class="flex flex-top">
                <!-- with optional title  -->
                @if(attribute.mode === 'attributeTitleValue') {
                <span class="p1">{{attribute.code | attributeLabel}}:&nbsp;</span>
                }
                <!-- value -->
                <p class="col-12 p1"
                   [innerHTML]="attribute.valuesLabels"></p>
            </div>
            }
        </li>
        }
        }
    </ul>
</div>
}

<!-- desktop layout  -->
@if (!client.isS) {
<ng-container *ngTemplateOutlet="optionsTpl" />
}

<!-- overlay -->
<ng-template #trayModalContentTemplateRef>
    <tray class="left-4 right-4"
          [open]="70"
          (offClick)="closeTray()"
          [offClickFilter]="'wishlist-message'">
        <ng-container *ngTemplateOutlet="optionsTpl" />
    </tray>
</ng-template>

<!-- OPTIONS  -->
<ng-template #optionsTpl>
    <div class="w-12 center no-wrap _options"
         [sizeClass]="'!S: p-r-2 p-l-2 inline-flex flex-middle flex-justify-center b-radius-max bg-col-35 col-12 overflow-x-auto, S: flex-column bg-col-w'">
        @if (client.isS) {
        <div class="s1 col-1 m-t p-b-1">
            {{'Select your %' | translate : (sizeAttribute.code | attributeLabel | lowercase)}}
        </div>
        }
        @for (option of lineOptions; track option.value) {
        <action class="button-3 button-size-1 m-r-0-s b-a-0 _option"
                [class.m-b--1-s]="!$last"
                [class.m-b-4-s]="$last"
                [class._in-wishlist]="inWishlist(option)"
                [status]="getStatus(option)"
                (click)="add(option, sizeAttribute)">
            <span class="pos-relative-s fw-bold col-1">{{option.label}}</span>
        </action>
        }
    </div>
</ng-template>
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { GridModule } from '@df/ui/atomic/atom/grid/grid.module';
import { DragScrollDirective } from '@df/ui/common/drag-scroll.directive';
import { ReplacePipe } from '@df/ui/common/replace.pipe';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ActionComponent } from '../../../../../ui/api/action.component';
import { ResultComponent } from '../../../../../ui/api/result.component';
import { TrayComponent } from '../../../../../ui/widget/tray/tray.component';
import { ProductStatusDirective } from '../../product-status.directive';
import { ProductListingConfigurableOptionsAbstract } from './product-listing-configurable-options.abstract';
import { ProductLayoutConfigurableOptionsSwatchComponent } from '../../layout/configurable-options/swatch/product-layout-configurable-options-swatch.component';
import { UiButtonDirective } from '@df/ui/button/button.directive';

@Component({
    selector: 'product-listing-configurable-options',
    templateUrl: './product-listing-configurable-options.component.html',
    styleUrl: './product-listing-configurable-options.component.scss',
    standalone: true,
    imports: [
        ActionComponent,
        CommonModule,
        DragScrollDirective,
        GridModule,
        ProductLayoutConfigurableOptionsSwatchComponent,
        ProductStatusDirective,
        ReplacePipe,
        ResultComponent,
        TrayComponent,
        TranslatePipe,
        UiButtonDirective
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductListingConfigurableOptionsComponent extends ProductListingConfigurableOptionsAbstract {}

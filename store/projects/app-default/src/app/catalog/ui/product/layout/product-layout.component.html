@if (content) {
<div class="w-12 pos-relative _media"
     [class._layout-grid]="mediaLayoutGrid">
    <product-layout-media [class]="mediaLayoutGrid() ? 'w-12' : 'w-6 w-12-m w-12-s fill-m fill-s pos-relative-l pos-relative-x'"
                          [product]="product()"
                          [media]="media()"
                          [content]="content"
                          (mediaLayoutChange)="handleMediaLayoutChange($event)" />
    @if (!mediaLayoutGrid() && !client.isM && !client.isS) {
    <div class="w-6 flex flex-middle flex-justify-center _details">
        <div class="overflow-hidden overflow-y-auto custom-scrollbar">
            <product-layout-details class="pos-relative pos-center w-8 p-r-2 p-l-2"
                                    [product]="product()"
                                    [content]="content" />
        </div>
    </div>
    }
    @if (mediaLayoutGrid() || client.isM || client.isS) {
    <product-layout-add-to-basket-panel [visible]="addToBasketPanelVisible()"
                                        [productViewLayout]="this" />
    }
    @if (client.isS || client.isM) {
    <product-layout-wishlist class="pos-fixed z-9 w-12 p-t-2 p-r-2 p-l-2"
                             [product]="product()" />
    }
</div>
@if (mediaLayoutGrid() || client.isM || client.isS) {
<product-layout-details class="w-12"
                        [product]="product()"
                        [content]="content"
                        [split]="true"
                        (productNameVisible)="handleProductNameVisible($event)" />
}
<related-products class="block"
                  [source]="productView.model"
                  [listName]="'Looks good with...' | translate"
                  [limit]="1000"
                  [options]="{ engines: ['crosssell'] }">
    <h2 class="h2 left m-t-2 m-b">{{'Looks good with...' | translate}}</h2>
</related-products>

<!-- related products upsell -->
<related-products class="block"
                  [source]="productView.model"
                  [listName]="'Related items' | translate"
                  [limit]="1000"
                  [options]="{ engines: ['upsell'] }">
    <h2 class="h2 left m-t-2 m-b">{{'Related items' | translate}}</h2>
</related-products>
<!-- further content -->
@if (cmsTemplate) {
<cms-outlet-block name="PDP Layout components"
                  [variant]="cmsTemplate" />
}
<icms-outlet [components]="content.bottomComponents || []"
             [name]="'Bottom section outlet'" />
<product-layout-sidebar [product]="product()"
                        [content]="content" />
}
<!-- configurable options modal -->
<ng-template #productLayoutConfigurableOptionsModalTpl>
    <div class="pos-relative z-3 bg-col-w b-t b-col-5"
         (offClick)="closeProductLayoutConfigurableOptionsModal()"
         [offClickFilter]="'back-in-stock-modal'">
        <product-layout-configurable-options class="block p-r p-l"
                                             [productViewLayout]="this"
                                             [modal]="true" />
        <product-layout-add-to-basket class="w-12"
                                      [productViewLayout]="this"
                                      [modal]="true" />
    </div>
</ng-template>
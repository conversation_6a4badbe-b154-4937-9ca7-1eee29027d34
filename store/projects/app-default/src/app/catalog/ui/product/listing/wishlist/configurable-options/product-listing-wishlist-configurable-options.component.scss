@use '@tomandco/atomic/scss/component';
@use 'styles/icons';

:host {
    ._options {
        &,
        > div,
        > div > div {
            height: 35px;
        }

        .button-1 {
            min-width: 24px;
            height: 24px;
            padding: 0 !important;
            border-color: transparent;
            border-radius: 12px;
            background-color: transparent;
            line-height: 24px;
        }

        ._option.is-padding {
            padding-right: 4px !important;
            padding-left: 4px !important;
        }
    }

    .is-picked {
        border-color: var(--atomic-border-color-1);
    }

    .is-disabled {
        background-color: var(--atomic-background-color-5);
        color: var(--atomic-color-13);
        opacity: 1 !important;
        pointer-events: auto !important;

        &:not(.is-picked) {
            border-color: var(--atomic-border-color-5) !important;
        }
    }

    result {
        display: flex;
        align-items: center;
        justify-content: center;

        .hidden {
            pointer-events: none;
        }

        @include component.state-busy {
            &::before {
                position: absolute;
                top: 1px;
                bottom: 1px;
                left: 0;
                width: 0;
                height: calc(100% - 2px);
                border-radius: 35px;
                margin: 0 !important;
                animation: progress 12000ms forwards;
                background-color: var(--atomic-background-color-23);
                content: '';
                opacity: 0;
            }

            &::after {
                position: absolute;
                top: 50%;
                right: 0;
                left: 0;
                color: var(--atomic-color-13);
                content: 'Adding...';
                text-align: center;
                transform: translate(0, -50%);
            }

            &.hidden {
                visibility: visible !important;
            }
        }

        @include component.state-success {
            color: var(--atomic-color-1);

            &::after {
                @extend .icon-thick, :before;

                @include icons.icons;

                position: absolute;
                top: 50%;
                right: 1em;
                background-color: var(--atomic-background-color-35);
                color: var(--atomic-color-21);
                transform: translate(0, -50%);
            }

            &.hidden {
                visibility: visible !important;
            }
        }

        &.success,
        &.error {
            border-radius: 35px;
            background-color: var(--atomic-background-color-35);

            &.hidden {
                visibility: visible !important;
            }
        }
    }
}

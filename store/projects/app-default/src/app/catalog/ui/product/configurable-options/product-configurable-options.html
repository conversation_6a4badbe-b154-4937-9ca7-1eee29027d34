@for (attribute of attributes || []; track attribute.code) {
<!-- attribute name -->
<h6 class="p-t-3 p-b-2 p-b-3-s flex flex-middle flex-justify-between col-13 fs-3-s left-s p2"
    [class.b-b-s]="attribute.code !== 'letter'">
    @if (!!data[attribute.code] && !isModernOptionType(attribute) && !app.client.isS) {
    {{data[attribute.code] | optionLabel : attribute.code}}
    } @else {
    <span class="col-1">{{'Select' | translate}} {{attribute.code | attributeLabel}}</span>
    @if (!!sizeGuideVariant) {
    <button class="inline-flex flex-middle col-1"
            type="button"
            (click)="sizeGuideClickHolder(sizeGuideVariant)">
        <span class="c1 underline">{{'Size guide' | translate}}</span> <i class="icon-size-guide fs-7 p-l-1"
           aria-hidden="true"></i>
    </button>
    }
    }
</h6>

<!-- letter type attrs -->
@if (isSwatchTypeAttribute(attribute)) {
@if (isModernOptionType(attribute)) {
<div class="p-b b-t-1-s letter-options"
     [class.p-t-4-s]="attribute.code === 'color'"
     [class.b-b]="!$last"
     [grid]="[4, 4, 4, attribute.code !== 'letter' ? 5 : 7][app.client.sizeId]"
     [gridColumnGap]="[8,8,2,2][app.client.sizeId]"
     [gridRowGap]="[4,4,2,2][app.client.sizeId]">

    <!-- attribute options -->
    @for (option of attribute.options; track option.value) {
    <div class="center">
        <label class="w-12 p-t-1 p-t-2-s p-r-2 p-r-0-s p-b-1 p-b-2-s p-l-2 p-l-0-s b-a b-radius-max fs-4 center pe-auto-s cursor-pointer option-rounded"
               [class.is-picked]="isPicked(attribute, option)"
               [class.is-disabled]="!option.salable"
               (click)="pick(attribute, option)">{{ option.label }}</label>
        @if (attribute.code === 'color') {
        <ng-template [productStatus]="$any(option.product)"
                     let-status="status">
            @if (!status.isSalable) {
            <div class="p-t-1 p-t-2-s c1">{{'Out of stock' | translate}}</div>
            }
            @if (status.isLowQty) {
            <div class="p-t-1 p-t-2-s c1">{{'Only % remaining' | translate : status.maxQty}}</div>
            }
        </ng-template>
        }
    </div>
    }
</div>
} @else {
<!-- swatch type attrs - PRODUCT IMAGE -->
<div class="w-12 p-b"
     dragScroll>
    <!-- attribute options -->
    @for (option of attribute.options; track option.value) {
    <label class="inline-block b-a cursor-pointer option pe-auto-s ratio-4-5"
           [ngClass]="isPicked(attribute, option) ? 'b-col-4' : 'b-col-t'"
           (click)="pick(attribute, option)">
        <img draggable="false"
             [dfImage]="option.product.data.image"
             [dfSrcset]="'40w, 60w, 80w, 100w'"
             [ratio]="'4/5'"
             fill>
    </label>
    }
</div>
}
} @else {
<!-- standard attrs - text -->
@if (!app.client.isS) {
<div class="p-b-2 center">
    @for (option of attribute.options; track option.value) {
    <label class="inline-block p-a-2 b-a cursor-pointer fs-4 center"
           [ngClass]="isPicked(attribute, option) ? 'b-col-4' : 'b-col-t'"
           (click)="pick(attribute, option)">{{option.label}}</label>
    }
</div>
} @else {
<div class="m-t p-b b-b letter-options"
     [grid]="4"
     [gridColumnGap]="8"
     [gridRowGap]="4">
    <!-- attribute options -->
    @for (option of attribute.options; track option.value) {
    <div class="center">
        <label class="w-12 p-t-2 p-b-2 b-a b-radius-max fs-4 center pe-auto-s cursor-pointer option-rounded"
               [class.is-picked]="isPicked(attribute, option)"
               [class.is-disabled]="!option.salable"
               (click)="pick(attribute, option)">{{option.label}}</label>
        <ng-template [productStatus]="$any(option.product)"
                     let-status="status">
            @if (!status.isSalable) {
            <div class="p-t-1 p-t-2-s c1">{{'Out of stock' | translate}}</div>
            }
            @if (status.isLowQty) {
            <div class="p-t-1 p-t-2-s c1">{{'Only % remaining' | translate : status.maxQty}}</div>
            }
        </ng-template>
    </div>
    }
</div>
}
}
}
@if (app.client.isS) {
<ng-template [productStatus]="$any(product)"
             let-status="status">
    @if (!status.isSalable && hasAllSelection()) {
    <div class="flex flex-middle flex-justify-center p-t p-b b-t no-wrap"
         (click)="openXnotifEmail()">
        <span class="m-r-2 underline p1">
            {{'Email me if back in stock' | translate}}
        </span>
        <i class="icon-email fs-9 inline-flex"
           aria-hidden="true"></i>
    </div>
    }
</ng-template>
}
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { GridModule } from '@df/ui/atomic/atom/grid/grid.module';
import { DragScrollDirective } from '@df/ui/common/drag-scroll.directive';
import { ReplacePipe } from '@df/ui/common/replace.pipe';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ActionComponent } from '../../../../../ui/api/action.component';
import { ResultComponent } from '../../../../../ui/api/result.component';
import { TrayComponent } from '../../../../../ui/widget/tray/tray.component';
import { ProductStatusDirective } from '../../product-status.directive';
import { ProductListingConfigurableOptionsAbstract } from './product-listing-configurable-options.abstract';

@Component({
    selector: 'product-listing-configurable-options-exp',
    templateUrl: './product-listing-configurable-options-exp.component.html',
    styleUrl: './product-listing-configurable-options-exp.component.scss',
    standalone: true,
    imports: [
        ActionComponent,
        CommonModule,
        DragScrollDirective,
        GridModule,
        ProductStatusDirective,
        ReplacePipe,
        ResultComponent,
        TrayComponent,
        TranslatePipe
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductListingConfigurableOptionsExpComponent extends ProductListingConfigurableOptionsAbstract {}

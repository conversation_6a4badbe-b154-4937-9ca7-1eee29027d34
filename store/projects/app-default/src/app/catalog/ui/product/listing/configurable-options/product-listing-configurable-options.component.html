<!-- desktop layout  -->
@if (client.isL || client.isX) {
<div class="w-12 center no-wrap overflow-hidden _options"
     [class.ng-hide]="status.busy"
     dragScroll
     [dragscrollFade]="false">
    <div class="flex flex-middle flex-justify-center">
        @for (option of sizeOptions; track option.value) {
        @if (option.product) {
        <action class="button-1"
                [status]="getStatus(option)"
                (click)="buy(option)">
            <span class="no-wrap _option"
                  [class.is-padding]="option.padding">
                <span [ngClass]="!option.salable ? 'line-through col-13' : ''">
                    @if (!hidePrefix) {
                    {{option.label}}
                    } @else {
                    {{option.label | replace : pattern : ''}}
                    }
                </span>
            </span>
        </action>
        }
        }
    </div>
</div>

<result class="fill z-3 center"
        [customClasses]="'col-13'"
        [duration]="5"
        [success]="'Added' | translate"
        [status]="status" />
}

<!-- overlay -->
<ng-template #trayModalContentTemplateRef>
    <tray [static]="true"
          [open]="70">
        <div class="wrap">
            <div class="flex flex-middle flex-justify-between m-t-1 p-t-2 p-b-2 p-b-3-s">
                <div class="flex-grow flex p2">
                    {{'Select Size:' | translate}}
                    @if (processedOption?.product) {
                    <ng-template [productStatus]="processedOption.product"
                                 let-status="status">
                        @if (!status.isSalable) {
                        <span class="col-2 p-l-1 uppercase">{{'Out of stock' | translate}}</span>
                        }
                        @if (status.isLowQty) {
                        <span class="p-l-1">{{'Only % remaining' | translate : status.maxQty}}</span>
                        }
                    </ng-template>
                    }
                </div>
                @if (product?.hasSizeGuide) {
                <button class="m-r-1 b-b b-col-1 c1"
                        type="button"
                        (click)="openSizeGuide()">
                    <span>{{'Size guide' | translate}}</span>
                </button>
                }
            </div>

            <div class="is-size _options-tray"
                 [grid]="[4, 4, sizeOptions.length <= 3 ? 3 : 7, sizeOptions.length <= 3 ? 3 : 7][client.sizeId]"
                 [gridColumnGap]="'8px'"
                 [gridRowGap]="'12px'">
                <!-- attribute options -->
                @for (option of sizeOptions; track option.value) {
                <button class="p2 cursor-pointer"
                        [isSelected]="isPicked(sizeAttribute, option)"
                        [isDisabled]="!option.salable"
                        uiButton
                        productLayoutConfigurableOptionsSwatch
                        [bgContrast]="getOptionSwatch(option)"
                        [bgContrastTolerance]="250"
                        (click)="pick(sizeAttribute, option)">
                    {{option.label}}
                </button>
                }
            </div>
        </div>

        <action class="w-12 m-t button"
                [class.is-disabled]="processedOption && !processedOption?.salable"
                cy-basketAddButton
                [status]="status"
                (click)="handleClick()">
            @if (!processedOption || (hasSizeOption && !data?.['size'])) {
            @if (hasSizeOption && hasLetterOption) {
            {{'Select size & letter' | translate}}
            } @else if (hasSizeOption && !hasLetterOption) {
            {{'Select size' | translate}}
            } @else if (!hasSizeOption && hasLetterOption) {
            {{'Select letter' | translate}}
            }
            } @else {
            @if (processedOption?.salable) {
            @if (!status.busy) {
            {{'Add to bag' | translate}}
            } @else {
            {{'Adding...' | translate}}
            }
            } @else {
            <span class="w-12 flex flex-middle flex-justify-center gap-12px p1">
                <i class="icon-email-24"
                   aria-hidden="true"></i>
                {{'Out of stock - notify me' | translate}}
            </span>
            }
            }
        </action>
        <result class="block m-t-2 s1"
                [status]="status" />
    </tray>
</ng-template>
import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { IProductDataV3 } from '@df/catalog/product/product';
import { DfImageDirective } from '@df/module-image/df-image.directive';
import { ShellComponent } from '@df/ui/common/shell/shell.component';
import { PhotoSwipeModule } from '@df/ui/photoswipe/photoswipe.module';
import { PhotoswipeOptions } from '@df/ui/photoswipe/photoswipe.types';
import { UiComponent } from '@df/ui/ui.component';
import { CustomCursorComponent } from '../../../../../../ui/display/custom-cursor/custom-cursor.component';
import { LocalVimeoPlayerComponent } from '../../../../../../ui/display/media/vimeo-player.component';
import { ProductDecorator } from '../../../../../model/product/product.decorator';
import { IMediaRow } from '../../layout/product-view-layout.interface';

@Component({
    selector: 'product-view-media-item',
    templateUrl: './product-view-media-item.component.html',
    styleUrl: './product-view-media-item.component.scss',
    standalone: true,
    imports: [CustomCursorComponent, DfImageDirective, LocalVimeoPlayerComponent, PhotoSwipeModule],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductViewMediaItemComponent extends UiComponent {
    item = input.required<IMediaRow>();
    priority = input(false);
    photoswipeUid = input<string>();
    _photoswipeOptions = input<Partial<PhotoswipeOptions>>({}, { alias: 'photoswipeOptions' });
    product = input<ProductDecorator<IProductDataV3>>();
    parent = input<ProductDecorator<IProductDataV3>>();

    photoswipeOptions = computed(() => ({
        canPreload: true,
        loadAfterOpen: false,
        imageOptions: {
            width: 4096,
            height: 4096
        },
        ...this._photoswipeOptions()
    }));
}

:host {
    --swiper-pagination-right: auto;
    --swiper-pagination-left: 24px;
    --swiper-pagination-color: var(--atomic-background-color-12);
    --swiper-pagination-bullet-inactive-color: #fff;
    --swiper-pagination-bullet-inactive-opacity: 1;

    ._icon {
        top: 0;
        width: 53px;
        height: 53px;

        i {
            display: block;
            width: 29px;
            height: 29px;
            font-size: 29px;
            border-radius: 5px;
            background-color: #fff;
        }
    }

    carousel.fill ::ng-deep {
        swiper-container {

            &,
            &::part(container) {
                position: absolute;
                inset: 0;
            }

            .swiper-slide {
                height: 100% !important;
            }
        }

        ._placeholder-slide {
            height: 100% !important;
        }
    }

    :host-context(.size-s),
    :host-context(.size-m) {
        --swiper-pagination-right: auto;
        --swiper-pagination-left: 50%;

        carousel.fill ::ng-deep swiper-container::part(pagination) {
            height: 8px !important;
            top: auto;
            bottom: 58px;
            transform: translateX(-50%);
        }

        carousel.fill ::ng-deep swiper-container::part(bullet),
        carousel.fill ::ng-deep swiper-container::part(bullet-active) {
            top: 0 !important;
        }
    }

    :host-context(.size-l),
    :host-context(.size-x) {
        carousel.fill ::ng-deep swiper-container::part(pagination) {
            width: 8px !important;
        }

        carousel.fill ::ng-deep swiper-container::part(bullet),
        carousel.fill ::ng-deep swiper-container::part(bullet-active) {
            left: auto !important;
        }
    }

    :host-context(.page-header-transparent) {
        ._icon {
            top: var(--page-header-height, 0px);
        }
    }
}
import { ChangeDetectionStrategy, Component, Input, type OnChanges, type SimpleChang<PERSON> } from '@angular/core';
import { Product, type IProductDataV3 } from '@df/catalog/product/product';
import { ProductConfigurationBaseService } from '@df/module-product-configuration/model/base/product-configuration-base.service';
import { configurableMismatchCodeEngine } from '@df/module-product-configuration/model/configurable/options/engines/configurable-missmatch.engine';
import { configurableStockCodeEngine } from '@df/module-product-configuration/model/configurable/options/engines/configurable-stock.engine';
import { getProductConfigurationProviders } from '@df/module-product-configuration/provider/product-configuration.providers';
import { UiComponent } from '@df/ui/ui.component';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { ProductConfigurationBaseServiceDecorator } from '../../../../../module/product-configuration/model/base/product-configuration-base.service.decorator';
import '../../../../../module/product-configuration/plugins/product-configuration-plugin';
import { type ProductDecorator } from '../../../../model/product/product.decorator';
import { ProductLayoutComponent } from '../product-layout.component';
import { ProductLayoutWishlistService } from '../wishlist/product-layout-wishlist.service';

@Component({
    selector: 'product-layout-view',
    templateUrl: './product-layout-view.component.html',
    imports: [ProductLayoutComponent],
    providers: getProductConfigurationProviders({
        component: ProductLayoutViewComponent,
        metaIntegration: true,
        configurable: () => ({
            order: ['color', 'size'],
            hashStorageKey: 'selection',
            urlModel: 'parent',
            metaModel: 'child',
            optionsEngines: [configurableMismatchCodeEngine({ logic: 'error' }), configurableStockCodeEngine({ logic: 'error' })],
            sanitizeSelection: (selection, product, productConfiguration) => {
                // preselect color if not set
                if ('color' in selection && !selection['color']) {
                    const colorOption = (productConfiguration.options$.value || []).find(i => i.attribute.id === 'color');

                    if (colorOption?.values?.length) {
                        selection['color'] = colorOption.values[0].value;
                    }
                }

                // find configurable options with only one value and preselect it
                const optionsWithOneValue = productConfiguration.options$.value.filter(i => i.values?.length === 1);

                optionsWithOneValue.forEach(option => {
                    if (!selection[option.attribute.id]) {
                        selection[option.attribute.id] = option.values[0].value;
                    }
                });
            }
        }),
        localProviders: [
            {
                provide: ProductConfigurationBaseService,
                useClass: ProductConfigurationBaseServiceDecorator
            },
            ProductLayoutWishlistService
        ]
    }),
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductLayoutViewComponent extends UiComponent implements OnChanges {
    @Input({ required: true })
    model!: ProductDecorator<IProductDataV3>;

    readonly model$ = new BehaviorSubject<Product<IProductDataV3> | undefined>(undefined);

    protected override canDetach = false;

    get parent() {
        return this.model.parent as ProductDecorator<IProductDataV3>;
    }

    override ngOnChanges(changes: SimpleChanges): void {
        if (this.model$.value !== this.model) {
            this.model$.next(this.model);
        }

        super.ngOnChanges(changes);
    }
}

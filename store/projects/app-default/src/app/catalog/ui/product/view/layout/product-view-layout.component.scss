@use '@tomandco/atomic/scss/core';
@use '@tomandco/atomic/scss/atom/color';
@use '@tomandco/atomic/scss/atom/typography';

:host {
    position: relative;
    display: block;
    width: 100%;

    --dots-max-top: calc(
        (var(--element-height-promo-bar, 0) + var(--element-height-product-media, 0) + var(--element-height-page-header, 0)) - 22px
    );
    --dots-top: calc(100vh - (var(--element-height-mobile-footer-content, 0) + 22px));

    :host-context(.page-header-transparent) {
        --dots-max-top: calc((var(--element-height-promo-bar, 0) + var(--element-height-product-media, 0)) - 22px);
    }

    product-configurable-options {
        opacity: 1;
        transition: opacity 500ms 500ms ease-in;
    }

    &.photoswipe--active tray {
        bottom: -100% !important;
        transition: bottom 750ms ease-out;

        product-configurable-options {
            opacity: 0;
            transition: opacity 500ms ease-out;
        }
    }

    &.photoswipe--inactive tray {
        transition: bottom 750ms ease-in;
    }

    .product-media-carousel.has-carousel-dots-visible ::ng-deep carousel-dots {
        position: fixed;
        top: min(var(--dots-top), var(--dots-max-top));
    }

    .product-media-carousel ::ng-deep {
        carousel-dots {
            position: absolute;
            z-index: 11;
            top: auto;
            right: 12px;
            bottom: 12px;
            display: inline-block;
            height: 10px;
            padding: 0;

            .carousel-dots-inner {
                --carousel-dot-height: 10px;
                --carousel-dot-width: 10px;
                --carousel-dots-background: #fff;
                --carousel-dots-background-hover: #{color.get(4)};
                --carousel-dots-background-active: #{color.get(12)};

                height: 10px;
                justify-content: flex-start;
            }

            .carousel-dot {
                height: var(--carousel-dot-width);

                @for $i from 1 through 10 {
                    &:nth-of-type(#{$i}).is-active ~ .carousel-dots-underbar {
                        left: calc(var(--carousel-dot-width) * #{$i - 1});
                    }
                }

                &:first-of-type.is-active ~ .carousel-dots-underbar {
                    &::after {
                        transform: translate3d(0, -50%, 0);
                    }
                }

                &::after {
                    width: 6px;
                    height: 6px;
                    border-radius: 100%;
                }
            }

            .carousel-dots-underbar {
                width: var(--carousel-dot-width);
                height: var(--carousel-dot-width);

                &::after {
                    width: 6px;
                    height: 6px;
                    border-radius: 100%;
                }
            }
        }
    }
}

:host ::ng-deep {
    tray {
        will-change: bottom;
    }

    product-configurable-options {
        will-change: opacity;
    }

    .product-details {
        padding-top: var(--element-height-page-header);

        &__item {
            &:hover {
                .button-inline.is-visible::after {
                    margin-left: 100%;
                }
            }
        }
    }

    photoswipe-lightbox {
        --pswp-show-hide-transition-duration: 500ms;
        --pswp-controls-transition-duration: 500ms;
        --pswp-background-color: #{color.get(5)};
        --pswp-placeholder-color: #{color.get(5)};
        --pswp-error-text-color: #{color.get(1)};
    }

    .is-out-of-stock {
        opacity: 0.5;
    }
}

.icms-open {
    position: absolute;
}

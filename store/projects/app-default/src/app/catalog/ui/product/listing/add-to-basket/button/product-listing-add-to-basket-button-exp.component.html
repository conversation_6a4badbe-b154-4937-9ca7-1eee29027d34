<action class="w-12 button-2"
        [class.is-disabled]="disabled"
        cy-listingQuickAdd
        [status]="status"
        (click)="add()">
    <span class="inline-flex flex-middle p2">
        <i class="icon-plus m-r-1"
           [style.font-size.px]="10"
           [style.line-height]="0"></i>{{'Add' | translate}}
    </span>
</action>
<result class="fill z-3 center pos-absolute"
        [ngClass]="{'_busy-overlay' : !product.isConfigurable }"
        [status]="status"
        [customClasses]="'col-12 p2'"
        [duration]="5"
        [success]="'Added' | translate"
        *ifBrowser />

@if ((client.isL || client.isX) && gridMedia().length > 1) {
<button class="pos-absolute left-0 z-5 flex flex-middle flex-justify-center _icon"
        [attr.aria-label]="(productViewLayout.mediaLayoutGrid() ? 'View as grid' : 'View as scroll') | translate"
        (click)="handleMediaLayoutChange()">
    @if (productViewLayout.mediaLayoutGrid()) {
    <i class="icon-media-carousel"
       aria-hidden="true"></i>
    } @else {
    <i class="icon-media-grid"
       aria-hidden="true"></i>
    }
</button>
}
@if (productViewLayout.mediaLayoutGrid()) {
<div class="fill"
     [grid]="4"
     [gridColumnGap]="'1px'"
     [gridRowGap]="'1px'">
    @for (item of gridMedia(); track $index) {
    <div class="overflow-hidden pos-relative"
         [class]="['photoswipe-galleryuid-' + parent().id]"
         [gridColumn]="getGridColSpan($index)"
         [gridRowSpan]="getGridRowSpan($index)">
        <ng-container *ngTemplateOutlet="itemTpl; context: { $implicit: item, index: $index }" />
    </div>
    }
</div>
} @else if (gridMedia().length > 1) {
<carousel class="fill z-1"
          [style.--carousel-slides-per-view-round]="1"
          [style.--carousel-slides-per-view]="1"
          [style.--carousel-space-between.px]="0"
          [loop]="true"
          [pagination]="{
                dynamicBullets: true,
                clickable: true
            }"
          [mousewheel]="!client.isTouchy"
          [direction]="client.isL || client.isX ? 'vertical' : 'horizontal'"
          (afterInit)="handleAfterInit($event)">
    @for (item of gridMedia(); track $index) {
    <ng-template carouselSlide>
        <div class="overflow-hidden height-100 pos-relative"
             [class]="['photoswipe-galleryuid-' + parent().id]">
            <ng-container *ngTemplateOutlet="itemTpl; context: { $implicit: item, index: $index }" />
        </div>
    </ng-template>
    }
</carousel>
} @else {
@let item = gridMedia()[0];
<div class="fill overflow-hidden"
     [class]="['photoswipe-galleryuid-' + parent().id]">
    <ng-container *ngTemplateOutlet="itemTpl; context: { $implicit: item, index: 0 }" />
</div>
}

<!-- register images for photoswipe -->
@for (item of media(); track $index) {
<div class="ng-hide"
     [photoswipe]="item.image"
     [photoswipeUid]="item.image + '.' + parent().id + '.' + $index"
     [photoswipeOptions]="{
        index: parent().id + '.' + $index,
        galleryUID: parent().id,
        canPreload: true,
        loadAfterOpen: false,
        imageOptions: {
            width: 4096,
            height: 4096
        }
    }"></div>
}

<product-layout-media-lightbox [product]="product()"
                               [nativeElement]="nativeElement" />

<ng-template #itemTpl
             let-item
             let-index="index">
    <div class="fill z-1 _item"
         uiExitAnimation>
        @if (item?.image) {
        <img class="z-2"
             [dfImage]="item.image"
             [ratio]="'auto'"
             [priority]="true"
             [attr.alt]="product().data?.name || ''"
             [sizes]="getImageSizes(index)"
             [minSize]="256"
             [maxSize]="1080"
             [photoswipe]="item.image"
             [photoswipeUid]="item.image + '.' + parent().id + '.' + index"
             [photoswipeOptions]="{
                    index: parent().id + '.' + index,
                    galleryUID: parent().id,
                    canPreload: true,
                    loadAfterOpen: false,
                    imageOptions: {
                        width: 4096,
                        height: 4096
                    }
                }"
             fill>
        }
        <!-- video -->
        @if (item?.video) {
        <local-vimeo-player class="fill z-3 bg-col-w"
                            [video-url]="item.video"
                            [background]="true"
                            [autoplay-in-viewport]="true"
                            [loop]="true" />
        }
    </div>
</ng-template>
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, OnInit } from '@angular/core';
import { IProductDataV3 } from '@df/catalog/product/product';
import { DfImageDirective } from '@df/module-image/df-image.directive';
import { Renderer } from '@df/ng/renderer';
import { PhotoSwipeModule } from '@df/ui/photoswipe/photoswipe.module';
import { PhotoswipeOptions } from '@df/ui/photoswipe/photoswipe.types';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { UiComponent } from '@df/ui/ui.component';
import { ProductDecorator } from '../../../../../model/product/product.decorator';

@Component({
    selector: 'product-view-lightbox',
    templateUrl: './product-view-lightbox.component.html',
    styleUrl: './product-view-lightbox.component.scss',
    standalone: true,
    imports: [CommonModule, DfImageDirective, PhotoSwipeModule, TranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductViewLightboxComponent extends UiComponent implements OnInit {
    @Input()
    product?: ProductDecorator<IProductDataV3>;

    @Input()
    nativeElement?: HTMLElement;

    photoswipeOptions?: Partial<PhotoswipeOptions>;

    protected renderer = inject(Renderer);

    override ngOnInit(): void {
        super.ngOnInit();

        this.photoswipeOptions = {
            showAnimationDuration: 250,
            hideAnimationDuration: 250,
            pinchToClose: false,
            closeOnScroll: false,
            closeOnVerticalDrag: false,
            closeOnHistoryBack: true,
            tapToToggleControls: false,
            counterEl: false,
            barsSize: { top: 0, bottom: 'auto' },
            initialZoom: 1,
            followMouse: true,
            canPreload: false,
            loadAfterOpen: false,
            imageOptions: {
                width: 3276, // ratio 4/5
                height: 4096 // max supported size by edge io
            },
            getDoubleTapZoom: (isMouseClick, item: any) => {
                if (!item.zoomLevel) {
                    item.zoomLevel = item.initialZoomLevel;
                }

                let res: number;

                if (item.zoomLevel < 0.5 && (this.client.isM || this.client.isS)) {
                    res = 0.5;

                    this.renderer.addClass(this.nativeElement, 'photoswipe--zoom-50');
                    this.renderer.removeClass(this.nativeElement, 'photoswipe--zoom-100');
                } else if (item.zoomLevel < 1) {
                    res = 1;

                    this.renderer.addClass(this.nativeElement, 'photoswipe--zoom-100');
                    this.renderer.removeClass(this.nativeElement, 'photoswipe--zoom-50');
                } else {
                    res = item.initialZoomLevel;

                    this.renderer.removeClass(this.nativeElement, 'photoswipe--zoom-50');
                    this.renderer.removeClass(this.nativeElement, 'photoswipe--zoom-100');
                }

                item.zoomLevel = res;

                return res;
            }
        };
    }

    onPhotoswipeStateChange(state: boolean) {
        this.renderer.setClass(this.nativeElement, 'photoswipe--active', state);
        this.renderer.addClass(this.nativeElement, 'photoswipe--inactive');

        this.timeoutService.setTimeout(() => {
            this.renderer.removeClass(this.nativeElement, 'photoswipe--inactive');
        }, 500);
    }
}

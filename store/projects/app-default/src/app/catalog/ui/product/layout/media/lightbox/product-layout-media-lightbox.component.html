<photoswipe-lightbox [photoswipeOptions]="photoswipeOptions"
                     (photoswipeStateChange)="onPhotoswipeStateChange($event)">
    <ng-template #photoswipeThumbnailTpl
                 let-item
                 let-i="index"
                 let-photoswipe="photoswipe">
        @if (item.source) {
        <button class="pos-relative bg-col-5 pswp__thumb"
                [class.is-active]="item === photoswipe?.currItem"
                (click)="photoswipe?.goTo(i)">
            <img [dfImage]="item.source"
                 [attr.alt]="product().data?.name"
                 [ratio]="'3/4'"
                 fill>
        </button>
        }
    </ng-template>

    <ng-template #photoswipeIconCloseTpl>
        <i class="icon-arrow-left inline-block va-m"
           aria-hidden="true"></i>
        <span class="inline-block no-wrap">{{'Back to item' | translate}}</span>
    </ng-template>

    <ng-template #photoswipeIconPrevTpl />

    <ng-template #photoswipeIconNextTpl />
</photoswipe-lightbox>
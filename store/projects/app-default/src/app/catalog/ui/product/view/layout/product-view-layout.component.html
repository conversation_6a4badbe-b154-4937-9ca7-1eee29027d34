@if (content) {
<div #mainContent>
    <!-- product header -->
    <div class="w-12 flex flex-column-s">
        <div class="w-8 w-12-m w-12-s product-media pos-relative"
             [elementHeight]="'product-media'"
             #productMediaRef>
            @if (product.displayDiscount && labelDiscount) {
            <product-discount-label class="pos-absolute top-2 top-4-s top-4-m right-2 right-4-m right-4-s z-2"
                                    [product]="product" />
            }
            <!-- media gallery / !S -->
            @if (!client.isS) {
            @for (row of mediaRows; track i; let i = $index) {
            <div [ngClass]="'photoswipe-galleryuid-' + parent.id"
                 [style.padding-bottom.px]="!$last ? 2 : 0"
                 [grid]="2"
                 [gridColumnGap]="'2px'">
                @for (item of row; track j; let j = $index) {
                <product-view-media-item class="overflow-hidden"
                                         [ngClass]="item.ratio"
                                         [gridColumn]="item.colspan"
                                         [item]="item"
                                         [priority]="item.colspan === 2 ? i <= 1 : i === 0"
                                         [photoswipeUid]="item.image + '.' + parent.id + '.' + i + '.' + j"
                                         [photoswipeOptions]="{
                                                            index: i + '.' + j,
                                                            galleryUID: parent.id
                                                         }"
                                         [product]="product"
                                         [parent]="parent" />
                }
            </div>
            <!-- components that is always visible after the first row -->
            @if ((i === 0) && content.usp_block_variant) {
            <product-view-usp-block [variant]="content.usp_block_variant" />
            }
            } @empty {
            <div [grid]="2"
                 [gridColumnGap]="'2px'">
                <div class="overflow-hidden ratio-4-5">
                    <shell />
                </div>
                <div class="overflow-hidden ratio-4-5">
                    <shell />
                </div>
            </div>
            }
            @if (!client.isM && content.detail_carousel_enabled && mediaCarouselDetail.length) {
            <product-details-carousel class="block p-t p-b-8"
                                      [product]="product"
                                      [media]="mediaCarouselDetail">
                <h2 class="left m-t-2 m-b-3"
                    [sizeClass]="'XLM:h2,S:s1'">{{content.detail_carousel_title}}</h2>
            </product-details-carousel>
            }
            <!-- related products crosssell -->
            <ng-container *ngTemplateOutlet="relatedProductsTemplate" />
            } @else {
            <!-- wishlist warning / S -->
            <product-view-wishlist class="pos-absolute top-0 z-9 w-12 p-t-2 p-r-2 p-b p-b-0-s p-l-2"
                                   [product]="product"
                                   [color]="product.data.color" />
            @switch (horizontalImageScroll()) {
            @case (0) {
            <product-media [media]="media"
                           [product]="product" />
            }
            @case (1) {
            <carousel class="product-media-carousel has-carousel-dots-visible"
                      [slidesPerView]="1"
                      [loop]="true"
                      [loopAdditionalSlides]="1"
                      [pagination]="media.length > 1 ? {
                                el: '.product-media-pagination-' + product.id
                            } : false"
                      [allowTouchMove]="media.length > 1"
                      [allowSlideNext]="media.length > 1"
                      [allowSlidePrev]="media.length > 1"
                      [gsap]
                      [gsapUpdate]="gsapUpdate"
                      [scrollTrigger]="carouselDotsScrollTrigger"
                      (slideChange)="handleSlideChange()"
                      #carouselRef>
                @for (item of media; track $index) {
                <ng-template carouselSlide>
                    <div class="overflow-hidden"
                         [ngClass]="[item.landscape ? 'ratio-9-4' : 'ratio-4-5', 'photoswipe-galleryuid-' + parent.id]">
                        @if (item.image) {
                        <img [dfImage]="item.image"
                             [priority]="$index === 0"
                             [ratio]="item.landscape ? 'ratio-9-4' : 'ratio-4-5'"
                             [alt]="product.data?.name || ''"
                             [photoswipe]="item.image"
                             [photoswipeUid]="item.image + '.' + parent.id + '.' + $index"
                             [photoswipeOptions]="{
                                    index: parent.id + '.' + $index,
                                    galleryUID: parent.id,
                                    canPreload: true,
                                    loadAfterOpen: false,
                                    imageOptions: {
                                        width: 4096,
                                        height: 4096
                                    }
                                }"
                             fill>
                        }

                        <!-- video -->
                        @if (item.video) {
                        <local-vimeo-player class="bg-col-w"
                                            [video-url]="item.video"
                                            [background]="true"
                                            [autoplay-in-viewport]="true"
                                            [loop]="true" />
                        }
                    </div>
                </ng-template>
                }
                <div slot="container-end">
                    <carousel-dots [carouselRef]="carouselRef"
                                   [paginationClass]="'product-media-pagination-' + product.id" />
                </div>
            </carousel>
            }
            @default {
            <div class="overflow-hidden ratio-4-5">
                <shell />
            </div>
            }
            }
            }
        </div>
        <!-- product details / !S -->
        @if (client.sizeId > 1) {
        <product-view-details class="pos-relative w-4"
                              [product]="product"
                              [parentView]="this"
                              [parent]="parent"
                              [content]="content"
                              [elementHeight]="'product-view'" />
        }
    </div>
    <div id="top-section-cutout"></div>
    <ng-container *afterContentInit>
        @if (cmsTemplate && (client.isL || client.isX)) {
        <cms-outlet-block name="PDP Layout components"
                          [variant]="cmsTemplate" />
        }

        <!-- /////////////////// BOTTOM COMPONENTS /////////////////// -->
        @if ((client.isL || client.isX) && content.bottomComponents) {
        <icms-outlet [components]="content.bottomComponents"
                     [name]="'Bottom section outlet'" />
        } @else {
        @if (content.usp_block_variant) {
        <product-view-usp-block [variant]="content.usp_block_variant" />
        }
        <!-- general information  -->
        <product-view-summary [product]="product"
                              [parentView]="this"
                              [parent]="parent"
                              [content]="content" />

        <breadcrumbs class="wrap block m-t-3 p-b-2" />

        <!-- it is in details -->
        @if (content.detail_carousel_enabled && mediaCarouselDetail.length) {
        <product-details-carousel class="block p-b-8"
                                  [product]="product"
                                  [media]="mediaCarouselDetail">
            <h3 class="h3 left m-t-2 m-b">{{content.detail_carousel_title}}</h3>
        </product-details-carousel>
        }

        <!-- add to basket tray -->
        <product-mobile-footer class="pe-none"
                               (hideSizeOptions)="onSizeOptionsChange()">
            @if (parent?.isConfigurable && !parent?.hasLetterOption) {
            <div class="wrap">
                <product-configurable-options-swatch class="left"
                                                     [style.bottom.%]="100"
                                                     [product]="product"
                                                     [parentView]="this"
                                                     [preselectOptions]="['color']"
                                                     [preselectSingles]="true"
                                                     [storage]="true"
                                                     (init)="handleInitChange($event.data)"
                                                     (selectionChange)="handleSelectionChange($event.data)"
                                                     (dataChange)="handleDataChange($event.data)" />
            </div>
            }
            @if (parent?.isBundle) {
            <product-bundle-options [product]="product"
                                    [selection]="productView.bundleSelection"
                                    [buyRequest]="request" />
            }
            <div class="wrap p-b p-t pos-relative bg-col-w pe-auto"
                 elementHeight="mobile-footer-content">
                <product-view-name [product]="product"
                                   [parentView]="this"
                                   [parent]="parent"
                                   [content]="content"
                                   [showKlarna]="showKlarna" />
                @if (client.isS && showCountdownBanner) {
                <countdown-banner />
                }
                @if (product.isOutOfStock && !parent?.isConfigurable) {
                <button class="button w-12 m-t-1 is-out-of-stock"
                        (click)="openXnotifTray()">
                    <span class="flex flex-justify-center flex-middle w-12">
                        <span class="p1 m-r-1 m-r-2-s">{{'Email me when back in stock' | translate}}</span>
                        <i class="icon-email fs-9 inline-flex"
                           aria-hidden="true"></i>
                    </span>
                </button>
                } @else {
                <add-to-basket class="block"
                               [product]="product"
                               [parentView]="this"
                               [buyRequest]="request"
                               [selection]="configurableSelection"
                               [changeText]="true"
                               [useNotifyModal]="true" />
                }
                <div class="pos-absolute top-100 left-0 right-0 bg-col-w"
                     style="height:10rem;"></div>
            </div>
        </product-mobile-footer>
        }
    </ng-container>
</div>

<div #afterMainContent></div>
<!-- below this line we hide mobile sticky footer -->

<!-- further content -->
@if (client.isS || client.isM) {
<ng-container *ngTemplateOutlet="relatedProductsTemplate" />
@if (cmsTemplate) {
<cms-outlet-block name="PDP Layout components"
                  [variant]="cmsTemplate" />
}
@if (content.bottomComponents) {
<icms-outlet [components]="content.bottomComponents"
             [name]="'Bottom section outlet'" />
}
<!-- share button S -->
<product-view-share [product]="product" />
}
}

<!-- /////////////////// GENERAL INFORMATION /////////////////// -->
<ng-template #relatedProductsTemplate>
    <related-products class="block"
                      [source]="productView.model"
                      [listName]="'Style it with' | translate"
                      [limit]="1000"
                      [options]="{ engines: ['crosssell'] }">
        <h2 class="h2 left m-t-2 m-b">{{'Style it with' | translate}}</h2>
    </related-products>

    <!-- related products upsell -->
    <related-products class="block"
                      [source]="productView.model"
                      [listName]="'Related items' | translate"
                      [limit]="1000"
                      [options]="{ engines: ['upsell'] }">
        <h2 class="h2 left m-t-2 m-b">{{'Related items' | translate}}</h2>
    </related-products>
</ng-template>

<product-view-lightbox [product]="product"
                       [nativeElement]="nativeElement" />

<product-view-sidebar [product]="product"
                      [parentView]="this"
                      [parent]="parent"
                      [content]="content" />

<product-view-tray [product]="product"
                   [parentView]="this" />
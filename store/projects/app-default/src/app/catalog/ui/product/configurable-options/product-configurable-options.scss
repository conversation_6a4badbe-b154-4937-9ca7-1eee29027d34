@use '~@tomandco/atomic/scss/atom/color';
@use '~@tomandco/atomic/scss/core';

:host ::ng-deep {
    .option {
        width: 12.5%;
        margin-right: core.px-to-rem(2);
    }

    .option-rounded {
        &.is-picked {
            border-color: color.get(1);
        }

        &.is-disabled {
            background-color: color.get(5);
            color: color.get(13);
            opacity: 1 !important;
            pointer-events: auto !important;

            &:not(.is-picked) {
                border-color: color.get(5) !important;
            }
        }
    }
}

.increased-width {
    width: 101%;
}

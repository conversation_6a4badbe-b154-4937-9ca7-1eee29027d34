@if (model?.data) {
<div class="w-12 ratio-product-image pos-relative"
     [style.aspectRatio]="model.isLandscapeListingView | aspectRatio : gridRowSpan : gridColumn">

    @if(model.displayDiscount && labelDiscount){
    <product-discount-label class="pos-absolute z-2"
                            [ngClass]="listingSize === 1 ? 'bottom-4 left-4' : 'left-1 bottom-1 left-2-m left-2-s bottom-2-m bottom-2-s'"
                            [product]="model"></product-discount-label>
    }

    <!-- images -->
    <a class="fill overflow-hidden"
       [routerLink]="model.data?.url"
       [fragment]="urlFragment"
       [gtmTrackSelectItem]="gtmTrackSelectItem"
       [gtmTrackSelectItemOff]="app.client.isCms"
       [klevuTrackCategoryProductClick]="klevuCategoryProductTrackClick"
       [klevuTrackRecommendationsProductClick]="klevuRecommendationsProductClickData"
       [klevuTrackClick]="klevuSearchTrackClick">
        @if (listingImages?.length) {
        <!-- out of stock overlay-->
        @if (wishlistCarouselItem && model.isOutOfStock) {
        <div class="fill bg-col-w z-3"
             style="opacity:0.7;"></div>
        }
        <!-- img -->
        <df-image-wrapper class="fill">
            <img class="z-1"
                 [ngClass]="model.isLandscapeListingView ? 'w-12 flex flex-grow' : ''"
                 [dfImage]="primaryImage"
                 [ratio]="model.isLandscapeListingView | aspectRatio : gridRowSpan : gridColumn"
                 [sizes]="sizes"
                 [alt]="model.productName"
                 [minSize]="256"
                 [maxSize]="1080"
                 [priority]="priority()"
                 [decoding]="priority() ? 'auto' : 'async'"
                 [placeholder]="false"
                 fill>
        </df-image-wrapper>

        <!-- vimeo -->
        @if (primaryVideo) {
        <local-vimeo-player class="fill z-3"
                            [ngClass]="isVideoBuffered && isPlaying ? '' : 'opacity-0'"
                            [video-id]="primaryVideo"
                            [autopause]="false"
                            [background]="true"
                            [pauseOnInit]="true"
                            [loop]="false"
                            [controls]="false"
                            [use-custom-controls]="true"
                            [playerIdentifier]="playerIdentifier"
                            (onPlayerInit)="onPlayerInit($event)"
                            (onVideoEnd)="onVideoEnd()"
                            (onBufferEnd)="onBufferEnd()" />
        }
        }
    </a>

    <!-- slider -->
    @if (canShowCarousel) {
    <carousel class="fill z-1 _carousel"
              [loop]="true"
              [speed]="1000"
              [navigation]="{
                            nextEl: '.product-carousel-next-' + model.id,
                            prevEl: '.product-carousel-prev-' + model.id
                        }"
              [pagination]="{
                            el: '.product-carousel-pagination-' + model.id
                  }"
              [allowTouchMove]="(!isAccountPage || app.client.isTouchy) && (app.client.isTouchy || sliderImages.length > 1)"
              [allowSlideNext]="(!isAccountPage || app.client.isTouchy) && (app.client.isTouchy || sliderImages.length > 1)"
              [allowSlidePrev]="(!isAccountPage || app.client.isTouchy) && (app.client.isTouchy || sliderImages.length > 1)"
              [mousewheel]="{
                    enabled: true,
                    forceToAxis: true,
                  }"
              #carouselRef>
        @for (item of sliderImages; track $index) {
        <ng-template carouselSlide
                     let-data>
            <a class="w-12 height-100 ratio-product-image"
               [class.bg-col-33]="!$first"
               [routerLink]="model.data?.url"
               [fragment]="urlFragment"
               [gtmTrackSelectItem]="gtmTrackSelectItem"
               [gtmTrackSelectItemOff]="app.client.isCms"
               [klevuTrackCategoryProductClick]="klevuCategoryProductTrackClick"
               [klevuTrackRecommendationsProductClick]="klevuRecommendationsProductClickData"
               [klevuTrackClick]="klevuSearchTrackClick">
                <!-- out of stock overlay-->
                @if (wishlistCarouselItem && model.isOutOfStock) {
                <div class="fill bg-col-w z-3"
                     style="opacity:0.7"></div>
                }
                <!-- img -->
                <img class="z-1"
                     [dfImage]="item.image"
                     [ratio]="model.isLandscapeListingView | aspectRatio : gridRowSpan : gridColumn"
                     [sizes]="sizes"
                     [minSize]="256"
                     [maxSize]="1080"
                     [alt]="model.productName"
                     fill
                     draggable="false"
                     decoding="async">

                <!-- vimeo -->
                @if (item.video) {
                <local-vimeo-player class="fill z-2"
                                    [ngClass]="isVideoBuffered && isPlaying ? '' : 'opacity-0'"
                                    [video-id]="item.video"
                                    [autopause]="false"
                                    [background]="true"
                                    [pauseOnInit]="true"
                                    [loop]="false"
                                    [controls]="false"
                                    [use-custom-controls]="true"
                                    [playerIdentifier]="playerIdentifier"
                                    (onPlayerInit)="onPlayerInit($event)"
                                    (onVideoEnd)="onVideoEnd()"
                                    (onBufferEnd)="onBufferEnd()" />
                }
            </a>
        </ng-template>
        }
        <div slot="container-end">
            @if (!wishlistCarouselItem && sliderImages.length > 2 && !this.client.isS) {
            <div class="carousel-nav">
                <button class="m-r-2 p-a-2 carousel-prev"
                        [ngClass]="'product-carousel-prev-' + model.id"
                        type="button"
                        aria-label="Previous slide">
                    <i class="col-1 fs-3 icon-wrap icon-chevron-left"
                       aria-hidden="true"></i>
                </button>
                <button class="p-a-2 carousel-next"
                        [ngClass]="'product-carousel-next-' + model.id"
                        type="button"
                        aria-label="Next slide">
                    <i class="col-1 fs-3 icon-wrap icon-chevron-right"
                       aria-hidden="true"></i>
                </button>
            </div>
            }
            <carousel-dots [carouselRef]="carouselRef"
                           [paginationClass]="'product-carousel-pagination-' + model.id" />
        </div>
    </carousel>
    } @else if (!disableCarousel && sliderImages.length > 1) {
    <!-- fake dots -->
    <div class="_carousel">
        <div class="carousel-dots"
             [style.--carousel-dots-number]="sliderImages.length">
            <div class="carousel-dots-inner">
                @for (bullet of sliderImages; track $index) {
                <button class="carousel-dot"
                        [class.is-active]="!$index"
                        type="button"
                        [attr.aria-label]="'Slide %' | translate : ($index + 1)"></button>
                }
                <div class="carousel-dots-underbar"></div>
            </div>
        </div>
    </div>
    }
    <ng-container *abIf="'(quickA2B) A'">
        <product-listing-add-to-basket [product]="model"
                                       [buyRequest]="request"
                                       [canAdd]="canShowAddToBasket"
                                       [listPosition]="listPosition"
                                       [color]="currentColor"
                                       [gtmTrackClick]="{category: 'PLP', action: 'Quick Add button click', label: model.data.name}"
                                       [context]="wishlistCarouselItem ? 'wishlist' : undefined" />
    </ng-container>
    <product-listing-wishlist [product]="model"
                              [color]="currentColor"
                              [listPosition]="listPosition" />
</div>
<ng-container *abIf="'(quickA2B) B'">
    <product-listing-add-to-basket-exp class="block"
                                       [listingSize]="listingSize"
                                       [product]="model"
                                       [configurableSelection]="configurableSelection"
                                       [wishlistItem]="wishlistItem"
                                       [buyRequest]="request"
                                       [canAdd]="canShowAddToBasket"
                                       [listPosition]="listPosition"
                                       [color]="currentColor"
                                       [gtmTrackClick]="{category: 'PLP', action: 'Quick Add button click', label: model.data.name}"
                                       [context]="wishlistCarouselItem ? 'wishlist' : undefined" />
</ng-container>
<div class="pos-relative flex flex-justify-between flex-grow _bottom-section p-t-1"
     cy-listingProductName
     [class.no-label]="!shortLabel || hideLabel">
    <div class="flex p-r-1 pos-relative _bottom-section-inner">
        <div class="w-12 flex-column">
            <a class="flex-column flex-grow left"
               [routerLink]="model.data?.url"
               [fragment]="urlFragment"
               [gtmTrackSelectItem]="gtmTrackSelectItem"
               [gtmTrackSelectItemOff]="app.client.isCms"
               [klevuTrackCategoryProductClick]="klevuCategoryProductTrackClick"
               [klevuTrackRecommendationsProductClick]="klevuRecommendationsProductClickData"
               [klevuTrackClick]="klevuSearchTrackClick">
                <!-- name & price -->
                <div class="col-1 p1 p-b-1"
                     [class.ng-hide]="hideName && hidePrice">

                    <!-- name -->
                    <div *abIf="'(plpProductName) A'"
                         [class.ng-hide]="hideName">{{model.productName}}</div>
                    <div *abIf="'(plpProductName) B'"
                         [class.ng-hide]="hideName"
                         class="_product-name">{{model.productName}}</div>

                    <!-- price -->
                    <div [class.ng-hide]="hidePrice">
                        @if (currentOrgPrice) {
                        <price class="p-r-1 inline-block line-through col-13 price"
                               [class.hidden]="hidePrice"
                               [sizeClass]="'XL: p1, MS:p2'"
                               [value]="currentOrgPrice"></price>
                        }
                        <price class="inline-block price fw-bold"
                               [value]="currentPrice"></price>
                    </div>
                </div>
                @if (model.isOutOfStock && wishlistCarouselItem && !emailMeWhenInStock) {
                <p class="col-2"
                   [sizeClass]="'XL: p1, MS:p2'">{{'Out of stock' | translate}}</p>
                }
                <div>
                    @if (colors) {
                    @if (!isAccountPage && !isSearchOutlet) {
                    <div class="flex flex-middle">
                        <div class="flex flex-middle flex-wrap _colors">
                            @for (color of colors.slice(0, 3); track $index) {
                            <swatch class="m-r-1 m-t-1-s m-t-1-m inline-block"
                                    [color]="color"
                                    [selected]="currentColor === color"
                                    (mouseenter)="setColourWithDelay(color)"
                                    (click)="currentColor = color" />
                            }
                        </div>
                        @if (colors.length > 3) {
                        <div class="flex flex-wrap _colors">
                            <button class="m-t-1-s m-t-1-m">+</button>
                        </div>
                        }
                    </div>
                    }
                    }
                    <ng-container *abIf="'(productReviews) B'">
                        @if (model.reviewsCount > 0) {
                        <div class="m-t-1 p-b-1 flex flex-middle">
                            <rating class="col-12 flex flex-middle"
                                    [value]="model.reviewsRating" />
                            <p class="col-12"
                               [sizeClass]="'XL: p1, MS:p2'">
                                ({{model.reviewsCount}})
                            </p>
                        </div>
                        }
                    </ng-container>
                </div>
            </a>
            <!-- label -->
            @if (!hideLabel && shortLabel) {
            <div class="c1 ta-left uppercase col-12"
                 [style.color]="labelColor || null"
                 [htmlBinding]="shortLabel">
            </div>
            }
        </div>
        <!-- carousel wishlist-->
        @if (wishlistCarouselItem) {
        <wishlist-carousel-remove class="inline-block p-a-1"
                                  [product]="model" />
        }
    </div>
    <ng-container *abIf="'(quickA2B) A'">
        <ng-container *ifBrowser>
            @if (!wishlistCarouselItem) {
            @if (!!wishlistItem) {
            <wishlist-remove [item]="wishlistItem"
                             [wrapButton]="true" />
            } @else {
            <product-listing-wishlist-toggle class="_on-product"
                                             [product]="model"
                                             [listPosition]="listPosition"
                                             [selection]="configurableSelection" />
            }
            }
        </ng-container>
    </ng-container>
</div>
}
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IAttributeConfDataOption } from '@df/catalog-ui/product/configurable-options/product-configurable-options.component.def';
import { IProductDataV2 } from '@df/catalog/product/product';
import { Wishlist } from '@df/session/api/wishlist/wishlist';
import { GridModule } from '@df/ui/atomic/atom/grid/grid.module';
import { DragScrollDirective } from '@df/ui/common/drag-scroll.directive';
import { ReplacePipe } from '@df/ui/common/replace.pipe';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ProductDecorator } from '../../../../../../catalog/model/product/product.decorator';
import { ActionComponent } from '../../../../../../ui/api/action.component';
import { ResultComponent } from '../../../../../../ui/api/result.component';
import { TrayComponent } from '../../../../../../ui/widget/tray/tray.component';
import { WishlistStatus } from '../../../../wishlist/wishlist.interface';
import { PRODUCT_CLOSE_PREVIEW_EVENT_NAME, PRODUCT_WISHLIST_OPEN_PREVIEW_EVENT_NAME, ProductEventData } from '../../../product.abstract';
import { ProductListingConfigurableOptionsAbstract } from '../../configurable-options/product-listing-configurable-options.abstract';

@Component({
    selector: 'product-listing-wishlist-configurable-options',
    templateUrl: './product-listing-wishlist-configurable-options.component.html',
    styleUrl: './product-listing-wishlist-configurable-options.component.scss',
    standalone: true,
    imports: [ActionComponent, CommonModule, DragScrollDirective, GridModule, ReplacePipe, ResultComponent, TranslatePipe, TrayComponent],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductListingWishlistConfigurableOptionsComponent extends ProductListingConfigurableOptionsAbstract {
    override ngOnInit(): void {
        super.ngOnInit();

        this.subscriptions.add(
            this.dfEventService.success(event => {
                if (this.productService.canTriggerEvent(event, this.product, this.listPosition)) {
                    if (event.id === PRODUCT_WISHLIST_OPEN_PREVIEW_EVENT_NAME) {
                        this.onOpenEvent(event.data);
                    }

                    if (event.id === PRODUCT_CLOSE_PREVIEW_EVENT_NAME) {
                        this.onCloseEvent(event.data);
                    }
                }
            })
        );
    }

    get wishlist(): Wishlist {
        return this.app.customer.wishlist;
    }

    override readyToOpen(): void {
        this.onOpenEvent();
    }

    add(option: IAttributeConfDataOption) {
        // do nothing while busy
        if (this.status.busy) {
            return;
        }

        this.wishlist.toggle(this.status, option.product.id, undefined, () => {
            this.dfEventService.dispatch({
                id: PRODUCT_WISHLIST_OPEN_PREVIEW_EVENT_NAME,
                data: {
                    product: this.product as ProductDecorator<IProductDataV2>,
                    index: this.listPosition,
                    option,
                    status: WishlistStatus.AddSuccess
                }
            });
        });
    }

    protected override onOpenEvent(data?: ProductEventData) {
        this.onDataChange(false);

        if (this.client.isS || this.client.isM) {
            if (data?.status === WishlistStatus.AddSuccess) {
                this.closeTray();
            } else {
                this.openTray(this.trayModalContentTemplateRef);
            }
        }
    }

    protected override onTrayClose(): void {
        // do nothing
    }
}

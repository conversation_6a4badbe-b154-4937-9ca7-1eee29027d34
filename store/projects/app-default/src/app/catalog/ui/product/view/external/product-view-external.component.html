<!-- product header -->
<div class="w-12 flex flex-column-m flex-column-s">
    <div class="w-8 w-12-m w-12-s">
        <div class="pos-relative"
             [ngClass]="'photoswipe-galleryuid-' + parent.id"
             [grid]="2"
             [gridColumnGap]="'2px'">
            @for (item of images; track $index) {
            <product-view-media-item class="overflow-hidden"
                                     [ngClass]="item.ratio"
                                     [class.ng-hide]="$index > 1"
                                     [gridColumn]="item.colspan"
                                     [item]="item"
                                     [priority]="$index <= 1"
                                     [product]="product"
                                     [parent]="parent"
                                     [photoswipeUid]="item.image + '.' + parent.id + '.0.' + $index"
                                     [photoswipeOptions]="{
                                                    index: '0.' + $index,
                                                    galleryUID: parent.id
                                                }" />

            } @empty {
            <div [grid]="2"
                 [gridColumnGap]="'2px'">
                <div class="overflow-hidden ratio-4-5">
                    <shell />
                </div>
                <div class="overflow-hidden ratio-4-5">
                    <shell />
                </div>
            </div>
            }
            @if (client.sizeId > 1) {
            <button class="pos-absolute right-4 bottom-4 z-10 button-2 semi-transparent-pane p-l-2-x p-r-2-x p-l-2-l p-r-2-l p-l-2-m p-r-2-m p-l-2-s p-r-2-s no-wrap"
                    [photoswipe]="images[0].image"
                    [photoswipeUid]="images[0].image + '.' + parent.id + '.0.0'"
                    [photoswipeOptions]="{
                                index: '0.0',
                                galleryUID: parent.id,
                                canPreload: true,
                                loadAfterOpen: false,
                                imageOptions: {
                                    width: 4096,
                                    height: 4096
                                }
                            }">
                <span>{{'View more images' | translate}}</span>
            </button>
            } @else {
            <button class="pos-absolute right-2 bottom-2 z-10 button-2 semi-transparent-pane p-l-2-x p-r-2-x p-l-2-l p-r-2-l p-l-2-m p-r-2-m p-l-2-s p-r-2-s no-wrap"
                    (click)="closeExternal()">
                <span>{{'View more details' | translate}}</span>
            </button>

            <!-- wishlist warning -->
            <product-view-wishlist class="pos-absolute top-0 w-12 z-9"
                                   [product]="product"
                                   [color]="product.data.color" />
            }
        </div>
    </div>

    <!-- product details / !S -->
    @if (client.sizeId > 1) {
    <product-view-details class="pos-relative w-4"
                          [product]="product"
                          [parentView]="this"
                          [content]="productLayoutContent || content"
                          [parent]="parent" />
    }
    @if (client.isS) {
    @if (parent.isConfigurable && !parent.hasLetterOption) {
    <product-configurable-options-swatch class="wrap left ng-hide"
                                         [product]="product"
                                         [parentView]="this"
                                         [preselectOptions]="['color']"
                                         [preselectSingles]="true"
                                         [storage]="true"
                                         (init)="handleInitChange($event.data)"
                                         (selectionChange)="handleSelectionChange($event.data)"
                                         (dataChange)="handleDataChange($event.data)" />
    }
    @if (parent.isBundle) {
    <product-bundle-options [product]="product"
                            [selection]="productView.bundleSelection"
                            [buyRequest]="request" />
    }
    <div class="wrap p-b p-t form-size-1 pos-relative bg-col-w">
        <product-view-name class="w-12"
                           [class.p-t-2]="hasSwatchTypeAttributes"
                           [product]="product"
                           [parentView]="this"
                           [parent]="parent"
                           [content]="productLayoutContent || content"
                           [showKlarna]="showKlarna" />
        @if (showCountdownBanner) {
        <countdown-banner />
        }
        <add-to-basket class="block"
                       [product]="product"
                       [parentView]="this"
                       [buyRequest]="request"
                       [selection]="configurableSelection"
                       [changeText]="true"
                       [showExternal]="true" />
    </div>
    }
</div>
@if (content?.bottomComponents) {
<icms-outlet [components]="content.bottomComponents"
             [name]="'Bottom section outlet'" />
}

<product-view-lightbox [product]="product"
                       [nativeElement]="nativeElement" />

<product-view-sidebar [product]="product"
                      [parentView]="this"
                      [parent]="parent"
                      [content]="productLayoutContent || content" />

<product-view-tray [product]="product"
                   [parentView]="this" />
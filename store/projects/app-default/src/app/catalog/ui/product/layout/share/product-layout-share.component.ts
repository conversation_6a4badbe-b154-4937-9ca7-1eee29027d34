import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import type { IProductDataV3 } from '@df/catalog/product/product';
import { UiComponent } from '@df/ui/ui.component';
import type { ProductDecorator } from '../../../../model/product/product.decorator';
import { ProductSocialShareComponent } from '../../social-share/product-social-share.component';

@Component({
    selector: 'product-layout-share',
    templateUrl: './product-layout-share.component.html',
    host: {
        '[class]':
            '"flex flex-justify-between flex-middle p-t-2 p-t-3-m p-t-3-s p-r-4-m p-r-4-s p-b-3-m p-b-3-s p-l-4-m p-l-4-s b-t b-col-5 col-13 s2"'
    },
    imports: [ProductSocialShareComponent],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductLayoutShareComponent extends UiComponent {
    readonly product = input.required<ProductDecorator<IProductDataV3>>();
}

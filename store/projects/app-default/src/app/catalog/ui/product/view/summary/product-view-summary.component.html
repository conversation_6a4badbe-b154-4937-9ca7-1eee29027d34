<div class="wrap-m">
    @for (item of parentView?.sidebars; track $index) {
    <div class="flex flex-justify-between flex-middle p-t-2 p-t-4-s p-b-4-s b-b-s cursor-pointer product-details__item"
         [class.b-t-s]="$first"
         (click)="parentView.openSidebar(item.sidebar)">
        <p class="m-l-4-s p-b-1 p-a-0-s"
           [sizeClass]="'!S:s2,S:s1'">{{item.title}}</p>
        @if (client.isS) {
        <i class="m-r-3 col-12 icon-right"
           aria-hidden="true"></i>
        } @else {
        <button class="m-r-3 button-inline has-icon is-visible"
                type="button"></button>
        }
    </div>
    @if (client.isPrerender) {
    @switch (item.sidebar) {
    @case ('details') {
    <product-general-details class="ng-hide"
                             [parentView]="parentView"
                             [content]="content" />
    }
    @case ('delivery') {
    <product-delivery-price class="ng-hide"
                            [productId]="product.id"
                            [content]="{
                              returnNote: content.returnNote,
                              returnLink: content.returnLink,
                              returnLinkText: content.returnLinkText
                           }" />
    }
    }
    }
    }
    <!-- gift boxing -->
    @if (parent?.data?.allow_gift_box && parentView.isGiftBoxesEnabled) {
    <div class="flex flex-justify-between flex-middle p-t-2 p-t-4-s p-b-4-s b-b-s cursor-pointer product-details__item"
         (click)="parentView.openSidebar('gift-boxing')">
        <p class="m-l-4-s p-b-1 p-a-0-s"
           [sizeClass]="'!S:s2,S:s1'">{{'Gift boxing available' | translate}}</p>
        @if (client.isS) {
        <i class="m-r-3 col-12 icon-right"
           aria-hidden="true"></i>
        } @else {
        <button class="m-r-3 button-inline has-icon is-visible"
                type="button"></button>
        }
    </div>
    @if (client.isPrerender) {
    <product-view-gift-boxing class="ng-hide" />
    }
    }
    <!-- reviews -->
    <product-view-reviews [product]="product"
                          [parent]="parent"
                          [parentView]="parentView"
                          [top]="false" />
</div>
<!-- desktop layout  -->
@if (client.isL || client.isX) {
<div class="w-12 center no-wrap overflow-hidden _options"
     [class.ng-hide]="status.busy"
     dragScroll
     [dragscrollFade]="false">
    <div class="flex flex-middle flex-justify-center">
        @for (option of sizeOptions; track option.value) {
        @if (option.product) {
        <action class="button-1"
                [status]="getStatus(option)"
                (click)="buy(option)">
            <span class="no-wrap _option"
                  [class.is-padding]="option.padding">
                <span [ngClass]="!option.salable ? 'line-through col-13' : ''">
                    @if (!hidePrefix) {
                    {{option.label}}
                    } @else {
                    {{option.label | replace : pattern : ''}}
                    }
                </span>
            </span>
        </action>
        }
        }
    </div>
</div>

<result class="fill z-3 center"
        [customClasses]="'col-12 p2'"
        [duration]="10"
        [success]="'Added' | translate"
        [status]="status" />
}

<!-- overlay -->
<ng-template #trayModalContentTemplateRef>
    <tray [static]="true"
          [open]="70">
        <div class="wrap p-b-5">
            <div class="flex flex-middle flex-justify-between m-t-1 p-t-2 p-b-2 p-b-3-s">
                <div class="flex-grow flex p2">
                    {{'Select Size:' | translate}}
                    @if (processedOption?.product) {
                    <ng-template [productStatus]="processedOption.product"
                                 let-status="status">
                        @if (!status.isSalable) {
                        <span class="col-2 p-l-1 uppercase">{{'Out of stock' | translate}}</span>
                        }
                        @if (status.isLowQty) {
                        <span class="p-l-1">{{'Only % remaining' | translate : status.maxQty}}</span>
                        }
                    </ng-template>
                    }
                </div>
                @if (product?.hasSizeGuide) {
                <button class="m-r-1 b-b b-col-1 c1"
                        type="button"
                        (click)="openSizeGuide()">
                    <span>{{'Size guide' | translate}}</span>
                </button>
                }
            </div>

            <div class="p-b"
                 [grid]="[4, 4, sizeOptions.length <= 3 ? 3 : 7, sizeOptions.length <= 3 ? 3 : 7][client.sizeId]"
                 [gridColumnGap]="[8,8,2,2][client.sizeId]"
                 [gridRowGap]="[4,4,2,2][client.sizeId]">
                <!-- attribute options -->
                @for (option of sizeOptions; track option.value) {
                <label class="w-12 p-t-1 p-t-2-m p-t-2-s p-r-1 p-r-0-s p-b-1 p-b-2-m p-b-2-s p-l-1 p-l-0-s b-a b-radius-max fs-4 lh-1 center pe-auto-s cursor-pointer _option"
                       [class.is-picked]="isPicked(sizeAttribute, option)"
                       [class.is-disabled]="!option.salable"
                       (click)="pick(sizeAttribute, option)">{{option.label}}</label>
                }
            </div>

            <action class="w-12 m-t button left"
                    cy-basketAddButton
                    [status]="status"
                    (click)="handleClick()">
                @if (!processedOption || (hasSizeOption && !data?.['size'])) {
                @if (hasSizeOption && hasLetterOption) {
                {{'Select size & letter' | translate}}
                } @else if (hasSizeOption && !hasLetterOption) {
                {{'Select size' | translate}}
                } @else if (!hasSizeOption && hasLetterOption) {
                {{'Select letter' | translate}}
                }
                } @else {
                @if (processedOption?.salable) {
                @if (!status.busy) {
                {{'Add to bag' | translate}}
                } @else {
                {{'Adding...' | translate}}
                }
                } @else {
                <div class="w-12 flex flex-middle flex-justify-center">
                    <span>{{'Email me when back in stock' | translate}}</span>
                    <i class="icon-email fs-9 m-t-1"
                       aria-hidden="true"></i>
                </div>
                }
                }
            </action>

            <result class="block s2"
                    [status]="status" />
        </div>
    </tray>
</ng-template>
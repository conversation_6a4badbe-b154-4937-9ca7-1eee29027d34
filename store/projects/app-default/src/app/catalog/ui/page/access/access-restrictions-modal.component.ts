import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnDestroy, OnInit, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { IRequestCallback } from '@df/api/request/request.service';
import { SessionStorageService } from '@df/browser/storage/session-storage.service';
import { CustomerModel } from '@df/session/api/customer.model';
import { LoginFormComponentDef } from '@df/session/login/login-form.component.def';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { BodyClassService } from '@df/ui/body-class.service';
import { DfModal } from '@df/ui/modal/df-modal.decorator';
import { NDfModal } from '@df/ui/modal/df-modal.interface';
import { TModalPosition } from '@df/ui/modal/modal-config';
import { ModalRef } from '@df/ui/modal/modal-ref';
import { MODAL_DATA } from '@df/ui/modal/modal.service';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { NICmsContentComponent } from '@icms/core/content';
import { ICmsDataChangeService, NICmsDataChange } from '@icms/core/data/change/icms-data-change.service';
import { isCustomerAllowed } from '../../../../customer/groups/customer-groups';
import { InputWrapBodyDirective } from '../../../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../../../ui/form/input-wrap.component';
import { MuiInputDirective } from '../../../../ui/form/mui-input.directive';
import { ValidatePatternAsyncDirective } from '../../../../ui/form/validate/validate-pattern-acync.directive';
import { ViewPasswordComponent } from '../../../../ui/form/view-password.component';
import { IPageConfig } from '../../../config/page.interface';
import { AccessRestrictionsModalInitiatorService } from './access-restrictions-modal-initiator.service';
import { NAccessRestrictionsModal } from './access-restrictions-modal.interface';
import { ActionComponent } from '../../../../ui/api/action.component';
import { ResultComponent } from '../../../../ui/api/result.component';

declare module '@df/catalog/model' {
    interface IModelDataV3WithUrl {
        typeData?: IPageConfig;
    }
}

export const ACCESS_RESTRICTIONS_POPUP_OPEN_BODY_CLASS = 'access-restriction-modal-open';

@DfModal({
    id: NAccessRestrictionsModal.ID,
    position: 'custom' as TModalPosition,
    config: sizeName => ({
        minWidth: sizeName === 'S' ? undefined : '478px',
        width: sizeName === 'S' ? undefined : '478px',
        maxWidth: sizeName === 'S' ? undefined : '478px',
        modalClass: 'p-a-0 p-l-4-m p-l-4-s p-r-4-m p-r-4-s p-b-0-s p-t-0-s',
        hasBackdrop: false,
        scrollStrategy: 'block'
    }),
    reopenPolicy: NDfModal.ReopenPolicy.wait,
    sizeRestriction: ['S', 'M', 'L', 'X'],
    priority: 20,
    blockGroup: 'modals',
    forbiddenLocation: /^\/(login|checkout)$/,
    initiators: [AccessRestrictionsModalInitiatorService]
})
@Component({
    selector: 'access-restrictions-modal',
    templateUrl: './access-restrictions-modal.component.html',
    styleUrl: './access-restrictions-modal.component.scss',
    standalone: true,
    imports: [
        ActionComponent,
        CommonModule,
        FormsModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        MuiInputDirective,
        ResultComponent,
        SizeClassDirective,
        ValidatePatternAsyncDirective,
        ViewPasswordComponent,
        TranslatePipe
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AccessRestrictionsModalComponent extends LoginFormComponentDef implements OnInit, OnDestroy {
    protected modalData = inject(MODAL_DATA);
    router = inject(Router);
    protected bodyClassService = inject(BodyClassService);
    protected customerModel = inject(CustomerModel);
    protected sessionStorageService = inject(SessionStorageService);
    protected iCmsDataChangeService = inject(ICmsDataChangeService);
    component!: NICmsContentComponent.IData;
    protected modalRef = inject(ModalRef);

    blurStrength: string = '0px';

    get cmsObservedData(): string[] {
        const keys = this.component ? Object.keys(this.component) : [];

        return ['data', ...keys];
    }

    get configData() {
        return this.app.meta.model?.data?.typeData;
    }

    override ngOnInit(): void {
        super.ngOnInit();

        this.updateBlurStrength();

        this.subscriptions.add(
            this.iCmsDataChangeService.afterChange$.subscribe(event => {
                if (
                    event.changeType === NICmsDataChange.EChangeType.change &&
                    this.cmsObservedData.some(path => event.valuePath.includes(path))
                ) {
                    this.onCmsUpdate();
                }
            })
        );
    }

    override login(onSuccess?: IRequestCallback, onError?: IRequestCallback) {
        return super.login(() => {
            if (Array.isArray(onSuccess)) {
                onSuccess.forEach(callback => callback());
            } else if (onSuccess) {
                onSuccess();
            }
            if (isCustomerAllowed(this.modalData?.visibility || this.configData?.visibility, this.customerModel.data.group_id)) {
                this.closeModal();
            }
        }, onError);
    }

    protected onCmsUpdate(): void {
        this.detectChanges();
    }

    navigateToLogin() {
        const currentUrl = this.router.url;
        this.sessionStorageService.set('returnUrl', currentUrl);
        this.router.navigate(['/login']);
    }

    preventClose(event: MouseEvent) {
        event.stopPropagation();
    }

    updateBlurStrength() {
        const blurValue = this.configData?.blurStrength || this.modalData?.blurStrength || 0;
        this.blurStrength = `${blurValue}px`;
    }

    closeModal() {
        this.modalRef.close();
        this.bodyClassService.remove(ACCESS_RESTRICTIONS_POPUP_OPEN_BODY_CLASS, this);
    }

    override ngOnDestroy() {
        super.ngOnDestroy();
        this.modalRef.close();
    }
}

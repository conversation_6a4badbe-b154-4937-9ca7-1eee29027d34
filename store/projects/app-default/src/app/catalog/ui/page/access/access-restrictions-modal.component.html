@if (!customer.model.isLoggedIn) {
<div class="wrap p-t p-t-6-s p-l-6-s p-r-6-s p-b-8-m p-b-8-s p-b bg-col-w b-radius-5 pos-relative modal-content">
    <h1 [sizeClass]="'SM: h2-bold, XL: h1-bold'"
        [innerHtml]="configData?.restrictionTitle || modalData?.restrictionTitle"></h1>
    <p class="p1 m-t-1"
       [innerHtml]="configData?.restrictionSubtitle || modalData.restrictionSubtitle"></p>
    <form class="flex-column-m flex-column-s flex-column-reverse-m flex-column-reverse-s"
          method="POST"
          [attr.name]="'login'"
          [attr.sl-form]="'login'"
          #form="ngForm">
        <div class="w-12 m-r-2-x m-r-2-l m-b-0 b-radius-5"
             id="login-form">
            <input-wrap class="w-12 pos-relative"
                        [messages]="{
                      email: ('Valid email is required' | translate),
                      required: ('Valid email is required' | translate)}"
                        [canShowValidate]="email?.length > 0 && (isChecked() || form.submitAttempt)">
                <input class="input"
                       name="email"
                       type="email"
                       autocomplete="email"
                       required
                       sl-input="email"
                       cy-loginMailInput
                       validatePatternAsync="email"
                       [muiInput]="'Email address' | translate"
                       [muiInputValidateOnSubmit]="true"
                       [(ngModel)]="email"
                       [ngModelOptions]="{updateOn: 'blur'}"
                       (keyup.enter)="signUpEmailInput.blur()"
                       #signUpEmailInput>
                @if (checkStatus.busy) {
                <span class="pos-absolute top-3 right-3 bottom-0 flex flex-middle flex-justify-center pe-none">
                    <i class="icon-loading"
                       aria-hidden="true"></i>
                </span>
                }
            </input-wrap>
            <input-wrap class="w-12 pos-relative async-validation"
                        [class.has-validation]=" !passwordStatus.busy && form.submitAttempt"
                        [canShowValidate]="passwordRef?.value?.length > 0 && !passwordStatus.busy && form.submitAttempt">
                <view-password>
                    <input class="input"
                           type="password"
                           name="password"
                           autocomplete="off"
                           required
                           [attr.placeholder]="'Enter password' | translate"
                           [muiInput]="'Password' | translate"
                           [(ngModel)]="data.password"
                           (keyup.enter)="login()"
                           #passwordRef>
                </view-password>
            </input-wrap>

            @if (hasAccount()) {
            <div class="m-t-2 m-t-3-m m-t-3-s m-b-s">
                <action class="button-inline"
                        sl-button="reset"
                        type="button"
                        [action]="passwordStatus"
                        (click)="resetPassword()">
                    <span class="s2">{{'Forgotten your password?' | translate}}</span>
                </action>
                <result class="m-t-1 block"
                        sl-result="reset"
                        [status]="passwordStatus"
                        [success]="'We\'ve sent you an email with a link to reset your password.' | translate" />
            </div>
            }
            <div class="m-t-3">
                <button class="button w-12"
                        sl-button="continue"
                        cy-loginSubmit
                        [action]="loginStatus"
                        (click)="login()">
                    <span>{{'Sign in' | translate}}</span>
                </button>
                <result class="m-t-1 block"
                        sl-result="continue"
                        cy-loginResult
                        [status]="loginStatus" />
            </div>
        </div>
    </form>
    @if (configData?.showAccountCreationLink || modalData?.showAccountCreationLink) {
    <div class="s2 center m-t-1 cursor-pointer">Don’t have an account? Create one
        <span class="underline"
              (click)="navigateToLogin()">
            {{'here' | translate}}
        </span>
    </div>
    }
</div>
} @else {
<div
     class="wrap p-t-5 p-b-5 p-l-6-m p-l-6-s p-r-6-m p-r-6-s p-t-7-m p-t-7-s p-b-7-m p-b-7-s b-radius-5 bg-col-w pos-relative modal-content">
    <div [sizeClass]="'SM: h2-bold, XL: h1-bold'"
         [innerHtml]="configData?.noAccessTitle || modalData?.noAccessTitle"></div>
    <div class="p1 m-t-1"
         [innerHtml]="configData?.noAccessSubtitle || modalData?.noAccessSubtitle"></div>

</div>
}
@if (!app.client.isCms) {
<div class="_overlay"
     (click)="preventClose($event)"
     [style.--blur-strength]="blurStrength">
</div>
}
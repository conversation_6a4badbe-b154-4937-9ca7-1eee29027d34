<div class="wrap-x wrap-l wrap-m center-x center-l p-t-9 p-b-9 p-t-4-s p-b-4-s">
    <div class="c-11-x c-10-l c-12-m c-12-s m-b-0 m-r-0">
        <div class="p-r-5-x p-l-5-x m-r--4-l m-l--4-l p-l-4-m p-r-4-m left">
            <!--Columns-->
            @for (item of component.columns; track $index) {
            <href [link]="item.link">
                <div class="m-t-6 w-6 w-12-s p-r-0-s p-l-0-s"
                     [ngClass]="$first ? 'p-r-4' : 'p-l-4 p-l-2-l p-r-2-l m-t-20-s'">
                    <!-- title -->
                    <p class="m-b-2 m-b-8-s h2 center-s"
                       *iCms="'title' of item"></p>
                    <!-- image -->
                    @if (item.image) {
                    <img class="w-12"
                         [dfImage]="item.image"
                         [priority]="component.cwvLoading === 'eager' || component.cwvPriority"
                         [ratio]="'1-1'">
                    }
                    <!-- copy -->
                    <p class="p3 m-t-3 m-t-4-s col-12"
                       *iCms="'copy' of item; content: 'html'"></p>
                </div>
            </href>
            }
        </div>
    </div>
</div>
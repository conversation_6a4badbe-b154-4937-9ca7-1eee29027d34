import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { ToggleModule } from '@df/ui/toggle/toggle.module';
import { ToggleService } from '@df/ui/toggle/toggle.service';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { UiComponent } from '@df/ui/ui.component';
import { ICmsClientModule } from '@icms/client/icms-client.module';
import { ICmsComponent, ICmsContent, NICmsContentComponent, NICmsContentField } from '@icms/core/content';
import { ISizeGuideTable } from './size-guide-table.interfaces';

let uid = 0;

@ICmsComponent<PageComponentSizeGuideTableComponent>({
    id: 'size-guide-table',
    name: 'Size Guide Table',
    tags: ['size guide table', 'PDP'],
    fields: [
        <NICmsContentField.IField>{
            type: NICmsContentField.EFieldType.text,
            key: 'table_title',
            title: 'Table Title',
            default: 'This is example table title'
        },
        <NICmsContentField.IRepeaterField>{
            type: NICmsContentField.EFieldType.repeater,
            key: 'tables',
            title: 'Tables',
            itemName: 'table',
            itemNameField: 'tables',
            defaultSize: 1,
            maxSize: 2,
            required: true,
            fields: [
                <NICmsContentField.IField>{
                    type: NICmsContentField.EFieldType.toggle,
                    key: 'column_background',
                    title: 'Add first column background',
                    default: false
                },
                <NICmsContentField.IRepeaterField>{
                    type: NICmsContentField.EFieldType.repeater,
                    key: 'rows',
                    title: 'Table',
                    itemName: 'row',
                    itemNameField: 'rows',
                    defaultSize: 2,
                    required: true,
                    fields: [
                        <NICmsContentField.IRepeaterField>{
                            type: NICmsContentField.EFieldType.repeater,
                            key: 'columns',
                            title: 'Columns',
                            itemName: 'column',
                            defaultSize: 3,
                            fields: [
                                <NICmsContentField.IChoiceField>{
                                    key: 'width',
                                    type: NICmsContentField.EFieldType.choice,
                                    title: 'Column width',
                                    required: true,
                                    default: 'flex-span-1',
                                    options: [
                                        { key: 'flex-span-1', title: 'Regular' },
                                        { key: 'flex-span-2', title: 'Wide' }
                                    ]
                                },
                                <NICmsContentField.IChoiceField>{
                                    key: 'bold',
                                    type: NICmsContentField.EFieldType.choice,
                                    title: 'Font weight',
                                    required: true,
                                    default: 'fw-regular',
                                    options: [
                                        { key: 'fw-regular', title: 'Font regular' },
                                        { key: 'fw-bold', title: 'Font bold' }
                                    ]
                                },
                                <NICmsContentField.IChoiceField>{
                                    key: 'color',
                                    type: NICmsContentField.EFieldType.choice,
                                    title: 'Font color',
                                    required: true,
                                    default: 'col-11',
                                    options: [
                                        { key: 'col-11', title: 'Black' },
                                        { key: 'col-13', title: 'Gray' }
                                    ]
                                },
                                <NICmsContentField.IField>{
                                    type: NICmsContentField.EFieldType.text,
                                    key: 'value',
                                    title: 'Value',
                                    default: '1'
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
})
@Component({
    selector: 'page-component-size-guide-table',
    templateUrl: 'size-guide-table.component.html',
    standalone: true,
    imports: [CommonModule, ICmsClientModule, ToggleModule, TranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class PageComponentSizeGuideTableComponent extends UiComponent implements NICmsContentComponent.IComponent {
    @ICmsContent({
        type: 'component'
    })
    component!: ISizeGuideTable;

    private toggleService = inject(ToggleService);
    readonly uid = uid++;

    override ngOnInit(): void {
        super.ngOnInit();
        this.toggleService.open([this.uid, 0]);
    }
}

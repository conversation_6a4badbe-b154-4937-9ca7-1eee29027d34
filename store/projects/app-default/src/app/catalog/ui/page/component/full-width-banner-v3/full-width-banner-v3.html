<div class="wrap-x overflow-hidden animation-long"
     [class.animation-fade-in]="!client.isCms && component.animation"
     [gsap]
     [gsapOff]="client.isCms || !component.animation"
     [scrollTrigger]="{ start: '50% bottom', end: '100% top', toggleClass: 'is-animate', once: true }">
    <href [link]="component.link">
        <div class="pos-relative">
            <div class="fill z-1"
                 [ngClass]="[component.background_color || 'bg-col-21']"
                 [style.background]="component.background_color"
                 [gsap]="{ opacity: 0 }"
                 [gsapOff]="client.isCms || !component.animation"
                 [scrollTrigger]="{ id: 'opacity-bg', start: '60% center', end: '95% center' }">
            </div>

            <div class="w-12 pos-relative z-2 flex flex-column-s"
                 [class.flex-reverse]="component.reverse === 'right'"
                 [class.animation-fade-in]="!client.isCms && component.animation"
                 [gsap]
                 [gsapOff]="client.isCms || !component.animation"
                 [scrollTrigger]="{ start: '50% bottom', end: '100% top', toggleClass: 'is-animate', once: true }">
                <div class="w-6 w-12-s"
                     [gsap]="{ opacity: 0 }"
                     [gsapOff]="client.isCms || !component.animation"
                     [scrollTrigger]="{ id: 'opacity', start: '80% center', end: '120% center' }"
                     [ngClass]="setPadding(component)">
                    <div class="ratio-8-9 ratio-4-5-s m-b--4-s overflow-hidden">
                        @if (component.video_id && !component.image) {
                        <shell />
                        }
                        <div class="fill-s">
                            <div [sizeClass]="'S: ratio-4-5'">
                                @if (component.image) {
                                <img class="z-1"
                                     [dfImage]="component.image"
                                     [priority]="component.cwvLoading === 'eager' || component.cwvPriority"
                                     [ratio]="client.isS ? '4/5' : '8/9'"
                                     fill>
                                }
                                @if (component.video_id) {
                                <local-vimeo-player class="rf z-2 bg-col-w"
                                                    [video-id]="component.video_id"
                                                    [autoplay-in-viewport]="true"
                                                    [background]="component.video_background" />
                                }
                            </div>
                        </div>
                    </div>

                    @if (client.isS) {
                    <div class="w-12 m-t-10 center flex flex-justify-center flex-bottom"
                         [gsap]="{ opacity: 0 }"
                         [gsapOff]="client.isCms || !component.animation"
                         [scrollTrigger]="{ id: 'opacity-center', start: '25% center', end: '80% center' }">
                        <ng-container *ngTemplateOutlet="textoptions; context: {$implicit: component}" />
                    </div>
                    }
                </div>

                @if (client.sizeId>0) {
                <div class="w-6 ratio-8-9">
                    <div class="fill flex flex-justify-center flex-middle center"
                         [gsap]="{ opacity: 0 }"
                         [gsapOff]="client.isCms || !component.animation"
                         [scrollTrigger]="{ id: 'opacity', start: '80% center', end: '120% center' }">
                        <ng-container *ngTemplateOutlet="textoptions; context: {$implicit: component}" />
                    </div>
                </div>
                }

            </div>
        </div>
    </href>
</div>

<ng-template #textoptions
             let-component>
    <div class="w-6-x w-6-l w-11 m-b-7-s"
         [style.color]="component.text_color">
        <p [ngClass]="component.heading ? 'o1 m-b-1' : ''"
           *iCms="'heading' of component"></p>

        <h1 [ngClass]="[component.title_style || 'h1', component.title ? 'p-t-2-s m-b-3-s m-b-1 m-b-2-m' : '']"
            *iCms="'title' of component"></h1>

        <a [ngClass]="component.cta_style || 'p2'"
           [routerLink]="component.link">
            <span class="button__body"
                  *iCms="'cta_text' of component"></span>
        </a>
    </div>
</ng-template>
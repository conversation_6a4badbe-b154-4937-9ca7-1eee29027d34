@if (!!items?.length || client.isCms) {
<div class="wrap overflow-hidden local-model-carousel local-model-carousel-v2"
     [style.--carousel-slide-width]="client.isS ? 202 : 326"
     [gtmTrackViewItemList]="gtmTrackViewItemList"
     [gtmTrackViewItemListOff]="client.isCms">
    @let showArrows = !component.carousel_marquee && canLoop && showCarousel;
    @let showTitle = component.title || component.cta_text;

    <!-- title -->
    @if (showTitle || showArrows) {
    <div class="w-12 flex flex-justify-between p-t">
        <div class="flex flex-middle">
            <span class="h2"
                  [style.color]="component.title_color || null"
                  *iCms="'title' of component; content: 'html'; tag: component.title_tag || 'h2'; skipEmpty: true"></span>
            @if (component.title) {
            &nbsp;
            }
            <href [link]="component.cta_link">
                <span class="link-2 fs-3"
                      [style.color]="component.cta_color || null"
                      *iCms="'cta_text' of component; content: 'html'; skipEmpty: true"></span>
            </href>
        </div>
        <!--  slider control  -->
        @if (showArrows) {
        <div class="flex flex-middle m-r--2"
             [style.color]="component.title_color || null">
            <button class="flex flex-middle p-r-1 p-l-1 cursor-pointer"
                    [ngClass]="'model-carousel-prev-' + uid"
                    role="button"
                    [disabled]="!swiper?.allowSlidePrev"
                    [attr.aria-label]="'Previous slide' | translate"
                    (click)="swiper?.slidePrev()">
                <i class="fs-4 icon-chevron-left"
                   aria-hidden="true"></i>
            </button>
            <button class="flex flex-middle p-r-1 p-l-1 cursor-pointer"
                    [ngClass]="'model-carousel-next-' + uid"
                    role="button"
                    [disabled]="!swiper?.allowSlideNext"
                    [attr.aria-label]="'Next slide' | translate"
                    (click)="swiper?.slideNext()">
                <i class="fs-4 icon-chevron-right"
                   aria-hidden="true"></i>
            </button>
        </div>
        }
    </div>
    }
    @if (showCarousel) {
    <div class="pos-relative w-12 m-t">
        @if ((component.editorial_title || component.editorial_text) && !client.isS) {
        <div class="inline-block left local-model-carousel__content"
             [class.opacity-disabled]="component.carousel_loop || component.carousel_marquee">
            <ng-container *ngTemplateOutlet="editorialContentTpl; context: { $implicit: component }" />
        </div>
        }
        <custom-cursor class="inline-block local-model-carousel__carousel"
                       [color]="component.cursor_color"
                       [disabled]="client.isTouchy || touched || !canLoop">
            <ng-template #customCursorContent>
                <carousel class="w-12"
                          [class.swiper-overflow-visible]="(!component.carousel_loop && !component.carousel_marquee) || client.isS"
                          [slidesPerView]="'auto'"
                          [spaceBetween]="client.isS ? 2 : 4"
                          [freeMode]="true"
                          [loop]="canLoop"
                          [marquee]="{
                           enabled: !!component.carousel_marquee,
                           bounce: !canLoop,
                           speed: component.carousel_marquee_speed || 1
                           }"
                          [navigation]="canLoop ? {
                               nextEl: '.model-carousel-next-' + uid,
                               prevEl: '.model-carousel-prev-' + uid
                           } : false"
                          [nested]="!!component.carousel_nested"
                          [mousewheel]="{
                            enabled: true,
                            forceToAxis: true,
                          }"
                          (afterInit)="afterInitHandle($event)"
                          (touchStart)="touchStartHandle()"
                          (touchMove)="touchMoveHandle($event)"
                          (scrollbarDragMove)="touchMoveHandle($event)"
                          (activeIndexChange)="activeIndexChangeHandle($event)"
                          (marquee)="touchMoveHandle($any($event))"
                          #carouselRef>
                    @for (item of items; track $index) {
                    <ng-template class="w-12 flex"
                                 carouselSlide
                                 let-data>
                        @if (!!item.model?.type || component.variant === 'custom') {
                        @switch (type) {
                        @case ('product') {
                        <product class="w-12 bg-col-w"
                                 [model]="item.model"
                                 [modelView]="item.modelView"
                                 [altView]="item.altView"
                                 [forceLoad]="true"
                                 [disableCarousel]="true"
                                 [hidePrice]="component.product_hide_price"
                                 [hideName]="component.product_hide_name"
                                 [hideLabel]="component.product_hide_label"
                                 [hideVideo]="component.product_hide_video"
                                 [wishlistSmallMessage]="client.isS || client.isM"
                                 [listPosition]="$index"
                                 [listId]="listId"
                                 [listName]="listName" />
                        }
                        @case ('category') {
                        <category class="w-12 bg-col-w"
                                  [model]="item.model" />
                        }
                        @case ('page') {
                        <cms-blog-post class="w-12 bg-col-w"
                                       [model]="item.model" />
                        }
                        @case ('custom') {
                        <div class="w-12 flex-column"
                             style="height: 100%;">
                            <href class="w-12 ratio-3-4"
                                  [link]="item.url">
                                <df-image-wrapper class="fill">
                                    <img [dfImage]="item.image"
                                         [priority]="(data.isActive || data.isNext || data.isPrev) && (component.cwvLoading === 'eager' || component.cwvPriority)"
                                         [ratio]="'ratio-3-4'"
                                         fill
                                         alt="">
                                </df-image-wrapper>
                            </href>
                            <div class="flex-grow">
                                <a class="block p-t-3 left"
                                   [sizeClass]="'XL: p1, MS:p2'"
                                   [routerLink]="item.url">{{item.copy}}</a>
                            </div>
                        </div>
                        }
                        }
                        } @else {
                        <ng-container *ngTemplateOutlet="editorialContentTpl; context: { $implicit: item.model }" />
                        }
                    </ng-template>

                    }
                </carousel>
            </ng-template>

            <!-- cursor -->
            <ng-template #customCursorIcon>
                {{ 'Drag' | translate }}
            </ng-template>
        </custom-cursor>
    </div>
    }

    <!-- product list -->
    @if (!showCarousel) {
    <div class="m-t"
         [grid]="[2,2,4,4][client.sizeId]"
         (infiniteScroll)="listing?.increaseLimit()"
         [infinitScrollActive]="listing?.hasMore">
        @for (item of items | slice : 0 : limit; track $index) {
        <div>
            @switch (type) {
            @case ('product') {
            <product class="w-12 bg-col-w"
                     [model]="item.model"
                     [modelView]="item.modelView"
                     [altView]="item.altView"
                     [forceLoad]="true"
                     [disableCarousel]="true"
                     [hidePrice]="component.product_hide_price"
                     [hideName]="component.product_hide_name"
                     [hideLabel]="component.product_hide_label"
                     [hideVideo]="component.product_hide_video"
                     [wishlistSmallMessage]="client.isS || client.isM"
                     [listPosition]="$index"
                     [listId]="listId"
                     [listName]="listName" />
            }
            @case ('category') {
            <category class="w-12 bg-col-w"
                      [model]="item.model" />
            }
            @case ('page') {
            <cms-blog-post class="w-12 bg-col-w"
                           [model]="item.model" />
            }
            @case ('custom') {
            <div class="w-12 flex-column"
                 style="height: 100%;">
                <href class="w-12 ratio-3-4"
                      [link]="item.url">
                    <img [dfImage]="item.image"
                         [priority]="component.cwvLoading === 'eager' || component.cwvPriority"
                         [ratio]="'ratio-3-4'"
                         fill
                         alt="">
                </href>
                <div class="flex-grow">
                    <a class="block p-t-3 left"
                       [sizeClass]="'XL: p1, MS:p2'"
                       [routerLink]="item.url">{{item.copy}}</a>
                </div>
            </div>
            }
            }
        </div>
        }
    </div>
    }
</div>
}

<ng-template #editorialContentTpl
             let-content>
    <div class="w-12 ratio-3-4">
        <div class="rf flex flex-middle">
            <div class="w-9 w-12-s"
                 [ngClass]="{'p-r-3-s p-l-3-s': canLoop}">
                <span class="h2"
                      [style.color]="component.editorial_title_color || null"
                      *iCms="'editorial_title' of component; tag: content?.editorial_title_tag || 'h2'; content: 'html', skipEmpty: true"></span>

                <div class="m-t p-b col-12 p1"
                     [style.color]="component.editorial_text_color || null"
                     *iCms="'editorial_text' of component; content: 'html'; skipEmpty: true"></div>

                <href [link]="content.editorial_cta_link">
                    <span [ngClass]="content.editorial_cta_style || 'button-inline'"
                          *iCms="'editorial_cta_text' of component; content: 'html'; skipEmpty: true"></span>
                </href>
            </div>
        </div>
    </div>
</ng-template>
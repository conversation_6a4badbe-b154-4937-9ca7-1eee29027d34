<div class="w-12 h4 col-12 center">
    <h3 class="m-b-2"
        [sizeClass]="'XL: h3, S: h4'"
        *iCms="'icons_header' of component"></h3>
    @if (component.items) {
    <div class="w-12 col-12">
        @for (item of component.items; track $index) {
        <href [link]="component.mode === 'link' ? item.link : undefined">
            <span class="fs-4-s"
                  [ngClass]="$first ? 'm-r-1' : $last ? 'm-l-1' : 'm-l-1 m-r-1'"
                  (click)="share(item.platform)">
                <i [ngClass]="'icon-' + item.platform"
                   aria-hidden="true"></i>
            </span>
        </href>
        }
    </div>
    }
</div>
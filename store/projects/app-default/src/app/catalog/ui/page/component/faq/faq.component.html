<!-- faq -->
@if (!!component.faqs) {
<!-- faq title -->
<p class="p-l m-b-3 m-b-5-s"
   *iCms="'faqsTitle' of component; tag: component.tag; content: 'html', contentClass: [component.textColor || 'col-13', component.textStyle || '']">
</p>
<!-- faq items -->
<accordion (uiAccordionValueChange)="detectChanges()">
    @for (faqItem of component.faqs; track $index) {
    <accordion-item [value]="`faq-${uid}-${$index}`">
        <ng-container ngProjectAs="[heading]">
            <span
                  *iCms="'question' of faqItem; tag: component.questionTag; content: 'html', contentClass: [faqItem.questionColor || 'col-13', faqItem.questionStyle || '']"></span>
        </ng-container>
        <div
             *iCms="'answer' of faqItem; tag: component.answerTag; content: 'html', contentClass: [faqItem.answerColor || 'col-13', faqItem.answerStyle || '']">
        </div>
    </accordion-item>
    }
</accordion>
}
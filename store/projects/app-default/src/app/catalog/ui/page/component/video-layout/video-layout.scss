@use '~@tomandco/atomic/scss/core';
@use '~@tomandco/atomic/scss/atom/color';

:host {
    cursor: none;

    .icon-control {
        position: absolute;
        z-index: 5;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: none;
        pointer-events: auto;
    }

    .video-player-progress {
        position: absolute;
        z-index: 6;
        right: 0;
        bottom: 0;
        left: 0;
        height: 4px;
        opacity: 0;

        .video-player-progress-percent {
            width: var(--vimeo-player-progress, 0);
            height: 4px;
            background-color: color.get(23);
            transition: width 250ms ease;
        }
    }

    &:hover,
    .is-touchy & {
        video-control-sound,
        .video-player-progress {
            opacity: 1;
            transition: opacity 250ms ease-in;
        }
    }

    custom-cursor {
        cursor: none;

        .custom-cursor {
            width: 68px;
            height: 68px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.25);
            cursor: pointer;
            pointer-events: none;

            .size-s & {
                width: 48px;
                height: 48px;
            }
        }
    }
}

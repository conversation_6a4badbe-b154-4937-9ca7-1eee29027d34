@use '~@tomandco/atomic/scss/atom/color';
@use '~@tomandco/atomic/scss/atom/typography';
@use '~@tomandco/atomic/scss/core';

.local-model-carousel {
    --opacity: calc(100 * var(--carousel-translate, 0) / calc(var(--carousel-slide-width, 322) * -2));
    --width-percentage: calc(var(--carousel-slide-width, 322) * 100 / var(--carousel-width, 1) / 100);

    min-height: core.px-to-rem(50);

    :not(.is-initialized) & {
        min-height: 507px;

        .size-s & {
            min-height: 100%;
        }
    }

    &__content {
        width: calc(var(--carousel-slide-width, 322) * 1px);

        &:not(.opacity-disabled) {
            opacity: calc(1 - var(--opacity) / 100);
            transition: opacity 100ms cubic-bezier(0.445, 0.05, 0.55, 0.95);
            will-change: opacity;
        }

        .is-dragging & {
            transition: opacity 0ms;
        }
    }

    &__carousel {
        .size-x &,
        .size-l &,
        .size-m & {
            width: calc(100% - var(--carousel-slide-width, 322) * 1px);
            float: right;
        }
    }

    .swiper-horizontal > .swiper-scrollbar {
        bottom: 0;
        left: 50%;
        width: core.px-to-rem(222);
        height: core.px-to-rem(48);
        border-radius: 0;
        background-color: transparent;
        transform: translateX(-50%);

        &::after,
        .swiper-scrollbar-drag::after {
            position: absolute;
            top: 50%;
            right: 0;
            left: 0;
            display: block;
            width: 100%;
            height: core.px-to-rem(2);
            background-color: color.get(5);
            content: '';
            pointer-events: none;
            transform: translateY(-50%);
        }

        .swiper-scrollbar-drag {
            z-index: 2;
            border-radius: 0;
            background-color: transparent;

            &::after {
                background-color: color.get(1);
            }
        }
    }

    &-v2 {
        .swiper-horizontal > .swiper-scrollbar {
            left: calc(50% - (#{core.px-to-rem(222)} / 2) - (var(--width-percentage) * #{core.px-to-rem(222)}));
        }

        .carousel-dots {
            position: relative;
            left: calc((var(--carousel-slide-width, 322) * -1px) / 2);
        }
    }

    .carousel-dots {
        --carousel-dot-width: min(#{core.px-to-rem(48)}, calc((var(--carousel-slide-width) * 1px) / var(--carousel-dots-number, 1)));
        --carousel-dot-height: #{core.px-to-rem(2)};
        --carousel-dot-width-default: #{core.px-to-rem(48)};
        --carousel-dot-half-width: calc(var(--carousel-dot-width, #{core.px-to-rem(48)}) / 2);
        --carousel-dot-half-width-default: calc(var(--carousel-dot-width-default, #{core.px-to-rem(48)}) / 2);
        --carousel-dots-background: #{color.get(5)};
        --carousel-dots-background-hover: #{color.get(4)};
        --carousel-dots-background-active: #{color.get(1)};

        .carousel-dot {
            @for $i from 1 through 36 {
                &:nth-of-type(#{$i}).is-active ~ .carousel-dots-underbar {
                    --carousel-dot-position-left-#{$i}: calc(
                        (var(--carousel-dot-width, #{core.px-to-rem(48)}) * #{$i - 1}) +
                            var(--carousel-dot-half-width) - var(--carousel-dot-half-width-default)
                    );

                    left: min(
                        calc((var(--carousel-slide-width) * 1px) - var(--carousel-dot-width)),
                        max(1px, var(--carousel-dot-position-left-#{$i}, 0))
                    );
                }
            }
        }

        .carousel-dots-underbar {
            width: var(--carousel-dot-width-default);
        }
    }

    custom-cursor {
        &:not(.is-disabled) {
            cursor: grab;
        }

        .custom-cursor {
            display: inline-flex;
            width: core.px-to-rem(68);
            height: core.px-to-rem(68);
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: color.get(21);
            color: color.get(w);
            font-size: typography.fs(3);
            font-weight: 400;
            pointer-events: none;
            text-transform: uppercase;
            transform: translate3d(75%, -75%, 0) !important;
        }
    }

    ::ng-deep product ._bottom-section {
        .size-s & {
            padding-right: 0 !important;
            padding-left: 0 !important;
        }
    }
}

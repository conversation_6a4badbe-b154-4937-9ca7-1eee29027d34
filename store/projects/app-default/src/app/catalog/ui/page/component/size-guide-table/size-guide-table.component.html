<p class="s1 m-b-2"
   *iCms="'table_title' of component"></p>

@if (component.tables?.length === 2) {
<div>
    <div class="cursor-pointer inline-block p-a-1 b-col-4 b-radius-max b-a p2 col-12 center"
         style="width: 5em;"
         [toggle]="[uid, 0]"
         [toggleClass]="'bg-col-23'">{{'cm' | translate}}
    </div>
    <div class="cursor-pointer inline-block m-l-1 p-a-1 b-col-4 b-radius-max b-a p2 col-12 center"
         style="width: 5em;"
         [toggle]="[uid, 1]"
         [toggleClass]="'bg-col-23'">{{'inches' | translate}}
    </div>
</div>

@for (table of component.tables; track $index) {
<div class="w-12 m-t-2 b-a b-col-12 b-radius-4 overflow-x-auto custom-scrollbar ng-hide-animation ng-hide"
     [toggleClass]="'!ng-hide'"
     [toggleClassBy]="[uid, $index]">
    @for (row of table.rows; track $index; let rowIndex = $index) {
    <div class="flex flex-justify-between b-col-4">
        @for (column of row.columns; track $index) {
        <div class="b-l b-r b-col-4"
             style="min-width: 80px;"
             [class.bg-col-5]="table.column_background && $index === 0"
             [ngClass]="column.width">
            <div class="flex p-t-2 p-b-2 p-r-2 b-t"
                 [innerHTML]="column.value"
                 [ngClass]="[column.bold, column.color, $first ? 'flex-justify-start' : 'flex-justify-center ', $first ? 'p-l-2 s1' : '', $last ? 'p-r-2' : '', (!$first && rowIndex !== 0) ? 'p1' : 's1']">
            </div>
        </div>
        }
    </div>
    }
</div>
}
}
@else {
@for (table of component.tables; track $index) {
<div class="w-12 m-t-2 b-a b-col-12 b-radius-4 overflow-x-auto custom-scrollbar">
    @for (row of table.rows; track $index; let rowIndex2 = $index) {
    <div class="flex flex-justify-between b-col-4">
        @for (column of row.columns; track $index; ) {
        <div class="b-l b-r b-col-4"
             style="min-width: 80px;"
             [class.bg-col-5]="table.column_background && $index === 0"
             [ngClass]="column.width">
            <div class="flex p-t-2 p-b-2 p-r-2 b-t"
                 [innerHTML]="column.value"
                 [ngClass]="[column.bold, column.color, $first ? 'flex-justify-start' : 'flex-justify-center', $first ? 'p-l-2 s1' : '', $last ? 'p-r-2' : '', !$first ? 'p1' : '']">
            </div>
        </div>
        }
    </div>
    }
</div>
}
}

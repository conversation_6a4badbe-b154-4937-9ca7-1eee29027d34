<div class="left flex-x flex-l flex-m recipe">
    @if (client.isS) {
    <div class="w-6 center p-a-2 bg-col-33"
         [toggle]="['recipe', 0]">
        <p class="h2"
           *iCms="'left_tab_title' of component"></p>
    </div>

    <div class="w-6 center p-a-2 bg-col-34"
         [toggle]="['recipe', 1]">
        <p class="h2"
           *iCms="'right_tab_title' of component"></p>
    </div>

    @for (content of [component.content_left, component.content_right]; track $index) {
    <div class="w-12 p-l p-r p-t-10 p-b-10"
         [ngClass]="!$first ? 'bg-col-34' : 'bg-col-33'"
         [hideUp]="['recipe', $index]"
         [open]="!$index">

        @for (item of content; track $index) {
        <div [ngClass]="{'b-t b-col-13 m-t-8 p-t-8': !$first}">
            <h2 class="h2 m-b-3"
                *iCms="'subheading' of item; content: 'html'; skipEmpty: true"></h2>

            <p class="p3"
               *iCms="'copy' of item; content: 'html'"></p>

            @if ($any(item).image) {
            <img class="w-12 m-t"
                 [dfImage]="$any(item).image"
                 [priority]="component.cwvLoading === 'eager' || component.cwvPriority"
                 [ratio]="$any(item).ratio">
            }
        </div>
        }
    </div>
    }
    } @else {
    @for (content of [component.content_left, component.content_right]; track $index) {
    <div class="w-6 b-w-2 b-col-w p-a"
         [ngClass]="!$first ? 'bg-col-34 b-l' : 'bg-col-33 b-r'">
        <div class="overflow-y-auto custom-scrollbar h-100 p-r">
            @for (item of content; track $index) {
            <div [ngClass]="!$first ? 'b-t b-col-13 m-t-5 p-t-5' : ''">
                <h2 class="h2 m-b-3"
                    *iCms="'subheading' of item; content: 'html'; skipEmpty: true"></h2>

                <p class="p3"
                   *iCms="'copy' of item; content: 'html'"></p>

                @if ($any(item).image) {
                <img class="w-12 m-t-2"
                     [dfImage]="$any(item).image"
                     [priority]="component.cwvLoading === 'eager' || component.cwvPriority"
                     [ratio]="$any(item).ratio">
                }
            </div>
            }
        </div>
    </div>
    }
    }
</div>

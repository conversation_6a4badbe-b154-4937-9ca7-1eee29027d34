@if (store) {
<div class="flex flex-column bg-col-w z-10 w-12 p-b-7 p-t-3">
    <!-- store name -->
    <span class="h3 col-1">{{store.data.store_name}}</span>
    <!-- open status-->
    <store-open-status class="p-t-3"
                       [store]="store" />
    <!-- Store details-->
    <div class="flex w-12 p-t-7 p-b-7 b-b">
        <div class="flex flex-column flex-wrap p-r w-6">
            @if (store.data.address) {
            <p class="p1 p-t-2 flex flex-wrap">{{store.data.address}}</p>
            }
            <p class="p1">{{store.data.city}}, {{store.data.postcode}}</p>
        </div>
        <div class="flex flex-column w-6">
            @if (store.data?.phone) {
            <div class="pos-relative m-t-2">
                <a class="m-l-8 block"
                   [attr.href]="'tel:' + store.data.phone">
                    <i class="icon-phone pos-absolute top-50 left-0 p-l-1"
                       [style.font-size.rem]="1.2"></i>
                    <span class="p1 col-1">{{store.data.phone}}</span>
                </a>
            </div>
            }
            @if (store?.directions) {
            <div class="pos-relative m-t-1">
                <a class="m-l-8 block"
                   [attr.href]="store.directions">
                    <i class="icon-location-fill pos-absolute top-50 left-0 p-l-1"
                       [style.font-size.rem]="1.2"></i>
                    <span class="p1 col-1">{{'Get directions' | translate}}</span>
                    <i class="icon-share pos-absolute top-50 right-0 p-l-1"
                       [style.font-size.rem]="1.2"></i>
                </a>
            </div>
            }
        </div>
    </div>
    <!-- Schedule-->
    <div class="b-b p-t-7 p-b-7">
        @for (day of weeklySchedule; track $index) {
        <div class="flex flex-justify-between p-b-1 p-t-2">
            <p class="p1">{{day.date | date :'EEEE'}}</p>
            @switch (day.status) {
            @case (1) {
            <p class="p1 col-12 uppercase">{{ day.open | hourFormat12 : '' }} - {{ day.close | hourFormat12 : '' }}
            </p>
            }
            @case (2) {
            <p class="p1 col-12">{{'Closed' | translate}}</p>
            }
            }
        </div>
        }
    </div>
    <!-- Features-->
    @if (store.data.tags) {
    <div class="p-t-7">
        <p class="s1">{{'Features' | translate}}</p>
        <div class="p-t-4 flex">
            @for (tag of store.data.tags | keyvalue; track $index) {
            <div class="flex p-r-5">
                <div class="ratio-1-1"
                     style="width: 20px;">
                    <img [dfImage]="tag.value.tag_icon"
                         [ratio]="'ratio-1-1'"
                         [loaderParams]="{ fitIn: true, trim: true }"
                         fill>
                </div>
                <span class="p1 p-l-1">{{tag.value.tag_name}}</span>
            </div>
            }
        </div>
    </div>
    }
</div>
}
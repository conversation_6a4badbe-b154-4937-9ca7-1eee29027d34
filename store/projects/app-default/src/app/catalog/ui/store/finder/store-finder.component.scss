@use '~@tomandco/atomic/scss/core';
@use '~@tomandco/atomic/scss/atom/color';

.store-locator-container {
    height: 120vh;
}

.icon-marker-width {
    width: core.px-to-rem(17);
}

.select {
    background-color: #fff !important;
    border-color: color.get(12) !important;
    padding-top: core.px-to-rem(0);
}

.input {
    border: none !important;
    border-bottom: 1px solid color.get(1) !important;
    height: core.px-to-rem(33);
    line-height: core.px-to-rem(33);
    padding-left: core.px-to-rem(18);

    &::placeholder {
        color: color.get(12);
    }
}

label {
    color: color.get(12) !important;
    height: core.px-to-rem(35);
}

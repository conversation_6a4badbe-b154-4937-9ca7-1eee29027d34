<div class="store-image p-t-10">
    <div class="wrap p-t p-t-6-s p-b-3 p-b-1-s">

        <!-- space / X, L -->
        @if (client.sizeId > 1) {
        <div class="c-1"></div>
        }

        <div class="c-4 c-6-m c-12-s">
            <div class="w-12 m-b">
                <!-- back to stores -->
                <a [routerLink]="backUrl">
                    <i class="icon-arrow-forward fs-6 inline-block va-m m-l-1 m-r-1 m-r-2-s"
                       style="margin-top: -1px; transform: rotate(180deg);"></i>
                    <span class="p2">{{'Back to list' | translate}}</span>
                </a>
            </div>
            <div class="w-12 flex flex-justify-between flex-reverse-s">
                <!-- store image -->
                <div class="w-5 p-r-1-x p-r-1-l p-l-5-s">
                    @if (model.data.images?.length) {
                    <img class="w-12 b-radius-max"
                         [dfImage]="model.data.images[0]"
                         [ratio]="1">
                    }
                </div>

                <div class="w-5 p-l-4-x p-l-4-l p-r-1 p-r-4-s flex-column flex-justify-between">
                    <div class="w-12">

                        <!-- 'popular store' badge - temporarily hidden -->
                        <!-- <p class="c1 uppercase">Popular store</p> -->
                        <p class="c1 uppercase">&nbsp;</p>

                        <!-- store name -->
                        <h1 class="h2 m-t-1 m-t-2-s">{{model.data.store_name}}</h1>

                        <!-- suburb -->
                        <p class="p1 m-t-2 m-t-5-s">{{model.data.suburb}}</p>

                        <!-- open/close / X, L, M -->
                        @if (!client.isS) {
                        <p class="p2 m-t-3 m-t-2-m"
                           [ngClass]="model.isOpenToday ? 'col-3' : 'col-2'">
                            @if (model.isOpenToday) {
                            <!-- open -->
                            {{'Open today until' | translate}} {{model.todayOpeningHours.close}}
                            } @else {
                            <!-- closed -->
                            {{'Closed' | translate}}
                            }
                        </p>
                        }
                    </div>

                    <!-- get direction / X, L, M -->
                    @if (!client.isS) {
                    <a class="button w-12 m-t-1"
                       [attr.href]="model.directions"
                       target="_blank">
                        <span class="button__body">{{'Get directions' | translate}}</span>
                    </a>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<div class="wrap p-t-5 p-t-6-s">
    <!-- first column - store data -->
    <div class="c-6-set c-12-set-m c-12-set-s">

        <!-- space / X, L -->
        @if (client.sizeId > 1) {
        <div class="c-2"></div>
        }

        <div class="c-5-set c-6-set-m c-6-set-s p-r-6">

            <!-- heading -->
            <h3 class="m-b-2"
                [sizeClass]="'XL:h3,SM:s1'">{{'Visit us' | translate}}</h3>

            <!-- address -->
            @if (model.data.address || model.data.state || model.data.postcode) {
            <p class="p1">
                <span class="block">{{model.data.address}}</span>
                <span class="block">{{model.data.state}}, {{model.data.postcode}}</span>
            </p>
            }

            <!-- contact phone -->
            @if (model.data.phone) {
            <a class="m-t-6 m-t-8-m m-t-12-s inline-block"
               [attr.href]="'tel:' +  model.data.phone">
                <i class="icon-phone fs-10 fs-8-m fs-7-s inline-block va-m m-r-1"></i>
                <span class="p1 inline-block va-m">
                    {{model.data.phone}}
                </span>
            </a>
            }
        </div>

        <!-- second column - opening hours -->
        <div class="c-4-set c-6-set-m c-6-set-s p-l-3 p-l-1-s">

            <!-- heading -->
            <h3 class="m-b-2"
                [sizeClass]="'XL:h3,SM:s1'">{{'Opening times' | translate}}</h3>

            <!-- weekly schedule -->
            @if (weeklySchedule?.length) {
            <ul style="display: table;">
                <!-- row -->
                @for (day of weeklySchedule; track $index) {
                <li class="p1"
                    style="display: table-row;">

                    <!-- day -->
                    <div class="col-13 table-cell p-r-1 p-b-2 p-b-1-s">
                        {{day.date | date: 'EEEE'}}
                    </div>

                    <!-- hours -->
                    <div class="p-b-2 p-b-1-s table-cell">
                        @switch (day.status) {
                        <!-- open -->
                        @case(1) {
                        {{day.open}} - {{day.close}}
                        }
                        <!-- closed -->
                        @case(2) {
                        {{'Closed' | translate}}
                        }
                        }
                    </div>
                </li>
                }
            </ul>
            }
        </div>
        <!-- description -->
        @if (model.data.description) {
        <div class="p-t-1 p-t-2-s p-b-1 p-b-2-s">
            <!-- space / X, L -->
            @if (client.sizeId > 1) {
            <div class="c-2"></div>
            }

            <div class="c-8-set-x c-8-set-l p1">
                {{model.data.description}}
            </div>
        </div>
        }
    </div>

    <div class="c-6-set c-12-set-m c-12-set-s m-r-0">
        <!-- map -->
        @if (!client.isPrerender) {
        <div class="w-12 ratio-3-2">
            <google-map class="fill"
                        [options]="mapOptions"
                        [fitBounds]="true"
                        [stores]="[model]" />
        </div>
        }
    </div>
</div>

<div class="wrap p-t-9">
    <!-- space / X, L -->
    @if (client.sizeId > 1) {
    <div class="c-1"></div>
    }
    <!-- store features -->
    <div class="w-7 w-8-m w-12-s">
        <h3 class="m-b"
            [sizeClass]="'XLM:h3,S:s1'">{{'What’s in store' | translate}}</h3>
        <div [grid]="[2,4,5,5][app.client.sizeId]"
             [gridColumnGap]="2"
             [gridRowGap]="client.isS ? 7 : 5">
            @for (featureId of model.data.store_features; track $index) {
            <div class="flex flex-middle">
                <div class="w-2 w-3-m m-r-2">
                    <img class="w-12"
                         [dfImage]="featureId | optionImage : 'store_features'"
                         [ratio]="1">
                </div>
                <div class="p1">{{featureId | optionLabel : 'store_features'}}</div>
            </div>
            }
        </div>
    </div>

    <!-- Nearby stores -->
    @if (!client.isS) {
    <div class="w-3 w-4-m p-r-2-x p-r-2-l">
        <h3 class="h3 m-b p-l-5">{{'Nearby stores' | translate}}</h3>
        <div class="flex w-10 m-l-3">
            @for (store of model.data.nearest_stores | slice:0:3; track $any(store).store_id) {
            <store-link class="block h5 w-4 m-b-3"
                        [modelId]="$any(store).store_id" />
            }
        </div>
    </div>
    }
</div>

<div class="wrap-x wrap-l p-t-7">
    <!-- space / X, L -->
    @if (client.sizeId > 1) {
    <div class="c-1"></div>
    }
    <div class="c-10 c-12-m c-12-s">
        <icms-outlet-block class="block"
                           [variant]="'store-view'"
                           [name]="'Store header'" />
    </div>
</div>

<!-- Nearby stores -->
@if (client.isS) {
<div class="wrap m-b-5">
    <h3 class="s1 m-b-5">{{'Nearby stores' | translate}}</h3>
    <div class="flex w-10 m-l--2 p-r-2">
        @for (store of model.data.nearest_stores | slice:0:3; track $any(store).store_id) {
        <store-link class="block h3 w-4 m-b-3"
                    [modelId]="$any(store).store_id" />
        }
    </div>
</div>

<div class="element__hide-sticky-over"></div>

<div class="wrap bg-col-w pos-fixed bottom-0 left-0 right-0 z-2 center p-t p-b is-sticky ng-hide-animate hide-fade"
     style="box-shadow: 0 0 6px 0 rgba(0,0,0,.2);"
     stickyHide>

    <!-- get direction -->
    <a class="button w-12"
       [attr.href]="model.directions"
       target="_blank">
        <i class="icon-location fs-8 m-r-2"
           aria-hidden="true"></i>
        <span class="button__body">{{'Get directions' | translate}}</span>
    </a>

    <!-- open/close -->
    <p class="p2 m-t-3">
        @if (model.isOpenToday) {
        <!-- open -->
        {{'Open today until' | translate}} {{model.todayOpeningHours.close}}
        } @else {
        <!-- closed -->
        {{'Closed' | translate}}
        }
    </p>
</div>
}
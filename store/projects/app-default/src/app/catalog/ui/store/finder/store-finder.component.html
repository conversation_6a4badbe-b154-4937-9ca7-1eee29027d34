<div class="store-finder flex-column pos-relative w-12">
    @if (!selectedStore) {
    <h3 class="h3 p-b"
        translate>Find a store</h3>
    <ng-container *ngTemplateOutlet="searchInputAndLocation" />
    }
    @if (selectedStore) {
    <button type="button"
            class="icon-close right-0 top-3 pos-absolute"
            aria-hidden="true"
            [style.font-size.rem]="0.8"
            (click)="hideStoreDetails()"></button>
    } @else {
    <div class="m-b-8-s m-b-8-m m-t-0-s m-t-0-m flex-grow flex-column overflow-y-hidden">
        <form #form="ngForm"
              method="POST"
              novalidate="true">
            <div>
                <div class="m-t">
                    <div class="w-12 pos-relative search-input">
                        <input-wrap>
                            <input class="input col-12"
                                   name="address"
                                   type="text"
                                   #addressInput
                                   [attr.placeholder]="'Town, City, Postcode' | translate"
                                   [(ngModel)]="address"
                                   (keyup)="keyup($event)"
                                   [style.font-size.rem]="1">
                            @if (status.busy) {
                            <span class="fs-1 pos-absolute top-50 right-0 flex flex-middle">
                                <i class="icon-loading pe-none m-r-10"
                                   aria-hidden="true"
                                   [style.font-size.rem]="1"></i>
                            </span>
                            }

                            <div class="pos-absolute top-50 right-0 m-r-6 m-r-10-s p-b-1"
                                 [ngSwitch]="isUserSearch || client.isS">
                                <i class="cursor-pointer icon-close-circle p-r-1"
                                   *ngSwitchCase="true"
                                   (click)="reset()">
                                </i>
                            </div>

                        </input-wrap>

                        <button class="fs-1 pos-absolute top-50 right-0 flex flex-middle"
                                (click)="search()"
                                type="button"
                                aria-label="Search">
                            <i class="icon-search p-r-2"
                               aria-hidden="true"
                               [style.font-size.rem]="1.4">
                            </i>
                        </button>
                    </div>
                </div>

                <div class="w-12 flex p-b-5 p-b-4-s p-b-4-m flex-justify-between flex-middle">
                    <p class="p2 w-6 w-12-s w-12-m p-t-3 col-12">
                        {{(findableModels?.length === 1 ? '% store found' : '% stores found') | translate :
                        (findableModels?.length || 0)}}</p>
                </div>
            </div>
        </form>
        <result [status]="status" />
        <div class="flex flex-grow flex-column pos-relative"
             *ifSize="'MS'">
            <div class="flex flex-row b-b m-b-2 m-b-1-s m-b-1-m col-12">
                <button class="p-b-2 p-l-3 p-r-3 b-w-2 b-b b-col-1 h3-bold"
                        type="button"
                        [class.b-col-t]="isMapVisible"
                        [class.col-1]="!isMapVisible"
                        (click)="isMapVisible=false"
                        translate>List</button>
                <button class="p-b-2 p-l-3 p-r-3 b-w-2 b-b b-col-1 h3-bold"
                        type="button"
                        [class.b-col-t]="!isMapVisible"
                        [class.col-1]="isMapVisible"
                        (click)="isMapVisible=true"
                        translate>Map</button>
            </div>
            @if (isMapVisible) {
            <div class="ratio-2-3">
                <google-map class="fill"
                            [fitBounds]="true"
                            [stores]="findableModels"
                            [markerLimit]="1000"
                            (storeSelected)="onStoreMarkerClicked($event)">
                </google-map>
            </div>
            } @else {
            <ng-container *ngTemplateOutlet="searchResults" />
            }
        </div>
        <ng-container *ifSize="'XL'">
            <div #container
                 class="w-12 overflow-y-scroll custom-scrollbar flipped scrolling-touch flex flex-column flex-grow">
                <div class="flipped p-l-2">
                    <ng-container *ngTemplateOutlet="searchResults" />
                </div>
            </div>
        </ng-container>
    </div>
    }

    @if (selectedStore) {
    <store-details class="overflow-hidden"
                   [store]="selectedStore" />
    }
    <!-- shearch results -->
    <ng-template #searchResults>
        @for (store of findableModels; track store.id) {
        <a [routerLink]="store.data?.url">
            <div class="w-12 b-b-s b-b-m p-t-3 p-b-3 flex flex-column cursor-pointer"
                 [ngClass]="!$first ? 'b-t ': ''"
                 [attr.id]="'store-' + store.id">

                <div class="w-12 pos-relative flex flex-justify-between"
                     [ngClass]="{'col-4': displaySeeRange}">
                    <span class="s1">{{store.data.store_name}}</span>

                    <div class="flex">
                        @if (store.data?.distance) {
                        <div class="p2 m-r-2">
                            {{store.getDistance(true)}}mi
                        </div>
                        }
                        <button type="button"
                                aria-label="info">
                            <i class="icon-info"
                               aria-hidden="true"
                               [style.font-size.rem]="1.34"></i>
                        </button>
                    </div>
                </div>
                <p class="p1 p-t-1 col-12">{{store.data.address}}</p>
                <p class="p1 p-t-1 col-12">{{store.data.city}}, {{store.data.postcode}}</p>
                <store-open-status class="p-t-1"
                                   [store]="store" />
            </div>
        </a>
        }
    </ng-template>

    <ng-template #searchInputAndLocation>
        <form #form="ngForm"
              method="POST"
              novalidate="true">
            <input-wrap>
                <select class="select w-12"
                        name="country"
                        [(ngModel)]="country"
                        (change)="search()"
                        (keydown.enter)="search()">
                    <option [ngValue]="undefined">{{'Please Select a Country' | translate}}</option>
                    @for (item of countries; track item) {
                    <option [ngValue]="item.code">{{item.name}}</option>
                    }
                </select>
            </input-wrap>
        </form>
    </ng-template>
</div>
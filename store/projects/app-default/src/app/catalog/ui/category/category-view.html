<breadcrumbs class="wrap block m-t-2-x m-t-2-l m-t-3" />
@if (categoryHeaderType === 'content') {
<div class="pos-relative p-t p-t-2-m p-t-2-s wrap-x wrap-l _category-view-inner">
    <ng-container *ngTemplateOutlet="categoryHeader" />
</div>
}

<category-header [variant]="model.categoryHeaderVariant"
                 [category]="model" />

<cms-outlet-block name="Category Content Top"
                  [variant]="model.cmsBlockHeaderVariant" />

<div class="pos-relative p-t-6 p-t-2-m p-t-2-s">
    <div class="wrap-x wrap-l">
        @if (categoryHeaderType !== 'content') {
        <ng-container *ngTemplateOutlet="categoryHeader" />

        @let description = model!.data?.description | facetOverride : model!.id : 'description';

        <!-- description -->
        @if (description) {
        <div class="m-t-2-s m-b-2-x m-b-2-l wrap-m wrap-s m-t">
            <read-more class="w-6 w-12-m w-12-s"
                       [ngClass]="isStreamlineTest ? 'p-b-3-s p-b-3-m' : '_read-more'"
                       [text]="description"
                       [limit]="'' + (isStreamlineTest ? [45, 45, 80, 80][app.client.sizeId] : [90, 240, 210, 210][app.client.sizeId])"
                       [readLess]="true" />
        </div>
        }
        }
    </div>

    @if (client.isS || client.isM) {
    <ng-container *abIf="'(plpStreamline) A'">
        <div class="wrap quick-link--m-l m-t-3 m-b-2 center">
            <category-quick-links [variant]="model.cmsBlockQuickLinksVariant"
                                  [category]="model" />
        </div>
    </ng-container>
    }

    @if (listing) {
    <div class="pos-relative z-9 c-12-set-m c-12-set-s c-12-set m-r-0 m-b-0 _sticky-filter"
         [class._enabled-search]="enableMobileSearch">
        <sticky-free [stickyId]="'product-listing-filters'"
                     [stickyOff]="app.client.isCms"
                     [order]="1">
            <product-listing-filters class="w-12"
                                     [listing]="listing"
                                     [productListingComponent]="productListingComponent"
                                     [displayModelViewSwitch]="displayModelViewSwitch" />
        </sticky-free>
    </div>
    }

    <product-listing context="category"
                     [listing]="listing"
                     [category]="model" />

    @if (links?.length) {
    <div class="wrap-x wrap-l">
        <p class="h2 p-b p-t b-t wrap-m wrap-s">{{'Explore more' | translate}}</p>
        <div class="m-l-4-m m-l-4-s no-wrap overflow-hidden"
             dragScroll>
            @for (item of links; track $index) {
            <a class="inline-block bg-col-5 col-12 p2 quick-link"
               [routerLink]="$any(item).link"
               [attr.rel]="hasNoFollow ? 'nofollow' : null">
                {{$any(item).label | translate}}
            </a>
            }
        </div>
    </div>
    }
</div>

<cms-outlet-block name="Category Content Bottom"
                  [variant]="model.cmsBlockFooterVariant" />

<ng-template #categoryHeader>
    <!-- filters -->
    <div class="c-2 c-12-s c-10-m m-b-0 m-l-4-m m-l-4-s p-t-2 no-wrap-x no-wrap-l"
         [ngClass]="isStreamlineTest ? 'p-b-0 p-t-0-m p-t-0-s' : 'p-b-1 _title'">
        <!-- category name -->
        <h1 class="inline h2">{{model.name | facetOverride : model.id : 'h1'}}</h1>

        <!-- total results -->
        @if (isPlatformBrowser) {
        <span class="p-l-2 col-13 lh-2 p2 va-b no-wrap"
              [ngClass]="isStreamlineTest ? 'p-l-2-s p-l-2-m' : 'w-12-s p-l-0-s'"
              [class.w-12-s]="!isStreamlineTest"
              cy-searchResultSummary>
            {{(listing?.getTotal() === 1 ? '% style' : '% styles') | translate : listing?.getTotal()}}
        </span>
        }
    </div>
    @if (client.sizeId > 1) {
    <ng-container *abIf="'(plpStreamline) A'">
        <div class="m-b-0 p-t-2 p-b-1 overflow-hidden _quick-links"
             [ngClass]="displayModelViewSwitch ? 'c-6' : 'c-8'">
            <category-quick-links class="center"
                                  [variant]="model.cmsBlockQuickLinksVariant"
                                  [category]="model" />
        </div>
    </ng-container>
    }
</ng-template>
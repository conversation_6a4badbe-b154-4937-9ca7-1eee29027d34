<action class="button-inline"
        [status]="status"
        (click)="moveFromBasket(onSuccess)">
    <span class="flex flex-middle">
        <i class="icon-wishlist-thin fs-8 fs-9-s"
           [style.line-height.px]="0"
           aria-hidden="true"></i>
        @if (displayLabel) {
        <span class="no-s inline-block m-l-1 p2">{{'Move to wishlist' | translate}}</span>
        }
    </span>
</action>

<result class="block col-2 lh-1"
        [status]="status" />
@if (moved) {
<div class="fill z-2 bg-col-w flex flex-middle flex-justify-center">
    <div class="b-radius-basket w-12-s center bg-col-34 p-a-2 p-l-10 p-r-10 p1">{{'Item moved to wishlist' | translate}}
    </div>
</div>
}
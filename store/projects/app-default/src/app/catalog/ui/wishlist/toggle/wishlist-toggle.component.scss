@use '@tomandco/atomic/scss/component';
@use 'styles/icons';

:host {
    ._wishlist-toggle {
        position: relative;
        display: inline-flex;
        width: 47px;
        height: 47px;
        align-items: center;
        justify-content: center;
        border-color: transparent !important;
        background-color: transparent !important;
        color: var(--atomic-color-1);

        @include component.state-busy {
            &::before,
            &::after {
                display: none;
            }

            ._icon i {
                animation: heart-pulse 1000ms infinite;
            }
        }

        .size-s & {
            width: 41px;
            height: 41px;
        }

        &::before,
        &::after {
            display: none;
        }

        span.button__body {
            position: relative;
            z-index: 2;

            i {
                line-height: 0;
            }
        }

        &.ng-hide {
            opacity: 0;
            pointer-events: none;
            transition: opacity 250ms ease-in;
        }

        ._will-remove-animate {
            position: absolute;
            z-index: 11;
            top: 14px;
            left: 0;
            pointer-events: none;

            &.ng-hide {
                opacity: 0 !important;
            }

            &.icon-heart-left-fill {
                left: 8px;

                .size-s & {
                    top: 8px;
                    left: 2px;
                }
            }

            &.icon-heart-right-fill {
                left: 18px;

                .size-s & {
                    top: 8px;
                    left: 14px;
                }
            }
        }

        ._icon {
            font-size: 20px;
            line-height: 0;

            .size-m &,
            .size-s & {
                font-size: 24px;
            }
        }
    }

    &._on-product {
        ._wishlist-toggle {
            display: inline-flex;
            width: 20px;
            height: 20px;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(#fff, 0.5) !important;
            color: var(--atomic-color-13);
            transition: opacity 250ms ease-out;
            will-change: opacity;

            .size-s & {
                width: 24px;
                height: 24px;
            }

            ._will-remove-animate {
                top: 0;

                &.icon-heart-left-fill {
                    left: -5px;

                    .size-s & {
                        top: 0;
                        left: -5px;
                    }
                }

                &.icon-heart-right-fill {
                    left: 5px;

                    .size-s & {
                        top: 0;
                        left: 5px;
                    }
                }
            }
        }
    }
}

@keyframes heart-pulse {
    0% {
        transform: scale(1);
    }

    25% {
        transform: scale(1.2);
    }

    50% {
        transform: scale(1);
    }

    75% {
        transform: scale(0.8);
    }

    100% {
        transform: scale(1);
    }
}

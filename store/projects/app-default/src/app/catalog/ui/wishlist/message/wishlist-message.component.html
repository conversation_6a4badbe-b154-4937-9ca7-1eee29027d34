<div class="w-12 p-r-2 p-l-2 inline-flex flex-middle flex-justify-center b-radius-max bg-col-35 col-12 center _message-content"
     [sizeClass]="'!S: p2, S: c1'">
    <!-- logged in -->
    <div class="_message">
        @if (showLogin) {
        <p class="cursor-pointer _message"
           [iCmsModalOpen]="'login-form-modal'">
            <span class="fw-bold">{{'Create an account' | translate}}</span>&nbsp;{{'or' | translate}}&nbsp;<span
                  class="fw-bold">{{'sign in' | translate}}</span>&nbsp;{{'to save your wishlist' | translate}}.
        </p>
        } @else if (showBefore) {
        @if (listPosition === undefined) {
        <span class="fw-bold no-wrap">{{'Select your size to add to your wishlist' | translate}}</span>
        } @else {
        <span class="fw-bold no-wrap">{{'Select your size' | translate}}</span>
        }
        } @else if (showAlready) {
        <span class="fw-bold">
            {{'Selected size % is already in your wishlist' | translate : (size | optionLabel :
            'size')}}
        </span>
        <action class="m-l-2 button-inline underline"
                [status]="status"
                (click)="remove(product.id)">{{'Remove' | translate}}</action>
        } @else {
        <a rel="nofollow"
           [routerLink]="'/account/wishlist'">
            @if (size) {
            <span class="fw-bold">{{'Size %' | translate : (size | optionLabel : 'size')}}</span>
            {{'was added to your wishlist' | translate}}
            } @else {
            @if (!client.isS) {
            {{'The item was added to your wishlist' | translate}}
            } @else {
            {{'Added to your wishlist' | translate}}
            }
            }
        </a>
        }
    </div>
</div>
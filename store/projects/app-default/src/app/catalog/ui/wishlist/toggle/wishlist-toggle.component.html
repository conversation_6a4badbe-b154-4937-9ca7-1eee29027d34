<action class="pe-auto button-inline _wishlist-toggle"
        [status]="status"
        (click)="handleClick()"
        #buttonRef>
    <span class="flex flex-middle _icon">
        <i [ngClass]="inWishlist ? 'col-23 icon-wishlist-fill' : 'icon-wishlist-thin'"
           [attr.id]="'wishlist-toggle-icon-' + productId"
           #iconRef></i>

        <!-- remove animation -->
        <i class="col-23 ng-hide ng-hide-animate icon-heart-left-fill _will-remove-animate"
           #iconHeartLeftRef></i>
        <i class="col-23 ng-hide ng-hide-animate icon-heart-right-fill _will-remove-animate"
           #iconHeartRightRef></i>
    </span>
</action>

<result class="block pos-absolute top-100"
        [status]="status" />
import { InjectionToken } from '@angular/core';
import { IConfigData } from '@df/catalog/config/config.interfaces';
import { TLoadDependeciesParams } from '@df/catalog/model';
import { IProductData, IProductDataV2, IProductDataV3, Product } from '@df/catalog/product/product';
import { ProductResource } from '@df/catalog/product/product.resource';
import { NgInject } from '@df/ng/ng-inject';
import { optionLabelPipe } from '@df/ui/common/option-label.pipe';
import { TaxMode } from '@df/ui/price/price.service';
import { ICmsBlockResource } from '@icms/core/resource/block/icms-block.resource';
import { ICmsConfig } from '@icms/core/resource/config/icms-config';
import { ICmsConfigResource } from '@icms/core/resource/config/icms-config.resource';
import { Nexus } from '@tomandco/nexus-api-types';
import { CMS_CONFIG_PRODUCT, ICmsConfigProductDataContent, IProductTemplateRule } from '../../../cms/config/product.cms-config';
import { ListingLabelService } from '../../../common/listing-label/listing-label.service';
import { utm } from '../../../module/gtm/utm';
import { COUNTDOWN_BANNER_ID } from '../../ui/product/countdown-banner/countdown-banner.component';
import { IProductViewLayoutContent } from '../../ui/product/view/layout/product-view-layout.interface';
import { InternalProductsService } from './../../ui/product/internal-products.service';
import { ECategoryId } from './../category/category.decorator';

declare module '@tomandco/nexus-api-types' {
    namespace Nexus.Lib.Catalog.Type {
        interface IProductMediaItem {
            landscape: boolean;
        }
    }
}

declare module '@df/catalog/product/product' {
    interface IProductDataV1 {
        attribute_set: number;
        categories?: {
            first?: number;
            last?: number;
        };
    }

    interface IProductDataV2 {
        attribute_set: number;
        color?: number | number[];
        col_span?: number;
        row_span?: number;
        align_on_listing?: number;
        disable_carousel?: boolean;
        media?: Nexus.Lib.Catalog.Type.IProductMediaItem[];
        product_label?: string;
        rowspan?: number;
        // children information map providing some information about color variants for listing (only in configurable)
        colorMap: IColorMapEntry[];
        // if product is banner
        isBanner?: boolean;
        banner_type?: number;
        banner_vertical_align?: number;
        banner_cta_type?: number;
        banner_title?: string;
        banner_link?: string;
        banner_button_text?: string;
        quote?: string;
        quote_author?: string;
        // size guide variant;
        size_guide_new?: number;
        department?: number;
        product_type_filter?: number | number[];
        style?: number | number[];
        brand?: number;
        size?: number;
        productgroup?: string;
        subgroup?: string;
        typenumber?: string | number;
        groupnumber?: string | number;
        category_ids?: number[];
        category_path?: string;
        internal_only_product?: 1 | 0;
        prevent_payment_method?: string[];
        // if product should be displayed in catalog results when OOS
        display_catalog_if_oos?: boolean;
        // if product should be displayed in search results when OOS
        display_search_if_oos?: boolean;
        // label shown on product (see label getter too)
        listing_label?: number;
        // Display if out of stock
        oos_visibility: 0 | 1;
        // name for PLP
        listing_page_name?: string;
        // gift box
        enable_jewelry_giftbox?: 0 | 1;
        gift_box_product_id?: number;
        upsell_carousel?: number;
    }

    interface IProductDataV3 {
        custom_label_1?: number;

        /**
         * Id of the product page layout (used with cms block)
         */
        product_page_new?: string;
        /**
         * Id of product page type, for example (furniture, fashion, etc.)
         */
        product_page?: string;
        /**
         * Allow Gift Box attribute
         */
        allow_gift_box?: boolean;

        /**
         * If product is not available for giftboxes
         */
        noGifting?: boolean;

        /**
         * Allow countdown baner on PDP
         */
        allow_countdown_baner?: boolean;
        backorder_delivery_time?: number;
        delivery_flag?: number;

        /**
         * Attribute code to set content to pdp
         */
        pdp_cms_template?: number;

        /**
         * Attribute code to set content to pdp
         */
        brand_filter?: number[];
    }
}

export enum EListingLabelOptionId {
    preOrder = 2936,
    madeToOrder = 2949,
    oosTemp = 39792,
    newIn = 39793
}

/**
 * Colors map entry type
 */
export interface IColorMapEntry {
    color: number;
    media?: Nexus.Lib.Catalog.Type.IProductMediaItem[];
    image?: string;
    org_price?: number;
    parent: number;
    price: number;
    is_from_price?: boolean;
    group_price?: Record<number, number | undefined>;
    group_org_price?: Record<number, number | false | undefined>;
    group_is_from_price?: Record<number, boolean | undefined>;
}

export const MEDIA_IMAGE_LABEL_BASE = ['base'];
export const MEDIA_IMAGE_LABELS_PRODUCT_PAGE_DISABLED = ['plp1', 'plponly', 'landscape', 'alternative'];

export enum EBannerTypeOption {
    singleblock = 39650,
    quote = 39649
}

export enum EButtonCtaTypeOption {
    button = 39651,
    link = 39652
}

export interface IProductAttributeSets {
    furniture: number;
}

/**
 * Product attribute sets
 *
 * Use this token to change attribute sets for products based on environment
 */
export const PRODUCT_ATTRIBUTE_SETS = new InjectionToken<IProductAttributeSets>('[PRODUCT_ATTRIBUTE_SETS] - Product attribute sets', {
    providedIn: 'root',
    factory: () => {
        return {
            furniture: 19
        };
    }
});

export class ProductDecorator<T extends IProductData = IProductData> extends Product<T> {
    @NgInject(InternalProductsService)
    private readonly internalProductsService!: InternalProductsService;

    @NgInject(ICmsBlockResource)
    private readonly iCmsBlockResource!: ICmsBlockResource;

    @NgInject(ICmsConfigResource)
    private readonly cmsConfigResource!: ICmsConfigResource;

    @NgInject(ListingLabelService)
    private readonly listingLabelService!: ListingLabelService;

    @NgInject(PRODUCT_ATTRIBUTE_SETS)
    private readonly productAttributeSets!: IProductAttributeSets;

    /** @deprecated */
    public ppExternalLayoutVariant: string | undefined;

    protected override addDependenciesToLoader(params: TLoadDependeciesParams): void {
        super.addDependenciesToLoader(params);

        const loader = params.loader;
        const parent = (<IProductDataV2>this.data)?.parent;

        if (this.verbosity! > 1) {
            loader.add(this.listingLabelService.load());
        }

        if (this.verbosity === 2) {
            if (parent) {
                loader.add(
                    this.resources.product.load(parent, this.verbosity, {
                        safe: true
                    })
                );
            }

            if (this.isBanner) {
                loader.add(
                    this.iCmsBlockResource.retrieve(
                        {
                            bareUrl: 'product-banner-block',
                            variant: this.bannerVariant
                        },
                        params
                    )
                );
            }
        }

        if (this.verbosity! > 2) {
            loader.add(
                new Promise<void>(resolve => {
                    this.cmsConfigResource.loadModelByUrl(CMS_CONFIG_PRODUCT).then(() => {
                        const config = this.cmsConfigResource.getModelByUrl(CMS_CONFIG_PRODUCT) as ICmsConfig<
                            IConfigData<ICmsConfigProductDataContent>
                        >;

                        if (config?.data?.content?.templateRules?.length) {
                            this.ppExternalLayoutVariant = this.getPPExternalVariant(config.data.content.templateRules);

                            if (this.ppExternalLayoutVariant) {
                                this.iCmsBlockResource
                                    .retrieve(
                                        {
                                            bareUrl: 'product-page-external',
                                            variant: this.ppExternalLayoutVariant
                                        },
                                        params
                                    )
                                    .then(() => {
                                        resolve();
                                    });
                            } else {
                                resolve();
                            }
                        } else {
                            resolve();
                        }
                    });
                })
            );

            loader.add(
                this.iCmsBlockResource.retrieve<IProductViewLayoutContent>(
                    {
                        bareUrl: 'product-page-layout',
                        variant: this.productLayoutVariant
                    },
                    params
                )
            );

            // preload usp block
            const department = (<IProductDataV2>this.data).department;

            if (department) {
                loader.add(
                    this.iCmsBlockResource.retrieve(
                        {
                            bareUrl: 'product-banner-block',
                            variant: department?.toString()
                        },
                        params
                    )
                );
            }

            // preload gift box
            const giftBoxProductId = (<IProductDataV2>this.data).gift_box_product_id;

            if (this.hasGiftBox && giftBoxProductId) {
                loader.add(this.app.resources.product.load(giftBoxProductId, 2, { safe: true }));
            }

            if ((<IProductDataV3>this.data).allow_countdown_baner) {
                loader.add(this.iCmsBlockResource.retrieve({ bareUrl: COUNTDOWN_BANNER_ID }, params));
            }
        }
    }

    get displayDiscount(): boolean | undefined {
        const data = this.data as IProductDataV3;
        return !!data.custom_label_1 && !!data.org_price;
    }

    get productName(): string | undefined {
        const data = this.data as IProductDataV2;

        return data?.listing_page_name || data?.name;
    }

    get colSpan(): number {
        return (<IProductDataV2>this.data).col_span || 1;
    }

    get rowSpan(): number {
        return (<IProductDataV2>this.data).row_span || 1;
    }

    get reviewsCount(): number {
        const data = this.data as IProductDataV2;

        return data?.reviews?.count || 0;
    }

    get reviewsRating(): number {
        const data = this.data as IProductDataV2;
        const rating = data?.reviews?.rating || 0;

        return rating / 20;
    }

    override get labelShort(): string | undefined {
        const label = this.label;
        return typeof label === 'string' && !label.includes('style=') ? label.split(':')[0] : undefined;
    }

    /**
     * Gets final listing label value
     */
    override getListingLabel(): number | undefined {
        const data = this.data as IProductDataV2;

        if (this.hasV2) {
            if (this.isOutOfStock && (data.display_search_if_oos || data.display_catalog_if_oos)) {
                return EListingLabelOptionId.oosTemp;
            }

            const label = (<IProductDataV2>this.data).listing_label;

            if (label) {
                return label;
            }

            if (this.isNew) {
                return EListingLabelOptionId.newIn;
            }

            const stockQty = this.getStockQty(false, undefined, false);

            if (stockQty < 1 && data.category_ids?.includes(ECategoryId.madeToOrder)) {
                return EListingLabelOptionId.madeToOrder;
            }

            if (stockQty === 0 && data.category_ids?.includes(ECategoryId.preOrderNow)) {
                return EListingLabelOptionId.preOrder;
            }
        }

        return undefined;
    }

    get isFurniture(): boolean {
        if (this.hasV2) {
            const departmentLabel = optionLabelPipe((<IProductDataV2>this.data)?.department, 'department');
            return departmentLabel === 'Furniture';
        }
        return false;
    }

    ///////////////////////////////////////////////
    //
    // Banner related
    //
    ///////////////////////////////////////////////

    get isBanner(): boolean {
        return !!(<IProductDataV2>this.data).isBanner;
    }

    /**
     * Check if banner type is 'single block'
     */
    get isSingleBlockBanner(): boolean {
        return this.isBanner && (<IProductDataV2>this.data).banner_type === EBannerTypeOption.singleblock;
    }

    /**
     * Check if banner type is 'quote'
     */
    get isQuoteBanner(): boolean {
        return this.isBanner && (<IProductDataV2>this.data).banner_type === EBannerTypeOption.quote;
    }

    get bannerVariant(): string {
        return (<IProductDataV2>this.data)?.id.toString() || 'default';
    }

    /**
     * Retrieves banner vertical alignment position
     */
    get bannerVerticalAlign(): 'bottom' | 'top' | 'middle' {
        const bannerVerticalAlign =
            (<IProductDataV2>this.data)?.banner_vertical_align &&
            <string | undefined>optionLabelPipe((<IProductDataV2>this.data).banner_vertical_align, 'banner_vertical_align');

        return typeof bannerVerticalAlign === 'string' ? <any>bannerVerticalAlign : 'bottom';
    }

    /**
     * Retrieves type of banner CTA
     */
    get bannerCtaType(): 'link' | 'button' {
        const type = (<IProductDataV2>this.data).banner_cta_type;

        return type === EButtonCtaTypeOption.link ? 'link' : 'button';
    }

    get isLandscapeListingView(): boolean {
        return this.colSpan > 1 && !this.app.client.isS;
    }

    ///////////////////////////////////////////////
    //
    // Product page layout
    //
    ///////////////////////////////////////////////
    readonly productLayoutVariant = 'default';

    get productDepartmentVariant(): string {
        const department = (<IProductDataV2>this.data)?.department;

        if (Array.isArray(department)) {
            return department.join('-');
        }

        return department?.toString() || 'default';
    }

    /**
     * Retrieves the id of product page layout (used as variant with CMS block)
     * @deprecated - Use `productLayoutVariant` instead
     */
    get ppLayoutVariant(): string {
        return (<IProductDataV3>this.data).product_page_new ? (<IProductDataV3>this.data).product_page_new + '' : 'default';
    }

    /**
     * If product is configurable with size option
     */
    get hasSizeOption(): boolean {
        const data = this.data as IProductDataV2;

        return data && !this.isBanner && this.isConfigurable && !!data.options?.configurable?.['size'];
    }
    /**
     * If product is configurable with color option
     */
    get hasColorOption(): boolean {
        const data = this.data as IProductDataV2;

        return data && !this.isBanner && this.isConfigurable && !!data.options?.configurable?.['color'];
    }
    /**
     * If product is configurable with letter option
     */
    get hasLetterOption() {
        return this.configurableAttributeCodes?.includes('letter');
    }

    /**
     * Retrieves size guide variant id
     */
    get sizeGuideId(): string | undefined {
        return (this.data as IProductDataV3).size_guide_new?.toString() || (this.parent?.data as IProductDataV3).size_guide_new?.toString();
    }

    /**
     * Check if size guide exists
     */
    get hasSizeGuide(): boolean {
        return !!this.sizeGuideId;
    }

    override getOrgPrice(request?: Omit<Nexus.Lib.Basket.BasketRequestData, 'id'>, taxMode: TaxMode = TaxMode.auto): number | undefined {
        const superOrgPrice = this.calcPrice(true, request, taxMode, true);
        const defaultPrice = this.calcPrice(false, request, taxMode, true);
        const orgPrice = defaultPrice && superOrgPrice ? Math.max(defaultPrice, superOrgPrice) : defaultPrice || superOrgPrice;
        if (orgPrice && orgPrice > this.price) {
            return orgPrice;
        }
        return undefined;
    }

    override isSalable(qty: number = 1, buyRequest?: Omit<Nexus.Lib.Basket.BasketRequestData, 'id'>, withBasket: boolean = true): boolean {
        if (!this.isAvailable) {
            return false;
        }
        return super.isSalable(qty, buyRequest, withBasket);
    }

    override isInStock(qty: number = 1, buyRequest?: Omit<Nexus.Lib.Basket.BasketRequestData, 'id'>, withBasket: boolean = true): boolean {
        if (!this.isAvailable) {
            return false;
        }
        return super.isInStock(qty, buyRequest, withBasket);
    }

    override getStockQty(
        withBackorders = false,
        buyRequest?: Omit<Nexus.Lib.Basket.BasketRequestData, 'id'>,
        withBasket: boolean = true
    ): number {
        if (!this.isAvailable) {
            return 0;
        }
        return super.getStockQty(withBackorders, buyRequest, withBasket);
    }

    public override get isOutOfStock(): boolean {
        if (!this.isAvailable) {
            return true;
        }

        return super.isOutOfStock;
    }

    get familyIds() {
        return this.family.map(i => i.id);
    }

    /**
     * Flag for showing/hiding Klarna pay over time stuff for product.
     */
    get isKlarnaAllowed() {
        return !(<IProductDataV2>this.data).prevent_payment_method?.includes('klarna_pay_over_time');
    }

    private get isAvailable(): boolean {
        return this.internalProductsService.isProductAvailable(this);
    }

    get deliveryFlag(): number | undefined {
        return (<IProductDataV3>this.data)?.delivery_flag || (<IProductDataV3>this.parent.data)?.delivery_flag;
    }

    get backorderDeliveryTime(): number | undefined {
        return (<IProductDataV3>this.data)?.backorder_delivery_time || (<IProductDataV3>this.parent.data)?.backorder_delivery_time;
    }

    get hasGiftBox(): boolean {
        return (<IProductDataV2>this.data)?.enable_jewelry_giftbox === 1 && !!(<IProductDataV2>this.data)?.gift_box_product_id;
    }

    protected getPPExternalVariant(rules: IProductTemplateRule[]): string | undefined {
        if (!this.app.isPlatformBrowser) {
            return undefined;
        }

        const utmParams = utm(window.location.href);
        const rule = rules.find(rule => rule.utm.find(u => utmParams[u.key] !== u.value) === undefined);

        return rule?.template;
    }
}

ProductResource.instance = ProductDecorator;

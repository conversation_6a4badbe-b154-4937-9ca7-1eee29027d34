@let isCheckoutProcess = isCheckoutProcess$ | async;

<div class="page-wrapper"
     [toggleClass]="'is-search-open'"
     [toggleClassBy]="searchOutletToggleId">

    <ng-container *ifSize="'SM'">
        <off-canvas [toggleClass]="'_visible'"
                    [toggleClassBy]="offCanvasState"
                    [offCanvasState]="offCanvasState"
                    ngSkipHydration />
    </ng-container>

    <app-busy-state />

    <router-app-busy-state />

    <div class="pos-relative page-container"
         [toggleClass]="'is-meganav-open'"
         [toggleClassBy]="meganavToggleGroupId">
        <page-header [elementHeight]="headerElementId"
                     [elementHeightOptions]="{topOffset: true, local: false}"
                     [offCanvasState]="offCanvasState"
                     #pageHeaderRef />

        @if (hasHeader) {
        <div class="page-content">
            <router-outlet #outlet="outlet" />
        </div>
        }

        <ng-container *afterContentInit="'router'">
            @if (!isCheckoutProcess) {
            <page-footer />
            }
        </ng-container>

        <ng-container *afterContentInit>
            <div class="main-overlay"></div>
            <overlay class="z-9"
                     [visible]="overlayVisible" />

            <minibasket class="pos-fixed top-0 bottom-0 right-0 block ng-hide ng-hide-animate minibasket"
                        cy-miniBasket
                        [style.z-index]="1005" />

            <!-- change to overlay component portal -->
            @if (!isCheckoutProcess && (client.isS || client.isM)) {
            <basket-add-notice />
            }
        </ng-container>
    </div>
</div>
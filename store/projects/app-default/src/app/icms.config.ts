import { atomicColor } from '@df/ui/atomic/atomic-options';
import { NICmsClientConfig } from '@icms/client/config/icms-client.config';
import { NICmsContentComponent, NICmsContentField } from '@icms/core/content';
import { ICmsContentService } from '@icms/core/content/icms-content.service';
import range from 'lodash-es/range';

export const defaultImage = '/assets/placeholder.webp';
export const routeError = '_routeError';

export const ICMS_CONFIG: NICmsClientConfig.IConfig = {
    modules: {
        markerIo: {
            enabled: true
        },
        campaigns: {
            enabled: true
        }
    },
    sizes: [
        {
            id: 'X',
            label: 'Large Desktop',
            width: 1920
        },
        {
            id: 'L',
            label: 'Desktop',
            width: 1440,
            default: true
        },
        {
            id: 'M',
            label: 'Tablet',
            width: 768,
            height: 1024
        },
        {
            id: 'S',
            label: 'Mobile',
            width: 375,
            height: 667
        }
    ],
    colors: atomicColor as NICmsClientConfig.IColorConfig[],
    spacings: sizeId =>
        range(0, 21).map(key => ({
            key: `${key * [4, 5, 6, 7][sizeId]}px`,
            title: `${key} (${key * [4, 5, 6, 7][sizeId]}px)`
        }))
};

NICmsContentComponent.styleFields.unshift({
    key: 'styles.bg',
    type: NICmsContentField.EFieldType.colorPicker,
    title: 'Background Color',
    group: NICmsContentComponent.EFieldGroup.styles,
    default: undefined
});

ICmsContentService.registerComponentMetaPatch((meta: NICmsContentComponent.IMeta) => {
    // add styles to block components - OB request
    if (meta.block) {
        meta.fields.push(...NICmsContentComponent.styleFields.filter(field => !['styles.hidden', 'styles.sectionId'].includes(field.key)));
    }
});

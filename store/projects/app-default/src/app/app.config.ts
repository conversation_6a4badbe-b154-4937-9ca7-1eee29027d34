import { provideHttpClient, withFetch } from '@angular/common/http';
import {
    ApplicationConfig,
    EnvironmentProviders,
    importProvidersFrom,
    inject,
    provideAppInitializer,
    provideExperimentalZonelessChangeDetection,
    Provider
} from '@angular/core';
import { provideClientHydration, withEventReplay, withNoHttpTransferCache } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, withInMemoryScrolling } from '@angular/router';
import { provideDfApi } from '@df/api/df-api.providers';
import { RequestStatusService } from '@df/api/request/request-status.service';
import { provideDfBrowser } from '@df/browser/df-browser.providers';
import { provideDfCatalogUi } from '@df/catalog-ui/df-catalog-ui.providers';
import { IsBannerPluginService } from '@df/catalog-ui/listing/plugin/banner/is-banner-plugin.service';
import { ProductListingFactory } from '@df/catalog-ui/listing/product-listing.factory';
import '@df/catalog-ui/listing/sort/listing-sort-attribute-engine';
import { ListingSortAttributeEngine } from '@df/catalog-ui/listing/sort/listing-sort-attribute-engine';
import { provideDfCatalogUiRecentlyViewed } from '@df/catalog-ui/recently-viewed/recently-viewed.providers';
import '@df/catalog-ui/related-products/category-engine';
import '@df/catalog-ui/related-products/crosssell-engine';
import '@df/catalog-ui/related-products/upsell-engine';
import { AttributeResource } from '@df/catalog/attribute/attribute.resource';
import { Category } from '@df/catalog/category/category';
import { CategoryResource } from '@df/catalog/category/category.resource';
import { CustomformResource } from '@df/catalog/customform/customform.resource';
import { provideDfCatalog } from '@df/catalog/df-catalog.providers';
import { Product } from '@df/catalog/product/product';
import { ProductResource } from '@df/catalog/product/product.resource';
import { ReviewResource } from '@df/catalog/review/review.resource';
import { Store } from '@df/catalog/store/store';
import { StoreResource } from '@df/catalog/store/store.resource';
import { CheckoutService } from '@df/checkout/checkout.service';
import { provideDfCheckout } from '@df/checkout/df-checkout.providers';
import { provideDfCore } from '@df/core/df-core.providers';
import { Meta as MetaService } from '@df/core/meta/meta';
import { MetaAlternateService } from '@df/core/meta/meta-alternate.service';
import { ADirective } from '@df/core/meta/router/a.directive';
import { StructuralDataService } from '@df/core/meta/structural-data/structural-data.service';
import { MultisiteService } from '@df/core/service/multisite';
import { StylesService } from '@df/core/service/styles';
import { provideDfDom } from '@df/dom/df-dom.providers';
import { provideDfModuleAb } from '@df/module-ab/ab.providers';
import { provideDfModuleAbCms2 } from '@df/module-ab/cms2/ab-cms2.providers';
import { provideDfModuleAkamai } from '@df/module-akamai/akamai.providers';
import { provideDfModuleElasticsearch } from '@df/module-elasticsearch/elasticsearch.providers';
import '@df/module-exponea/exponea-recommendations-engine';
import { ExponeaServiceDef } from '@df/module-exponea/exponea.service.def';
import { FacetUrlService } from '@df/module-facet-url/core/facet-url.service';
import { provideDfModuleFacetUrl } from '@df/module-facet-url/facet-url.providers';
import { FacetUrlMetaPluginService } from '@df/module-facet-url/plugin/meta.plugin.service';
import { provideDfModuleGiftcards } from '@df/module-giftcards/giftcards.providers';
import { provideDfModuleGoogleMaps } from '@df/module-google-maps/google-maps.providers';
import { GoogleMapsService } from '@df/module-google-maps/google-maps.service';
import { GtmProvider } from '@df/module-gtm/gtm-provider.service';
import { provideDfModuleGtm } from '@df/module-gtm/gtm.providers';
import { GtmService } from '@df/module-gtm/gtm.service';
import { provideDfModuleImage, provideImage } from '@df/module-image/image.providers';
import { provideDfModuleKlevuSearch } from '@df/module-klevu-search/klevu-search.providers';
import { KlevuSearchService } from '@df/module-klevu-search/klevu-search.service';
import { KlevuTrackService } from '@df/module-klevu-search/klevu-track.service';
import { ListingSortKlevuEngine } from '@df/module-klevu-search/listing-sort-klevu.engine';
import { provideDfModuleLoqate } from '@df/module-loqate/df-module-loqate.providers';
import { provideDfModuleOneTrust } from '@df/module-onetrust/onetrust.providers';
import { provideDfModulePca } from '@df/module-pca/pca.providers';
import { provideDfModulePrivacy } from '@df/module-privacy/privacy.providers';
import { provideDfModuleProductConfiguration } from '@df/module-product-configuration/df-module-product-configuration.providers';
import { SagepayCloudPaymentService, SagepayPiService } from '@df/module-sagepay';
import { provideDfModuleSentry } from '@df/module-sentry/sentry.providers';
import { provideDfSearch } from '@df/search/df-search.providers';
import { SearchResult } from '@df/search/search-result';
import { SearchResultFactoryService } from '@df/search/search-result-factory.service';
import { Basket } from '@df/session/api/basket/basket';
import { CustomerModel } from '@df/session/api/customer.model';
import { Wishlist } from '@df/session/api/wishlist/wishlist';
import { CustomerLoggedGuard } from '@df/session/customer/customer-logged.guard';
import { provideDfSession } from '@df/session/df-session.providers';
import { provideDfUi } from '@df/ui/df-ui.providers';
import { TIME_UNITS } from '@df/ui/display/countdown.component';
import { provideDfUiGsap } from '@df/ui/display/gsap/gsap.providers';
import { provideDfUiModal } from '@df/ui/modal/modal.providers';
import { provideDfUiTranslation } from '@df/ui/translation/translation.providers';
import { provideDfSsr } from '@df/universal/client/df-ssr.providers';
import { ICmsClientModule } from '@icms/client/icms-client.module';
import { ICmsPageResource } from '@icms/core/resource/page/icms-page.resource';
import { ICmsCampaignsClientModule } from '@icms/plugin/campaigns/client/icms-campaigns-client.module';
import { provideAccount } from './account/account';
import { appRoutes } from './app.routes';
import './catalog/config/page.config';
import { CategoryDecorator } from './catalog/model/category/category.decorator';
import { ProductDecorator } from './catalog/model/product/product.decorator';
import { StoreDecorator } from './catalog/model/store/store.decorator';
import './catalog/ui/listing/filter/stock/filter-stock';
import { IsBannerPluginServiceDecorator } from './catalog/ui/listing/is-banner-plugin.service.decorator';
import { ProductListingFactoryDecorator } from './catalog/ui/listing/product-listing.factory.decorator';
import { ListingSortAttributeEngineDecorator } from './catalog/ui/listing/sort/listing-sort-attribute-engine';
import { WishlistDecorator } from './catalog/ui/wishlist/wishlist.decorator';
import { CheckoutCollectionMethods } from './checkout/checkout-form.component';
import { CheckoutServiceDecorator } from './checkout/checkout-service.service.decorator';
import { Metapack } from './checkout/collection/metapack/metapack.namespace';
import './cms/blog/group/cms-blog-group-navigation.config';
import { provideCmsComponents } from './cms/cms.providers';
import './cms/config/checkout.cms-config';
import './cms/config/open-search.cms-config';
import { ICmsPageResourceDecorator } from './cms/page/icms-model.resource.decorator';
import './core/app/app.config';
import { MetaDecorator } from './core/app/service/meta.decorator';
import { StructuralDataServiceDecorator } from './core/app/service/structural-data/structural-data.service.decorator';
import { StylesServiceDecorator } from './core/service/styles.decorator';
import { BasketDecorator } from './customer/api/basket/basket.decorator';
import { CustomerModelDecorator } from './customer/api/customer.model.decorator';
import './customer/basket/basket-item.decorator';
import { provideCustomerGroups } from './customer/groups/customer-groups.providers';
import { CustomerLoggedGuardDecorator } from './customer/guards/customer-logged.guard.decorator';
import { ICMS_CONFIG } from './icms.config';
import { LOCAL_TIME_UNITS } from './local.config';
import { provideCuralate } from './module/curalate/curalate.providers';
import './module/exponea/exponea-recommendations-model-carousel-engine';
import { ExponeaService } from './module/exponea/exponea.service';
import { FacetUrlServiceDecorator } from './module/facet-url/core/facet-url.service.decorator';
import { FacetUrlMetaPluginServiceDecorator } from './module/facet-url/core/meta.plugin.service.decorator';
import { FacetUrlGtmPluginService } from './module/facet-url/plugin/gtm.plugin.service';
import { GoogleMapsServiceDecorator } from './module/google-maps/google-maps.service.decorator';
import './module/google-maps/local-gmaps.config';
import { GtmProviderDecorator } from './module/gtm/gtm-provider.service.decorator';
import { GtmServiceDecorator } from './module/gtm/gtm.service.decorator';
import { provideIdle } from './module/idle/idle.providers';
import { KlevuSearchServiceDecorator } from './module/klevu-search/klevu-search.service.decorator';
import { KlevuTrackServiceDecorator } from './module/klevu-search/klevu-track.service.decorator';
import { ListingSortKlevuEngineDecorator } from './module/klevu-search/listing-sort-klevu.engine';
import { providePio } from './module/pio/pio.providers';
import { QuestionResource } from './module/question/question.resource';
import { SagepayCloudPaymentServiceDecorator } from './module/sagepay/cloud-payment/sagepay-cloud-payment.service.decorator';
import { SagepayPiServiceDecorator } from './module/sagepay/pi/sagepay.service.decorator';
import { StudentBeans } from './module/student-beans/student-beans';
import { MultisiteServiceDecorator } from './multisite/multisite.service.decorator';
import { SearchResultFactoryServiceDecorator } from './search/search-result-factory.service.decorator';
import { SearchResultDecorator } from './search/search-result.decorator';
import './ui/form/validate-pattern.config';

export const appComponents = [ADirective];

const providers: (Provider | EnvironmentProviders)[] = [];

if (typeof window !== 'undefined') {
    providers.push(provideExperimentalZonelessChangeDetection());
}

export const appConfig: ApplicationConfig = {
    providers: [
        ...providers,
        provideClientHydration(withEventReplay(), withNoHttpTransferCache()),
        provideHttpClient(withFetch()),
        provideAnimations(),
        provideDfApi(),
        provideDfBrowser(),
        provideDfCatalog(),
        provideDfCatalogUi({
            listing: {
                category: {
                    sort: [
                        {
                            label: 'Recommended',
                            default: true,
                            engine: 'klevu'
                        },
                        {
                            label: 'Newest in',
                            code: 'news_from_date',
                            desc: true
                        },
                        {
                            label: 'Price low',
                            code: 'price'
                        },
                        {
                            label: 'Price high',
                            code: 'price',
                            desc: true
                        }
                    ],
                    size: [
                        {
                            size: 1,
                            onSize: ['S', 'M']
                        },
                        {
                            size: 2,
                            onSize: ['S', 'M']
                        },
                        {
                            size: 3,
                            onSize: ['L', 'X']
                        },
                        {
                            size: 5,
                            onSize: ['L', 'X']
                        }
                    ],
                    defaultSize: [2, 2, 5, 5],
                    infScrollInit: [6, 6, 15, 15],
                    infScrollStep: [6, 6, 15, 15]
                },
                resetTop: undefined
            }
        }),
        provideDfCatalogUiRecentlyViewed(),
        provideDfCheckout({
            autoPickSavedCard: true,
            collectionMethods: [CheckoutCollectionMethods.storePickup, CheckoutCollectionMethods.metapackCollection],
            defaults: {
                newsletter: true
            }
        }),
        provideDfCore({
            multisite: {
                useCookie: true
            }
        }),
        provideDfDom(),
        provideDfModuleAb({
            convertcom: {
                script: app =>
                    ({
                        uat: '//cdn-4.convertexperiments.com/js/10045835-10045911.js',
                        staging: '//cdn-4.convertexperiments.com/js/10045835-10045911.js',
                        production: '//cdn-4.convertexperiments.com/js/10045835-10045912.js'
                    })[app.dfConfig.environment]
            }
        }),
        provideDfModuleAbCms2(),
        provideDfModuleAkamai({
            defaultOptions: {
                fillColor: 'transparent'
            }
        }),
        provideDfModuleElasticsearch(),
        provideDfModuleFacetUrl(),
        provideDfModuleGiftcards(),
        provideDfModuleGoogleMaps({
            mapOptions: {
                zoom: 14,
                fullscreenControl: false,
                mapTypeControl: false,
                scrollwheel: false,
                styles: [
                    {
                        featureType: 'landscape.man_made',
                        elementType: 'labels.icon',
                        stylers: [
                            {
                                visibility: 'off'
                            }
                        ]
                    },
                    {
                        featureType: 'landscape.man_made',
                        elementType: 'labels.text',
                        stylers: [
                            {
                                visibility: 'off'
                            }
                        ]
                    },
                    {
                        featureType: 'poi',
                        stylers: [
                            {
                                visibility: 'off'
                            }
                        ]
                    },
                    {
                        featureType: 'poi',
                        elementType: 'geometry',
                        stylers: [
                            {
                                visibility: 'simplified'
                            }
                        ]
                    },
                    {
                        featureType: 'poi.business',
                        stylers: [
                            {
                                visibility: 'off'
                            }
                        ]
                    },
                    {
                        featureType: 'road',
                        elementType: 'labels.icon',
                        stylers: [
                            {
                                visibility: 'off'
                            }
                        ]
                    },
                    {
                        featureType: 'transit',
                        stylers: [
                            {
                                visibility: 'off'
                            }
                        ]
                    }
                ]
            },
            storeToMarker: (store: Store, options: google.maps.MarkerOptions) => {
                return {
                    ...options,
                    icon: {
                        url: `/assets/images/checkout-collection-marker-${store.type === Metapack.COLLECTION_POINT_TYPE ? 'collection-point' : 'store'
                            }.svg`,
                        scaledSize: new google.maps.Size(50, 50)
                    },
                    info: `<div class="center font-default p-a-1">
                    <h5 class="h4 p-b-1">${store.data?.store_name || 'store name not set'}</h5>
                    <p class="p-b-1 font-2 fs-4">${store.data?.address || 'address not set'}</p>
                    <p class="font-2">
                    <a class="col-2" href="tel:${store.data?.phone || 'phone not set'}">
                    ${store.data?.phone || 'phone not set'}
                    </a>
                    </p>
                    </div>`
                };
            }
        }),
        provideDfModuleGtm(),
        provideDfModuleImage({
            placeholderBlur: false,
            placeholderBackgroundColor: '#fafafa',
            placeholderError: '/assets/placeholder-ob.webp?0123456789'
        }),
        provideDfModuleKlevuSearch(),
        provideDfModuleLoqate({
            emailKey: 'WR97-JH36-TJ79-NK89',
            addressKey: 'WR97-JH36-TJ79-NK89',
            phoneKey: 'JX23-TU54-DH42-GE33'
        }),
        provideDfModuleOneTrust(),
        provideDfModulePca(),
        provideDfModulePrivacy(),
        provideDfModuleProductConfiguration(),
        provideDfModuleSentry({
            dsn: 'https://<EMAIL>/58'
        }),
        provideDfSearch({
            listing: {
                size: [
                    {
                        size: 1,
                        onSize: ['S', 'M']
                    },
                    {
                        size: 2,
                        onSize: ['S', 'M']
                    },
                    {
                        size: 3,
                        onSize: ['L', 'X']
                    },
                    {
                        size: 5,
                        onSize: ['L', 'X']
                    }
                ],
                filters: ['price', 'product_type_filter', 'style', 'colors', 'material', 'size']
            },
            options: {
                view: {
                    stockCheck: false
                }
            }
        }),
        provideDfSession({
            wishlist: { guest: true },
            onCustomerLoggedGuardErrorUrl: '/'
        }),
        provideDfUi({
            price: {
                precision: 2,
                thousandSeparator: ',',
                freeLabelText: 'Free'
            }
        }),
        provideDfUiGsap(),
        provideDfUiModal(),
        provideDfUiTranslation(),
        provideDfSsr(),
        provideAccount(),
        provideCmsComponents(),
        provideCuralate({
            sitename: 'oliverbonas-ka0qyu'
        }),
        provideCustomerGroups(),
        provideIdle({
            enabled: true,
            idle: 30,
            timeout: 0,
            pingInterval: 60,
            idlePingInterval: 15 // 15sec
        }),
        provideImage(), // old `image` directive
        providePio({
            enabled: false,
            listing: {
                enabled: false
            },
            user: {
                enabled: true,
                cookieSeries: 1
            }
        }),
        importProvidersFrom(ICmsCampaignsClientModule, ICmsClientModule.forRoot(ICMS_CONFIG)),
        { provide: Basket, useExisting: BasketDecorator },
        { provide: Category, useExisting: CategoryDecorator },
        { provide: CheckoutService, useExisting: CheckoutServiceDecorator },
        { provide: CustomerLoggedGuard, useExisting: CustomerLoggedGuardDecorator },
        { provide: CustomerModel, useExisting: CustomerModelDecorator },
        { provide: ExponeaServiceDef, useExisting: ExponeaService },
        { provide: FacetUrlService, useExisting: FacetUrlServiceDecorator },
        { provide: FacetUrlMetaPluginService, useClass: FacetUrlMetaPluginServiceDecorator },
        { provide: GoogleMapsService, useExisting: GoogleMapsServiceDecorator },
        { provide: GtmService, useExisting: GtmServiceDecorator },
        { provide: ICmsPageResource, useExisting: ICmsPageResourceDecorator },
        { provide: IsBannerPluginService, useExisting: IsBannerPluginServiceDecorator },
        { provide: KlevuTrackService, useExisting: KlevuTrackServiceDecorator },
        { provide: ListingSortAttributeEngine, useExisting: ListingSortAttributeEngineDecorator },
        { provide: ListingSortKlevuEngine, useExisting: ListingSortKlevuEngineDecorator },
        { provide: GtmProvider, useExisting: GtmProviderDecorator },
        { provide: KlevuSearchService, useExisting: KlevuSearchServiceDecorator },
        { provide: MetaService, useExisting: MetaDecorator },
        { provide: MultisiteService, useExisting: MultisiteServiceDecorator },
        { provide: ProductListingFactory, useExisting: ProductListingFactoryDecorator },
        { provide: Product, useExisting: ProductDecorator },
        { provide: Store, useExisting: StoreDecorator },
        { provide: SagepayCloudPaymentService, useExisting: SagepayCloudPaymentServiceDecorator },
        { provide: SagepayPiService, useExisting: SagepayPiServiceDecorator },
        { provide: SearchResult, useExisting: SearchResultDecorator },
        { provide: SearchResultFactoryService, useExisting: SearchResultFactoryServiceDecorator },
        { provide: StructuralDataService, useExisting: StructuralDataServiceDecorator },
        { provide: StylesService, useExisting: StylesServiceDecorator },
        { provide: TIME_UNITS, useValue: LOCAL_TIME_UNITS },
        { provide: Wishlist, useExisting: WishlistDecorator },
        provideAppInitializer(() => {
            inject(AttributeResource);
            inject(CategoryResource);
            inject(CustomformResource);
            inject(ProductResource);
            inject(QuestionResource);
            inject(ReviewResource);
            inject(StoreResource);
            inject(Wishlist);
            inject(ExponeaService);
            inject(IsBannerPluginService);
            inject(FacetUrlGtmPluginService);
            inject(MetaAlternateService);
            inject(RequestStatusService);
            inject(SagepayCloudPaymentService);
            inject(StudentBeans);
        }),
        provideRouter(
            appRoutes,
            withInMemoryScrolling({
                scrollPositionRestoration: 'disabled' // we have our own implementation that does that
            })
        )
    ]
};

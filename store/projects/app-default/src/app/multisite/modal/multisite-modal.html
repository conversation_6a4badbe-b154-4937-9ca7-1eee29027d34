<ng-form class="flex flex-column p-a-1 p-a-0-s">
    @if (!client.isS) {
    <span class="cursor-pointer pos-absolute fs-3 top-5 right-5 icon-close"
          (click)="close()"></span>
    <div class="flex flex-justify-center m-b-10">
        <div class="logo-oliverbonas"></div>
    </div>
    }

    <p class="p1 center m-t-2 m-t-5-s m-b-6"
       *iCms="'copy' of contentVariant"></p>

    <!-- stores list -->
    <radio-group name="multisiteModel"
                 [(ngModel)]="selectedStore">
        @for (store of stores; track store.siteId) {
        <div class="b-radius-7 p-t-2 p-l-3 p-r-3 p-b-3 p-a-4-s b-a b-col-4 m-b-6 m-b-5-s shipping-selection pos-relative"
             [class.bg-col-34]="store.siteId === selectedStore"
             (click)="pick(store)">
            <!-- store image -->
            <img class="icon pos-absolute right-4 top-3 right-4-s top-5-s fs-4"
                 [dfImage]="store.icon"
                 [width]="16"
                 [height]="16">
            <!-- checkbox -->
            <radio class="block w-12 p-l-6 radio-full-width-label"
                   [value]="store.siteId">
                <!-- store information -->
                <div class="flex-grow col-11">
                    <div class="flex flex-top">
                        <!-- store name -->
                        <h5 class="s1 flex-grow"
                            *iCms="'title' of store"></h5>
                    </div>
                    <!-- subtitle -->
                    <p class="p2 col-13"
                       *iCms="'subtitle' of store"></p>
                    <!-- details -->
                    <div class="p2 m-t-1 text"
                         *iCms="'details' of store; content: 'html'">
                    </div>
                </div>
            </radio>
        </div>
        }
    </radio-group>

    <div class="center">
        <button class="w-12 m-b-3-s button"
                (click)="close()">
            <span *iCms="'buttonCopy' of contentVariant"></span>
        </button>
    </div>
</ng-form>
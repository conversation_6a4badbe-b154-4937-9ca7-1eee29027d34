import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { LoqateEmailValidateDirective } from '@df/module-loqate/email/loqate-email.validator';
import { DfModal } from '@df/ui/modal/df-modal.decorator';
import { NDfModal } from '@df/ui/modal/df-modal.interface';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { CmsNewsletterBlockComponent } from '../../../cms/newsletter-block/cms-newsletter-block.component';
import { ActionComponent } from '../../../ui/api/action.component';
import { ResultComponent } from '../../../ui/api/result.component';
import { VhFixDirective } from '../../../ui/display/vhfix/vh-fix.directive';
import { CheckboxComponent } from '../../../ui/form/checkbox';
import { InputWrapBodyDirective } from '../../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../../ui/form/input-wrap.component';
import { MuiInputDirective } from '../../../ui/form/mui-input.directive';
import { ScrollIntoViewDirective } from '../../../ui/form/scroll-into-view.directive';
import { ValidatePatternAsyncDirective } from '../../../ui/form/validate/validate-pattern-acync.directive';
import { ViewPasswordComponent } from '../../../ui/form/view-password.component';
import { LoginFormContentBlockComponent } from '../content/login-form-content-block.component';
import { LoginFormContentTextComponent } from '../content/login-form-content-text.component';
import { LoginFormModalAbstract } from './login-form-modal.abstract';
import { NLoginFormModal } from './login-form-modal.interface';

@DfModal({
    id: NLoginFormModal.LOGIN_FORM_MODAL_ID,
    position: 'right',
    config: sizeName => {
        return {
            minWidth: undefined,
            maxWidth: undefined,
            width: sizeName === 'S' || sizeName === 'M' ? '100%' : '30rem',
            height: '100%',
            minHeight: '100vh',
            maxHeight: '100vh',
            position: {
                top: '0',
                right: '0',
                bottom: '0'
            },
            panelClass: 'login-panel',
            modalClass: 'login-modal',
            closeOnNavigation: true
        };
    },
    reopenPolicy: NDfModal.ReopenPolicy.wait,
    sizeRestriction: ['S', 'M', 'L', 'X'],
    forbiddenLocation: /^\/(login|checkout)$/
})
@Component({
    selector: 'login-form-modal',
    templateUrl: './login-form-modal.component.html',
    styleUrl: './login-form-modal.component.scss',
    standalone: true,
    imports: [
        ActionComponent,
        CheckboxComponent,
        CmsNewsletterBlockComponent,
        CommonModule,
        FormsModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        MuiInputDirective,
        LoginFormContentBlockComponent,
        LoginFormContentTextComponent,
        LoqateEmailValidateDirective,
        ResultComponent,
        RouterModule,
        ScrollIntoViewDirective,
        TranslatePipe,
        ValidatePatternAsyncDirective,
        ViewPasswordComponent,
        VhFixDirective
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoginFormModalComponent extends LoginFormModalAbstract {
    protected override newsletterPlacement = 'login_tray'
}

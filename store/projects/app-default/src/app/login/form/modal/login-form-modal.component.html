<div class="bg-col-w z-10 height-100 pos-relative">
    <button class="pos-absolute top-4 right-4"
            type="button"
            (click)="close()">
        <i class="icon-close"
           aria-hidden="true"></i>
    </button>
    <div class="_content"
         vhFix
         [onlyCustomProperties]="true">
        <div class="p-l-5 p-r-5 p-r-3-l p-l-6-l p-b bg-col-w"
             style="padding-top: 7.6em">
            @if (!isLoggedIn || isCloseVariant) {
            <login-form-content-text [checked]="isChecked()"
                                     [shouldRegister]="shouldRegister()"
                                     variant="loginForm" />
            @if (shouldRegister()) {
            <login-form-content-block class="w-12 p-a-2 m-t" />
            }

            <form class="m-t-2 m-t-4-s"
                  method="POST"
                  [attr.name]="isRegisterForm ? 'create_account' : 'login'"
                  [attr.sl-form]="shouldRegister() ? 'register' : 'login'"
                  #form="ngForm">
                <input-wrap [canShowValidate]="emailInput?.value?.length > 0 && (isChecked() || form.submitAttempt)"
                            [messages]="{
                                    email: ('Valid email is required' | translate),
                                    required: ('Valid email is required' | translate),
                                    LOQATE_INVALID_EMAIL: ('Valid email is required' | translate)
                                }">
                    <input class="input"
                           name="email"
                           type="email"
                           autocomplete="email"
                           required
                           data-cs-mask
                           sl-input="email"
                           cy-loginMailInput
                           validatePatternAsync="email"
                           scrollIntoView="true"
                           [muiInput]="'Email address' | translate"
                           [muiInputValidateOnSubmit]="!shouldRegister()"
                           [loqateEmail]="shouldRegister()"
                           [(ngModel)]="email"
                           [ngModelOptions]="{updateOn: 'blur'}"
                           (keyup)="handleKeyUp($event)"
                           #emailInput>
                </input-wrap>

                @if (!isChecked()) {
                <div class="m-t">
                    <button class="button w-12"
                            sl-button="continue"
                            cy-loginMailSubmit
                            cy-loginSubmit
                            type="button"
                            [action]="checkStatus"
                            (click)="check()">
                        <span>{{'Continue' | translate}}</span>
                    </button>

                    <result class="m-t-1 block"
                            sl-result="continue"
                            cy-loginResult
                            [status]="checkStatus" />
                </div>
                }

                @if (isChecked()) {
                @if (shouldRegister()) {
                <input-wrap>
                    <input class="input"
                           type="text"
                           name="firstname"
                           required
                           data-cs-mask
                           sl-input="firstname"
                           cy-registerFnameInput
                           scrollIntoView="true"
                           [muiInput]="'First name' | translate"
                           [(ngModel)]="$any(data).firstname">
                </input-wrap>

                <input-wrap>
                    <input class="input"
                           type="text"
                           name="lastname"
                           required
                           data-cs-mask
                           sl-input="lastname"
                           cy-registerLnameInput
                           scrollIntoView="true"
                           [muiInput]="'Surname' | translate"
                           [(ngModel)]="$any(data).lastname">
                </input-wrap>
                }

                <view-password>
                    <input class="input"
                           type="password"
                           name="password"
                           cy-loginPwdInput
                           [attr.placeholder]="'Enter password' | translate"
                           required
                           data-cs-mask
                           scrollIntoView="true"
                           [muiInput]="(shouldRegister() ? 'Create a password' :'Password') | translate"
                           [(ngModel)]="data.password"
                           (keyup.enter)="submit()">
                </view-password>

                @if (!shouldRegister()) {
                <div class="m-t m-t-6-s m-b-s">
                    <action class="button-inline"
                            sl-button="reset"
                            [action]="passwordStatus"
                            (click)="resetPassword()">
                        <span class="fw-bold p2">{{'Forgotten your password?' | translate}}</span>
                    </action>
                    <result class="m-t-1 block"
                            [status]="passwordStatus"
                            sl-result="reset"
                            success="We've sent you an email with a link to reset your password." />
                </div>
                }

                @if (shouldRegister()) {
                <div class="m-t p-l-3 p-r-3 p-t-2 p-b-2 bg-col-33 b-radius-4">
                    <input-wrap class="block">
                        <checkbox name="termsandconditions"
                                  required
                                  [(ngModel)]="$any(data).termsandconditions"
                                  sl-input="termsandconditions"
                                  cy-registerTermsCheckbox>
                            <span class="p1">
                                {{'I agree to the Account Credit' | translate}}&nbsp;
                                <a class="underline"
                                   target="_blank"
                                   [routerLink]="'/about-us/terms-and-conditions'">{{'Terms & Conditions' |
                                    translate}}</a>
                            </span>
                        </checkbox>
                    </input-wrap>
                </div>
                }

                @if (showSubscriptionOptIn) {
                <cms-newsletter-block [newsletter]="data.newsletter"
                                      (newsletterChange)="dataChangeHandle($event)" />
                }

                <div class="m-t-5 m-t-7-l">
                    <button class="button w-12"
                            sl-button="continue"
                            cy-loginSubmit
                            type="button"
                            [action]="shouldRegister() ? registerStatus : loginStatus"
                            (click)="submit()">
                        @if (shouldRegister()) {
                        <span>{{'Create an account' | translate}}</span>
                        } @else {
                        <span>{{'Sign in' | translate}}</span>
                        }
                    </button>

                    <result class="m-t-1 block"
                            sl-result="continue"
                            cy-loginResult
                            [status]="shouldRegister() ? registerStatus : loginStatus" />
                </div>
                }
            </form>
            }
            @if (isLoggedIn && !isCloseVariant) {
            <h2 class="h2 m-b-3"
                sl-login-label="header"
                cy-loginConfirm>
                {{'You\'re signed in' | translate}}
            </h2>
            <p class="p1">
                {{'You can view recent orders in your account, add items to your wishlist and enjoy quicker checkout' |
                translate}}
            </p>
            <button class="button m-t w-12"
                    type="button"
                    (click)="close()">
                <span cy-continueShopping>{{'Continue shopping' | translate}}</span>
            </button>
            }
        </div>
    </div>
</div>
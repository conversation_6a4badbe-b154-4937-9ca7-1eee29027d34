<div class="w-12 center bg-col-33"
     [ngClass]="{'bg-col-w-m bg-col-w-s': isRegisterForm}">
    <div class="wrap w-12 m-t-10 m-t-4-m m-t-4-s m-b-16 m-b-4-m m-b-4-s">
        <icms-outlet [components]="cmsBlock?.componentsTop" />
        <div class="w-12">
            <div class="c-8-set-x c-8-set-l c-8-m-set-m m-r-0 m-b-0 left">
                <div class="_content"
                     vhFix
                     [onlyCustomProperties]="true">
                    <h1 class="m-b p-l p-l-0-m p-l-0-s"
                        [ngClass]="{'no-m no-s': isRegisterForm}"
                        [sizeClass]="'XL: h1, MS: h2'">{{'Log in or create an account' | translate}}</h1>
                    <form class="flex-column-m flex-column-s flex-column-reverse-m flex-column-reverse-s"
                          method="POST"
                          [attr.name]="isRegisterForm ? 'create_account' : 'login'"
                          [attr.sl-form]="isRegisterForm ? 'register' : 'login'"
                          #form="ngForm">
                        @if (!isLoggedIn) {
                        <div class="c-6-set-x c-6-set-l w-12 m-r-2-x m-r-2-l m-b-0 b-radius-5"
                             [ngClass]="isRegisterForm ? 'p-a-4-x p-a-4-l bg-col-w-x bg-col-w-l b-a-x b-a-l': 'p-a bg-col-w b-a'"
                             id="login-form">
                            <div [ngClass]="{'no-m no-s': isRegisterForm}">
                                @if (hasAccount()) {
                                <p class="p-b-2"
                                   [sizeClass]="'XL: h2, MS: h3'">{{'Welcome back, sign in' | translate}}</p>
                                } @else {
                                <p class="p-b-2"
                                   [sizeClass]="'XL: h2, MS: h3'">
                                    @if (app.client.sizeId > 1) {
                                    {{'Already have an account?' | translate}}
                                    } @else {
                                    {{'Sign in' | translate}}
                                    }
                                </p>
                                }
                                <p class="p2 col-12">
                                    {{'Sign in to your account to enjoy quicker checkout, save items to your wishlist,
                                    view your order history, and more.' | translate}}
                                </p>
                            </div>
                            @if (isLoginForm) {
                            <input-wrap class="w-12 pos-relative"
                                        [messages]="{
                                            email: ('Valid email is required' | translate),
                                            required: ('Valid email is required' | translate),
                                            LOQATE_INVALID_EMAIL: ('Valid email is required' | translate)
                                        }"
                                        [canShowValidate]="email?.length > 0 && (isChecked() || form.submitAttempt)">
                                <input class="input"
                                       name="email"
                                       type="email"
                                       autocomplete="email"
                                       required
                                       sl-input="email"
                                       cy-loginMailInput
                                       validatePatternAsync="email"
                                       [muiInput]="'Email address' | translate"
                                       [muiInputValidateOnSubmit]="true"
                                       [(ngModel)]="email"
                                       [ngModelOptions]="{updateOn: 'blur'}"
                                       (keyup.enter)="signUpEmailInput.blur()"
                                       #signUpEmailInput>
                                @if (checkStatus.busy) {
                                <span
                                      class="pos-absolute top-3 right-3 bottom-0 flex flex-middle flex-justify-center pe-none">
                                    <i class="icon-loading"
                                       aria-hidden="true"></i>
                                </span>
                                }
                            </input-wrap>

                            <input-wrap class="w-12 pos-relative">
                                <view-password>
                                    <input class="input"
                                           type="password"
                                           name="password"
                                           autocomplete="off"
                                           required
                                           cy-loginPwdInput
                                           [attr.placeholder]="'Enter password' | translate"
                                           [muiInput]="'Password' | translate"
                                           [(ngModel)]="data.password"
                                           (keyup.enter)="submitLogin()">
                                </view-password>
                            </input-wrap>

                            @if (hasAccount()) {
                            <div class="m-t-2 m-t-4-s m-b-s">
                                <action class="button-inline"
                                        sl-button="reset"
                                        type="button"
                                        [action]="passwordStatus"
                                        (click)="resetPassword()">
                                    <span class="c1">{{'Forgotten your password?' | translate}}</span>
                                </action>
                                <result class="m-t-1 block"
                                        sl-result="reset"
                                        [status]="passwordStatus"
                                        [success]="'We\'ve sent you an email with a link to reset your password.' | translate" />
                            </div>
                            }
                            <!-- newsletter sing up -->
                            @if (showSubscriptionOptIn && email) {
                            <cms-newsletter-block [newsletter]="data.newsletter"
                                                  (newsletterChange)="dataChangeHandle($event)" />
                            }
                            <div class="m-t-2">
                                <button class="button w-12"
                                        sl-button="continue"
                                        cy-loginSubmit
                                        [action]="loginStatus"
                                        (click)="submitLogin()">
                                    <span>{{'Sign in' | translate}}</span>
                                </button>
                                <result class="m-t-1 block"
                                        sl-result="continue"
                                        cy-loginResult
                                        [status]="loginStatus" />
                            </div>
                            }
                            @if (!isLoginForm) {
                            @if (app.client.sizeId > 1) {
                            <button class="w-12 m-t button"
                                    sl-button="continue"
                                    cy-loginMailSubmit
                                    cy-loginSubmit
                                    [action]="checkStatus"
                                    (click)="showLoginForm()">
                                <span>{{'Sign in' | translate}}</span>
                            </button>

                            <result class="m-t-1 block"
                                    sl-result="continue"
                                    cy-loginResult
                                    [status]="checkStatus" />
                            } @else {
                            <div class="center">
                                <button class="link s2"
                                        (click)="showLoginForm()">
                                    {{'Already have an account?' | translate}}&nbsp;
                                    <span class="underline">{{'Sign in here' | translate}}</span>
                                </button>
                            </div>
                            }
                            }
                        </div>

                        <div class="c-6-set-x c-6-set-l m-r-0 m-b-0 m-b-4-m m-b-4-s b-radius-5"
                             [ngClass]="isRegisterForm ? 'p-a-4-x p-a-4-l bg-col-w-x bg-col-w-l b-a-x b-a-l': 'p-a bg-col-w b-a'">
                            @if (shouldRegister()) {
                            <h2 class="h2">{{'You’re new here get £5 off your next order' | translate}}</h2>
                            } @else {
                            <h2 [sizeClass]="'XL: h2, MS: h3'">{{'You’re new here get £5 off your next order' |
                                translate}}</h2>
                            }
                            <p class="m-t-1 p2 col-12">
                                {{'Create an account and receive a welcome treat when you join. Plus, enjoy early access
                                to sales and new collections, quicker checkout, and more.' | translate}}
                            </p>
                            <login-form-content-block />
                            @if (!isRegisterForm) {
                            <button class="w-12 m-t-3 m-t-4-m m-t-4-s button"
                                    (click)="showRegisterForm()">
                                <span>{{'Create an account' | translate}}</span>
                            </button>
                            }
                            @if (isRegisterForm) {
                            <input-wrap class="w-12 pos-relative"
                                        [messages]="{
                                                    validateEmailExists: ('A customer with the same email address already exists' | translate),
                                                    email: ('Valid email is required' | translate),
                                                    required: ('Valid email is required' | translate),
                                                    LOQATE_INVALID_EMAIL: ('Valid email is required' | translate)
                                                }"
                                        [canShowValidate]="email?.length > 0">
                                <input class="input"
                                       name="email"
                                       type="email"
                                       autocomplete="email"
                                       required
                                       data-cs-mask
                                       sl-input="email"
                                       cy-loginMailInput
                                       validatePatternAsync="email"
                                       [loqateEmail]="true"
                                       [muiInput]="'Email address' | translate"
                                       [muiInputValidateOnSubmit]="false"
                                       [(ngModel)]="email"
                                       [ngModelOptions]="{updateOn: 'blur'}"
                                       #emailInput>
                                @if (checkStatus.busy) {
                                <span
                                      class="pos-absolute top-3 right-3 bottom-0 flex flex-middle flex-justify-center pe-none">
                                    <i class="icon-loading"
                                       aria-hidden="true"></i>
                                </span>
                                }
                            </input-wrap>

                            <input-wrap>
                                <input class="input"
                                       type="text"
                                       name="firstname"
                                       autocomplete="given-name"
                                       required
                                       sl-input="firstname"
                                       cy-registerFnameInput
                                       [muiInput]="'First name' | translate"
                                       [(ngModel)]="$any(data).firstname">
                            </input-wrap>

                            <input-wrap>
                                <input class="input"
                                       type="text"
                                       name="lastname"
                                       autocomplete="family-name"
                                       required
                                       sl-input="lastname"
                                       cy-registerLnameInput
                                       [muiInput]="'Last name' | translate"
                                       [(ngModel)]="$any(data).lastname">
                            </input-wrap>

                            <input-wrap>
                                <view-password>
                                    <input class="input"
                                           type="password"
                                           name="password"
                                           autocomplete="new-password"
                                           cy-loginPwdInput
                                           [attr.placeholder]="'Enter password' | translate"
                                           required
                                           [muiInput]="'Create a password' | translate"
                                           [(ngModel)]="data.password"
                                           (keyup.enter)="submitRegister()">
                                </view-password>
                            </input-wrap>

                            <div class="m-t-3 p-l-3 p-r-3 p-t-2 p-b-2 bg-col-33 b-radius-4">
                                <input-wrap class="block"
                                            [required]="true">
                                    <checkbox name="termsandconditions"
                                              required
                                              sl-input="termsandconditions"
                                              cy-registerTermsCheckbox
                                              [(ngModel)]="$any(data).termsandconditions">
                                        <span class="p1">
                                            {{'I agree to the Account Credit' | translate}}&nbsp;
                                            <a class="underline"
                                               target="_blank"
                                               [routerLink]="'/about-us/terms-and-conditions'">{{'Terms & Conditions' |
                                                translate}}</a>
                                        </span>
                                    </checkbox>
                                </input-wrap>
                            </div>

                            <!-- newsletter sing up -->
                            @if (showSubscriptionOptIn) {
                            <cms-newsletter-block class="w-12 m-t-2 m-t-6-s"
                                                  [newsletter]="data.newsletter"
                                                  (newsletterChange)="dataChangeHandle($event)" />
                            }

                            <div class="m-t-2">
                                <button class="button w-12"
                                        sl-button="continue"
                                        cy-loginSubmit
                                        [action]="registerStatus"
                                        (click)="submitRegister()">
                                    <span>{{'Create an account' | translate}}</span>
                                </button>
                                <result class="m-t-1 block"
                                        sl-result="continue"
                                        cy-loginResult
                                        [status]="registerStatus" />
                            </div>
                            }
                        </div>
                        } @else {
                        <div class="p-a bg-col-w b-a b-radius-5">
                            <h2 class="m-b-2"
                                [sizeClass]="'XL: h2, MS: h3'"
                                sl-login-label="header"
                                cy-loginConfirm>
                                @if (isNewUser) {
                                {{'Welcome, you\'re signed in' | translate}}
                                } @else {
                                {{'You\'re signed in' | translate}}
                                }
                            </h2>
                            <p class="p2">
                                @if (isNewUser) {
                                {{'You\'ve created an OB account and have £5 credit to spend on your first order. View
                                the credit in your account and redeem it at checkout.' | translate}}
                                } @else {
                                {{'You can view recent orders in your account, add items to your wishlist and enjoy
                                quicker checkout' | translate}}
                                }
                            </p>
                            <button class="button m-t-3 w-12-s"
                                    [style.min-width.px]="317"
                                    type="button"
                                    cy-continueShopping
                                    (click)="close()">
                                <span>{{'Continue shopping' | translate}}</span>
                            </button>
                        </div>
                        }
                    </form>
                </div>
            </div>
        </div>
        <icms-outlet [components]="cmsBlock?.componentsBottom" />
    </div>
</div>
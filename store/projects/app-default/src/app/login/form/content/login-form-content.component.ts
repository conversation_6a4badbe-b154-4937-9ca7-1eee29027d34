import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { LoqateEmailValidateDirective } from '@df/module-loqate/email/loqate-email.validator';
import { Login } from '@df/session/login/login.interfaces';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ICmsClientModule } from '@icms/client/icms-client.module';
import { CmsNewsletterBlockComponent } from '../../../cms/newsletter-block/cms-newsletter-block.component';
import { ActionComponent } from '../../../ui/api/action.component';
import { ResultComponent } from '../../../ui/api/result.component';
import { VhFixDirective } from '../../../ui/display/vhfix/vh-fix.directive';
import { CheckboxComponent } from '../../../ui/form/checkbox';
import { InputWrapBodyDirective } from '../../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../../ui/form/input-wrap.component';
import { MuiInputDirective } from '../../../ui/form/mui-input.directive';
import { ValidatePatternAsyncDirective } from '../../../ui/form/validate/validate-pattern-acync.directive';
import { ViewPasswordComponent } from '../../../ui/form/view-password.component';
import { LoginFormContentBlockComponent } from './login-form-content-block.component';
import { LoginFormContentAbstract } from './login-form-content.abstract';

declare module '@df/session/login/login.interfaces' {
    export namespace Login {
        export interface ILoginFormData {
            newsletter?: boolean;
        }

        export interface IResetPasswordFormData {
            newsletter?: boolean;
        }
    }
}

@Component({
    selector: 'login-form-content',
    templateUrl: './login-form-content.component.html',
    styleUrl: './login-form-content.component.scss',
    standalone: true,
    imports: [
        ActionComponent,
        CheckboxComponent,
        CmsNewsletterBlockComponent,
        CommonModule,
        FormsModule,
        ICmsClientModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        LoginFormContentBlockComponent,
        LoqateEmailValidateDirective,
        MuiInputDirective,
        ResultComponent,
        RouterModule,
        SizeClassDirective,
        TranslatePipe,
        ValidatePatternAsyncDirective,
        ViewPasswordComponent,
        VhFixDirective
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoginFormContentComponent extends LoginFormContentAbstract {}

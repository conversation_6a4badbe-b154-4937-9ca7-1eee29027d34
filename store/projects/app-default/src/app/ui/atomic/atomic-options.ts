import { UndefinedOption } from '@df/ui/atomic/atomic-options';
import { AtomicOption } from '@df/ui/atomic/atomic.interface';
import { NICmsContentField } from '@icms/core/content';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const atomic = require('atomicConfig'); // alias setup in webpack

export const atomicIcons = (atomic.icons || []) as AtomicOption[];
export const ICON_OPTIONS: NICmsContentField.IOption[] = [...UndefinedOption, ...atomicIcons];

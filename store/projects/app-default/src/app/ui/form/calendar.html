<!-- month and month nav -->
<div class="flex flex-middle flex-justify-between p-t-3 p-b-3 p-l-2 p-r-2">
    <!-- current month -->
    <div class="p1">{{currentMonth}}</div>
    <div>
        <!-- prev month -->
        <button class="icon-chevron-left fs-2 pos-relative m-l-3 m-r-3"
                type="button"
                (click)="prevMonth()"
                aria-label="prev month"></button>

        <!-- next month -->
        <button class="icon-chevron-right fs-2 pos-relative m-l-3 m-r-3"
                type="button"
                (click)="nextMonth()"
                aria-label="next month"></button>
    </div>
</div>

<div class="flex flex-justify-between p-l-2 p-r m-t-1 m-b-3 no-wrap calendar-week">
    @for (name of dayLabels; track $index) {
    <div class="w-2 flex-span-1 center uppercase c1">{{name}}</div>
    }
</div>

<!-- days of month -->
@for (week of weeks; track $index) {
<div class="calendar-week p-l-2 p-r m-b-2 flex flex-justify-between no-wrap">
    <!-- day -->
    @for (day of week; track $index) {
    <div class="calendar-day w-2 flex flex-middle flex-justify-center pos-relative"
         [ngClass]="{'is-active': day?.isPicked, 'is-now': day?.isToday}">
        <div class="now-day-overlay pos-absolute bg-col-34 b-col-21 z-4"></div>
        <div class="picked-day-overlay pos-absolute bg-col-34 b-a b-col-21 z-4"
             [style.border-width.px]="3"></div>
        <div class="z-5"
             [ngClass]="{
                'col-11': day?.isPicked,
                'col-13': day?.isDisabled || !day,
                'fw-semi-bold cursor-pointer': day && !day?.isDisabled
             }"
             (click)="pick(day)">{{day | date: 'd'}}</div>
    </div>
    }
</div>
}
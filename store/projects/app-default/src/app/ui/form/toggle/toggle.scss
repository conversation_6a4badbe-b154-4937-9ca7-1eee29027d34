@use '@tomandco/atomic/scss/component/toggle/toggle';

.toggle {
    @include toggle.toggle(
        $background: var(--atomic-background-color-13),
        $color: var(--atomic-color-5),
        $background-checked: var(--atomic-background-color-21),
        $color-checked: var(--atomic-color-21),
        $background-disabled: var(--atomic-background-color-4),
        $color-disabled: var(--atomic-color-5)
    );

    label {
        i {
            cursor: pointer;
            height: 12px;
            width: 24px;
            min-width: 24px;
            overflow: visible;
            border-radius: 12px;

            &::after {
                cursor: pointer;
                top: 1px;
                width: 10px;
                height: 10px;
                border-radius: 10px;
            }
        }

        &.is-checked,
        .is-checked & {
            i {
                &::after {
                    background-color: var(--atomic-background-color-w);
                    left: calc(100% - 11px);
                }
            }
        }

        &.is-disabled,
        .is-disabled & {
            opacity: 1;

            i {
                &::after {
                    background-color: var(--atomic-background-color-13);
                }
            }

            .toggle__body {
                color: var(--atomic-color-13);
            }
        }
    }
}

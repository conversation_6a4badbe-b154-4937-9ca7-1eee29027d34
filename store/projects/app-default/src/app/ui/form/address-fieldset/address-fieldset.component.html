<div ngModelGroup="address">
    <!--First name && Last name-->
    <input-wrap>
        <input class="input"
               type="text"
               name="firstname"
               autocomplete="xyz"
               required
               cy-addressAddFname
               data-cs-mask
               [muiInput]="'First name' | translate"
               [(ngModel)]="data.firstname"
               (blur)="onInputBlur()">
    </input-wrap>

    <input-wrap>
        <input class="input"
               type="text"
               name="lastname"
               cy-addressAddLame
               autocomplete="xyz"
               required
               data-cs-mask
               [muiInput]="'Surname' | translate"
               [(ngModel)]="data.lastname"
               (blur)="onInputBlur()">
    </input-wrap>

    <!--Country-->
    @if (!expandable) {
    <input-wrap>
        <select class="input"
                name="country"
                cy-addressAddCountry
                autocomplete="xyz"
                required
                data-cs-mask
                [muiInput]="'Country' | translate"
                [(ngModel)]="data.country_id"
                (change)="onCountryChange()">
            @for (country of countries | orderBy: ['name'] | pushCountriesToFront : countryCodes; track country.code) {
            <option [value]="country.code">{{country.name}}</option>
            }
        </select>
    </input-wrap>
    } @else {
    <input-wrap class="pos-relative z-8 _country"
                [offClickOff]="!countrySelectState?.value"
                [offClickFilter]="'._country-list'"
                (offClick)="closeCountrySelect()">

        <div class="pos-relative">
            <select class="input"
                    name="country"
                    cy-addressAddCountry
                    autocomplete="xyz"
                    disabled
                    required
                    [muiInput]="'Country' | translate"
                    [(ngModel)]="data.country_id"
                    [toggleClass]="'_is-open'"
                    [toggleClassBy]="countryToggleId"
                    (change)="onCountryChange()">
                @for (country of countries | orderBy: ['name'] | pushCountriesToFront : countryCodes; track
                country.code) {
                <option [value]="country.code">{{country.name}}</option>
                }
            </select>

            <div class="fill z-8 cursor-pointer"
                 (click)="toggleCountrySelect()"></div>

            <div class="pos-absolute right-0 top-100 left-0 z-9">
                <div class="hide-up-container">
                    <div class="ng-hide ng-hide-animate hide-up"
                         [toggleClass]="'!ng-hide'"
                         [toggleClassBy]="countryToggleId">

                        <div class="bg-col-w b-r b-b b-l b-col-21 _country-list">

                            <!-- search -->
                            <div class="p-t-1 p-r-2 p-b-2 p-l-2">
                                <input class="w-12 input input-size-1"
                                       [attr.placeholder]="'Search for a country' | translate"
                                       [(ngModel)]="countryQuery"
                                       [ngModelOptions]="{ standalone: true }"
                                       #countrySearchRef>
                            </div>

                            <ul class="pe-auto p-r-2 p-b-2 p-l-2">
                                @for (country of countries | orderBy: ['name'] | pushCountriesToFront : countryCodes |
                                searchCountries: countryQuery; track country.code) {
                                <li class="block p-a-2 p-a-4-s b-radius-7 cursor-pointer"
                                    [class._is-active]="data.country_id === country.code"
                                    [class.m-b-2]="$last"
                                    (click)="selectCountry(country.code)">
                                    {{country.name}}
                                </li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </input-wrap>
    }

    @if (searcher) {
    <div [offClickOff]="!searcher.hints.length"
         (offClick)="searcher.clear()">

        <input-wrap class="pos-relative"
                    [ngClass]="{'is-busy': searcher.status.busy}">
            <input class="input"
                   [class._is-open]="searcher.hints.length"
                   type="text"
                   name="query"
                   autocomplete="xyz"
                   data-cs-mask
                   [muiInput]="'Search for your address' | translate"
                   [(ngModel)]="searcher.query"
                   (keydown.enter)="searcher.search()">

            @if (searcher.status.busy) {
            <span class="pos-absolute top-3 right-3 bottom-0 flex flex-middle flex-justify-center pe-none">
                <i class="icon-loading"
                   aria-hidden="true"></i>
            </span>
            }

            <div class="pos-relative">
                <div class="pos-absolute right-0 left-0 z-5 bg-col-w p-l-2 p-b-2 p-r-2 b-l b-b b-r b-col-21 ng-hide dropdown-options overflow-y-scroll"
                     [ngClass]="{'ng-hide': !searcher.hints.length}"
                     style="max-height: 20em;"
                     role="listbox"
                     [attr.aria-hidden]="!searcher.hints.length"
                     [attr.tabindex]="searcher.hints.length ? 0 : -1">
                    <ul class="dropdown-options-inner m-t-1">
                        @for (hint of searcher.hints; track $index) {
                        <li class="flex flex-middle cursor-pointer no-wrap p-a-2 b-radius-7 dropdown-option"
                            role="option"
                            (click)="searcher.search(hint)">
                            <span class="w-12 p2 dropdown-option-label flex flex-justify-between flex-middle">
                                <p>
                                    <span class="block">{{$any(hint).Text}}</span>
                                    <span>{{hint.Description}}</span>
                                </p>
                                @if ($any(hint).Type === 'Street') {
                                <i class="icon-chevron-right"
                                   aria-hidden="true"></i>
                                }
                            </span>
                        </li>
                        }
                    </ul>
                </div>
            </div>
        </input-wrap>
    </div>
    }

    <toggle class="m-t-4 block"
            name="display_form"
            [(ngModel)]="canShowForm">
        <span class="p2 fs-3-l cursor-pointer"
              cy-addressAddUnfold>{{'Enter address manually' | translate}}</span>
    </toggle>

    <div [ngClass]="{'ng-hide': !canShowForm}">
        <!--Street Name-->
        <input-wrap>
            <input class="input"
                   type="text"
                   name="street_1"
                   autocomplete="xyz"
                   required
                   data-cs-mask
                   cy-addressAddFirstLine
                   [muiInput]="'Address line 1' | translate"
                   [(ngModel)]="data.street_1"
                   (blur)="onInputBlur()">
        </input-wrap>

        <input-wrap>
            <input class="input"
                   type="text"
                   autocomplete="xyz"
                   data-cs-mask
                   name="street_2"
                   cy-addressAddSecondLine
                   [muiInput]="'Address line 2' | translate"
                   [(ngModel)]="data.street_2"
                   (blur)="onInputBlur()">
        </input-wrap>

        <!--Post Code-->
        <input-wrap>
            <input class="input"
                   autocomplete="xyz"
                   type="text"
                   name="postcode"
                   required
                   data-cs-mask
                   cy-addressAddPostcode
                   [muiInput]="'Postcode' | translate"
                   [(ngModel)]="data.postcode">
        </input-wrap>

        <!--Town-->
        <input-wrap>
            <input class="input"
                   type="text"
                   name="city"
                   autocomplete="xyz"
                   required
                   data-cs-mask
                   cy-addressAddCity
                   [muiInput]="'Town/City' | translate"
                   [(ngModel)]="data.city"
                   (blur)="onInputBlur()">
        </input-wrap>

        <!--Region-->
        @if (!regions || !regions.length) {
        <input-wrap>
            <input class="input"
                   type="text"
                   name="region"
                   autocomplete="xyz"
                   data-cs-mask
                   cy-addressAddRegion
                   [required]="regionRequired"
                   [muiInput]="'Region' | translate"
                   [(ngModel)]="data.region"
                   (blur)="onInputBlur()">
        </input-wrap>
        } @else {
        <input-wrap>
            <select class="input"
                    name="region_id"
                    autocomplete="xyz"
                    [required]="regionRequired"
                    [muiInput]="'Region' | translate"
                    [(ngModel)]="data.region_id">
                <option value=""></option>
                @for (region of regions | orderBy: ['name']; track region.id) {
                <option [value]="region.id">{{region.name}}</option>
                }
            </select>
        </input-wrap>
        }
        <!--Phone-->
        @if (!phoneFlags) {
        <input-wrap class="async-validation"
                    [class.has-validation]="telephoneRef?.value?.length > 0"
                    [canShowValidate]="telephoneRef?.value?.length > 0">
            <input class="input"
                   name="telephone"
                   type="text"
                   inputmode="numeric"
                   autocomplete="xyz"
                   required
                   data-cs-mask
                   cy-addressAddPhone
                   validatePatternAsync="telephone"
                   [muiInput]="'Phone number' | translate"
                   [(ngModel)]="data.telephone"
                   (blur)="onInputBlur()"
                   #telephoneRef>
        </input-wrap>
        } @else {
        <input-wrap class="pos-relative z-8 async-validation _phone"
                    [class.has-validation]="phoneValidationEnabled"
                    [canShowValidate]="phoneValidationEnabled"
                    (offClick)="closePhoneSelect()"
                    [offClickOff]="!phoneSelectState?.value"
                    [offClickFilter]="'._phone-list'">
            <div class="pos-relative">
                <div class="flex"
                     [toggleClass]="'_is-open'"
                     [toggleClassBy]="phoneToggleId">
                    <div class="flex flex-middle flex-justify-between cursor-pointer _select"
                         (click)="togglePhoneSelect()">
                        <country-flag [code]="phoneCountryCode" />
                        <i class="icon-chevron-down"
                           [toggleClass]="'icon-chevron-up !icon-chevron-down'"
                           [toggleClassBy]="phoneToggleId"
                           aria-hidden="true"></i>
                    </div>
                    <input class="flex-grow input"
                           name="phoneTmp"
                           type="text"
                           inputmode="numeric"
                           autocomplete="xyz"
                           required
                           data-cs-mask
                           cy-addressAddPhone
                           validatePatternAsync="telephone"
                           [loqatePhone]="phoneCountryCode"
                           [muiInput]="'Phone number' | translate"
                           [(ngModel)]="phoneTmp"
                           (keyup)="onInputKeyUp()"
                           (blur)="onPhoneInputBlur()"
                           (loqatePhoneResponse)="onPhoneResponse($event)"
                           #telephoneRef>
                    <input type="hidden"
                           name="telephone"
                           [value]="data.telephone"
                           required>
                    <span
                          class="pos-absolute top-0 right-6 bottom-0 flex flex-middle flex-justify-center pe-none _loading">
                        <i class="icon-loading"
                           aria-label="true"></i>
                    </span>
                </div>
                <div class="pos-absolute right-0 top-100 left-0 z-9">
                    <div class="hide-up-container">
                        <div class="ng-hide ng-hide-animate hide-up"
                             [toggleClass]="'!ng-hide'"
                             [toggleClassBy]="phoneToggleId">
                            <div class="bg-col-w b-r b-b b-l b-col-21 _phone-list">
                                <!-- search -->
                                <div class="p-t-1 p-r-2 p-b-2 p-l-2">
                                    <input class="w-12 b-col-4 b-radius-4 input input-size-2"
                                           [attr.placeholder]="'Search for a country' | translate"
                                           autocomplete="off"
                                           [(ngModel)]="phoneQuery"
                                           [ngModelOptions]="{ standalone: true }"
                                           (ngModelChange)="detectChanges()"
                                           #phoneSearchRef>
                                </div>
                                <ul class="pe-auto p-r-2 p-b-2 p-l-2">
                                    @for (country of countries | orderBy: ['name'] | pushCountriesToFront : countryCodes
                                    | searchCountries: phoneQuery; track country.code) {
                                    <li class="w-12 flex flex-middle p-a-2 p-a-4-s b-radius-7 cursor-pointer"
                                        [class._is-active]="phoneCountryCode === country.code"
                                        [class.m-b-2]="$last"
                                        (click)="selectPhoneCode(country.code)">
                                        <country-flag class="m-r-1"
                                                      [code]="country.code" />
                                        {{country.name}}
                                        @if (country.dialCode) {
                                        <span class="inline-block m-l-1 col-13">{{country.dialCode}}</span>
                                        }
                                    </li>
                                    }
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </input-wrap>
        }
        @if (!isBilling) {
        <p class="m-t-2 p2 fs-3-l col-13">{{'*You\'ll receive delivery updates by SMS' | translate}}</p>
        }
    </div>
</div>
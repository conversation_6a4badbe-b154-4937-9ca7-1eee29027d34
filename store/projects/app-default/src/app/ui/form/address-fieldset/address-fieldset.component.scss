@use '~@tomandco/atomic/scss/atom/color';

:host ::ng-deep {
    ._country,
    ._phone {
        > div.is-disabled,
        .input.disabled,
        .input.is-disabled,
        .input:disabled,
        .input[disabled],
        .mui-input.is-disabled select ~ label,
        .mui-input select[disabled] ~ label {
            color: color.get(12) !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        &:hover .input:not(._is-open) {
            border-color: color.get(12);
        }

        &-list {
            border-bottom-left-radius: 7px;
            border-bottom-right-radius: 7px;
        }

        ul {
            overflow: hidden;
            max-height: 200px;
            overflow-y: scroll;

            li {
                &._is-active {
                    background-color: color.get(4);
                }

                &:hover {
                    background-color: color.get(34);
                }
            }
        }
    }

    ._phone {
        .mui-input {
            border: 1px solid color.get(4);
            border-radius: 7px;
            padding-top: 0;
            margin-top: 1.375em;

            .input {
                border: none;
                padding-left: 0;
            }

            label {
                top: 2px;
                left: 76px;
            }

            &.is-focused,
            &.is-not-empty {
                label {
                    top: -10px !important;
                    left: 0.95rem !important;
                    background-color: #fff;
                    opacity: 1;
                }
            }

            &.is-valid:not(.is-select):not(.is-pending),
            &.is-invalid:not(.is-select):not(.is-pending) {
                &::before {
                    top: 1em;
                }
            }

            &._is-open {
                border-color: color.get(21);
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
                border-bottom: 0px;
            }

            &::before {
                top: 1.25em !important;
            }
        }

        ._select {
            padding-right: 19px;
            padding-left: 19px;

            i {
                font-size: 12px;
                margin-left: 6px;
            }
        }

        &:not(.async-validation) .is-required,
        &.async-validation.has-validation .is-required {
            .mui-input:not(.is-valid) {
                border-color: color.get(2);

                input {
                    ~ label {
                        color: color.get(2) !important;
                    }
                }
            }

            ._phone-list {
                border-color: color.get(2) !important;
            }
        }

        &:not(.async-validation) .is-required,
        &.async-validation:not(.has-validation) .is-required {
            .mui-input:not(.is-valid) {
                border-color: color.get(4) !important;

                &::before {
                    display: none !important;
                }

                input {
                    ~ label {
                        color: color.get(12) !important;
                    }
                }
            }

            ._phone-list {
                border-color: color.get(4) !important;
            }
        }
    }

    .input._is-open {
        border-color: color.get(21);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom: 0px;
    }
}

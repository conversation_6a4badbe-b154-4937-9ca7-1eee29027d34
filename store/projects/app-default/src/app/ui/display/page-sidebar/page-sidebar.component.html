<div class="pos-relative fill flex-column p-t-4-x p-t-6 overflow-y-auto custom-scrollbar"
     [class]="backArrow ? 'p-r-9 p-l-9' : 'p-r p-l'"
     [offClickOff]="!isOpen"
     [offClickFilter]="'.no-sidebar-close'"
     (offClick)="close()">
    <div class="w-12 flex"
         [class]="!backArrow ? 'flex-justify-end' : 'flex-justify-between'">
        <button class="_close"
                [class.is-arrow]="backArrow"
                [attr.aria-label]="!backArrow ? ('Close' | translate) : undefined"
                (click)="requestClose()">
            @if (backArrow) {
            <!-- back buttons -->
            <span class="flex flex-middle">
                <span class="arrow-rotation"
                      [style.line-height.px]="0"><i class="icon-arrow-forward fs-6"
                       aria-hidden="true"
                       [style.line-height.px]="0"></i></span>
                <span class="col-12 p1 m-l-3">{{'Back' | translate}}</span>
            </span>
            } @else if (!closeButton) {
            <!-- cross -->
            <i class="icon-close-24"
               aria-hidden="true"></i>
            }
        </button>
        @if (closeButton) {
        <button class="_close"
                type="button"
                [attr.aria-label]="'Close' | translate"
                (click)="close()">
            <i class="icon-close-24"
               aria-hidden="true"></i>
        </button>
        }
    </div>
    <div class="flex-column flex-grow pos-relative">
        <ng-content />
    </div>
</div>
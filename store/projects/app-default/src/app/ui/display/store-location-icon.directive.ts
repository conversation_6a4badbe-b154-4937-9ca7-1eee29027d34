import { Directive, ElementRef, OnD<PERSON>roy, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Meta } from '@df/core/meta/meta';
import { Renderer } from '@df/ng/renderer';
import { Subscription } from 'rxjs/internal/Subscription';

@Directive({
    selector: '[storeLocatorIcon]',
    standalone: true
})
export class StoreLocatorIconDirective implements OnInit, OnDestroy {
    private subscriptions: Subscription = new Subscription();
    private isStoreLocator: boolean;

    constructor(
        private elementRef: ElementRef,
        private renderer: Renderer,
        private router: Router,
        private meta: Meta
    ) {}

    /**
     * Subscribes to router changes and runs initial check
     */
    ngOnInit() {
        this.subscriptions.add(
            this.meta.routerEvents.subscribe(routerEvent => {
                if (routerEvent instanceof NavigationEnd) {
                    this.update();
                }
            })
        );
        this.update();
    }

    ngOnDestroy() {
        this.subscriptions.unsubscribe();
    }

    private update() {
        const isStoreLocator = !!this.router.url.match(/^\/about-us\/store-locator/);
        if (this.isStoreLocator !== isStoreLocator) {
            this.renderer.setClass(this.elementRef.nativeElement, ['icon-location-fill', 'col-23'], isStoreLocator);
            this.renderer.setClass(this.elementRef.nativeElement, 'icon-location', !isStoreLocator);
            this.isStoreLocator = isStoreLocator;
        }
    }
}

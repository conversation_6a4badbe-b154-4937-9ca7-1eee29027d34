import { Injectable } from '@angular/core';
import { IProductDataV2, IProductDataV3, Product } from '@df/catalog/product/product';
import { StructuralData } from '@df/core/meta/structural-data/structural-data.namespace';
import { StructuralDataService } from '@df/core/meta/structural-data/structural-data.service';
import { optionLabelPipe } from '@df/ui/common/option-label.pipe';
import { ProductDecorator } from '../../../../catalog/model/product/product.decorator';

declare module '@df/core/meta/structural-data/structural-data.namespace' {
    namespace StructuralData {
        export interface IProductData extends IData<Type.product> {
            isVariantOf?: IProductParentVariantData;
            productGroupID?: number | string;
            variesBy?: string[];
            hasVariant?: IProductData[];
        }

        export interface IGroupProductData extends Omit<IProductData, '@type'> {
            '@type': IData<Type.productGroup>['@type'];
        }

        export interface IProductParentVariantData {
            '@type': IData<Type.productGroup>['@type'];
            '@id': string;
            name: string;
        }
    }
}

@Injectable({
    providedIn: 'root'
})
export class StructuralDataServiceDecorator extends StructuralDataService {
    private getProductChildData(product: ProductDecorator<IProductDataV3 | IProductDataV2>): any {
        const productData = product.data as IProductDataV3;

        const data = {
            '@type': StructuralData.Type.product,
            sku: productData.sku,
            url: this.url.toUrl(productData.url || ''),
            image: productData.image ? this.app.url.toMediaUrl(productData.image) : [],
            name: productData.name,
            description: productData.description || (product.parent.data as IProductDataV3).description,
            size: optionLabelPipe(productData.size, 'size'),
            offers: {}
        };

        if (productData.price) {
            data.offers = {
                '@type': StructuralData.Type.offer,
                priceCurrency: this.app.data.currency.base,
                price: productData.price ? productData.price.toString() : '',
                itemCondition: StructuralData.ItemCondition.new,
                availability: this.app.meta.getProductAvailability(product.id) as StructuralData.ItemAvailability
            };
        }

        return data;
    }

    override getOrganizationData(): StructuralData.IOrganizationData {
        const superData = super.getOrganizationData();

        superData.sameAs = [
            'https://www.facebook.com/oliverbonas',
            'https://www.twitter.com/oliverbonas',
            'https://www.instagram.com/oliverbonas',
            'https://www.pinterest.com/oliverbonas',
            'https://www.oliverbonas.com/blog'
        ];

        return superData;
    }

    /**
     * Skip reviews
     */
    override addOrganizationData() {
        const data = this.getOrganizationData();

        this.add(data);
    }

    protected override setListingTracking() {
        // do not set tracking for listing
    }

    protected override getAppData(): StructuralData.IAppData {
        const superAppData = super.getAppData();

        superAppData.potentialAction['query-input'] = 'search_term_string';

        return superAppData;
    }

    override getProductData(product: Product<IProductDataV3 | IProductDataV2>): any {
        const productData = product.data as IProductDataV3;
        const meta = productData.meta;

        const data: StructuralData.IProductData | StructuralData.IGroupProductData = {
            '@context': 'http://schema.org',
            '@type': product.isConfigurable ? StructuralData.Type.productGroup : StructuralData.Type.product,
            name: productData.name,
            sku: productData.sku,
            url: this.url.toUrl(productData.url || '')
        };

        if (product.isSimple && product.parent.data?.sku && product.parent.id !== product.id) {
            data.isVariantOf = {
                name: product.parent.data?.name,
                '@type': StructuralData.Type.productGroup,
                '@id': product.parent.data?.sku?.toString()
            };
        }

        if (product.isConfigurable) {
            data.productGroupID = product.parent.data?.sku?.toString() || product.data?.sku?.toString();
            data.variesBy = ['https://schema.org/size'];

            const productChildren = product.children as ProductDecorator<IProductDataV3>[];
            const uniqueChildren = productChildren.filter(
                (child, index, self) => index === self.findIndex(c => c.data?.sku === child.data?.sku)
            );

            data.hasVariant = uniqueChildren.map(child =>
                this.getProductChildData(child as ProductDecorator<IProductDataV3>)
            ) as StructuralData.IProductData[];
        }

        const image = meta ? meta.ogimage || productData.image : productData.image;
        if (image) {
            data.image = [this.app.url.toMediaUrl(image)];
        }

        if (meta && meta.description) {
            data.description = meta.description;
        }

        const brand = product.brand;
        if (brand) {
            data.brand = {
                '@type': StructuralData.Type.brand,
                name: brand
            };
        }

        if (productData.price) {
            data.offers = {
                '@type': StructuralData.Type.offer,
                priceCurrency: this.app.data.currency.base,
                price: productData.price ? productData.price.toString() : '',
                itemCondition: StructuralData.ItemCondition.new,
                availability: this.app.meta.getProductAvailability(product.id) as StructuralData.ItemAvailability
            };
        }

        return data;
    }
}

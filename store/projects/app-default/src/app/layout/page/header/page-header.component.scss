@use '@tomandco/atomic/scss/atom/color';
@use '@tomandco/atomic/scss/core';
@use 'styles/icons';

:host {
    sticky-free {
        ::ng-deep .content {
            z-index: 10;
        }

        &.is-sticky {
            ._header-inner {
                background-color: #fff;
            }
        }
    }

    ._header {
        display: block;
        transition: transform 0.45s ease;
        will-change: transform;

        &:hover ._header-inner {
            background-color: #fff;
            transition: background-color 50ms ease-in 0ms;
            will-change: background-color;
        }

        &:not(.page-header-transparent) ._header-inner {
            background-color: #fff;

            ._custom-search {
                background-color: #fff;
            }
        }

        &.is-meganav-open,
        .size-x.search-is-focus &,
        .size-l.search-is-focus & {
            ._header-inner {
                background-color: #fff;
                transition: background-color 50ms ease-in 0ms;
            }
        }

        &.search-is-focus,
        .size-m.search-is-focus &,
        .size-s.search-is-focus & {
            ._header-inner {
                background-color: #fff !important;
            }
        }

        .icon-nav span {
            transition: background-color var(--page-header-transition);
        }
    }

    ._header-inner {
        display: grid;
        align-items: center;
        background-color: transparent;
        grid-template-areas: 'logo meganav icons' 'navigation navigation navigation' 'search search search';
        grid-template-columns: 1fr auto 5fr;
        grid-template-rows: 1fr;
        transition:
            background-color var(--page-header-transition),
            color var(--page-header-transition),
            opacity var(--page-header-transition);

        .size-x &,
        .size-l & {
            height: var(--page-header-height);
        }

        .size-s &,
        .size-m & {
            grid-template-areas: 'left-icons logo icons' 'search search search';
            grid-template-columns: auto 1fr auto;
        }
    }

    ._logo {
        position: relative;
        z-index: 2;
        width: 153px;
        height: 14px;
        margin-right: 0.5rem;
        margin-left: 0.5rem;
        grid-area: logo;

        .size-m & {
            width: 180px;
            height: 20px;
            margin-right: 0.5rem;
            margin-left: 0.5rem;
        }

        .size-l & {
            width: 200px;
            height: 22.219px;
            margin-right: 1.5rem;
            margin-left: 0;
        }

        .size-x & {
            width: 240px;
            height: 26.656px;
            margin-right: 1.5rem;
            margin-left: 0;
        }
    }

    ._icons {
        grid-area: icons;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        text-align: right;
        white-space: nowrap;
        height: 64px;
        margin-right: -0.875rem;

        ._icon-search-close {
            display: none;
        }

        .search-is-focus & {
            ._icons {
                pointer-events: none;
            }

            *:not(._icon-search-close) {
                opacity: 0;
                pointer-events: none;
            }

            ._icon-search-close {
                line-height: 1;
                display: inline-block;
            }
        }

        .size-s &,
        .size-m & {
            height: 48px;
            margin-right: -0.5rem;
        }
    }

    ._icon {
        display: inline-flex;
        align-items: center;
        justify-content: flex-end;
        padding: 8px;
        cursor: pointer;

        search-header & {
            min-width: 48px !important;
        }

        ::ng-deep [class^='icon-'],
        ::ng-deep [class*=' icon-'] {
            display: inline-flex;
            height: 24px;
            font-size: 24px;
        }

        &.busy {
            .icon-nav {
                @extend .icon-loading !optional;
                @include icons.icons;

                span {
                    opacity: 0;
                }
            }
        }

        .size-s &,
        .size-m & {
            padding: 5px;
        }
    }

    meganav {
        display: inline-block;
        grid-area: meganav;
    }

    meganav-content {
        grid-area: navigation;
    }

    checkout-nav {
        grid-area: meganav;

        .size-s &,
        .size-m & {
            grid-area: search;
        }
    }

    search-header {
        position: absolute;
        pointer-events: none;
    }

    search-outlet {
        grid-area: search;
    }

    ._custom-search {
        grid-area: search;
    }

    .icon-nav {
        .size-s & {
            width: 18px !important;
            height: 17px !important;
        }

        .size-m & {
            width: 14px !important;
            height: 21px !important;
        }
    }

    ._meganav-button {
        height: 25px;
        margin-left: 1.5em;
        grid-area: left-icons;

        .size-s & {
            padding-right: 0.5rem;
            padding-left: 0.5rem;
            margin-left: -0.25rem;
        }

        .size-m & {
            margin-left: 0;
            margin-left: -0.25rem;
        }
    }

    :host-context(body.page-header-transparent) {
        ._header {
            width: 100%;
            height: 0;
            float: left;
            transition:
                transform var(--page-header-transition),
                height 0ms ease 1000ms;
        }
    }

    :host-context(body.page-header-transparent.is-touchy:not(.search-is-focus)),
    :host-context(body.page-header-transparent:not(.search-is-focus)) &:not(:hover) {
        sticky-free:not(.is-sticky) ::ng-deep ._header.page-header-light-mode:not(.is-meganav-open) {
            color: #fff;

            .icon-nav span {
                background-color: #fff;
            }

            ._logo {
                filter: brightness(0) invert(1);
            }
        }

        sticky-free:not(.is-sticky) ::ng-deep ._header:not(.is-meganav-open) {
            ._header-inner {
                background-color: transparent;
                transition: background-color var(--page-header-transition);
            }

            .icon-nav span {
                background-color: #000;
                transition: background-color var(--page-header-transition);
            }
        }

        @media (hover: hover) {
            &:hover {
                ._header-inner {
                    background-color: #fff;
                }
            }
        }
    }

    :host-context(body.size-s.product-listing-filters:not(.is-scrolling-stopped)),
    :host-context(body.size-m.product-listing-filters:not(.is-scrolling-stopped)),
    :host-context(body.page-header-hide-up) {
        sticky-free.is-sticky.is-scroll-down ::ng-deep ._header:not(.is-meganav-open) {
            transform: translateY(calc(var(--element-height-page-header) * -1));
            transition: transform 0.8s ease;
        }
    }

    :host-context(body.block-scrolling-by-search) sticky-free.is-sticky.is-scroll-down ::ng-deep ._header:not(.is-meganav-open) {
        transform: translateY(0) !important;
    }

    :host-context(body.size-s:not(.is-sticky-up, .block-scrolling-by-search, .is-scrolling-stopped)),
    :host-context(body.size-m:not(.is-sticky-up, .block-scrolling-by-search, .is-scrolling-stopped)) {
        sticky-free.is-sticky ::ng-deep ._header._enabled-search:not(.is-meganav-open) {
            transform: translateY(-100%);
            transition: transform 0.5s ease;
        }
    }

    :host-context(body.size-s.is-sticky-up, body.size-s.is-scrolling-stopped),
    :host-context(body.size-m.is-sticky-up, body.size-m.is-scrolling-stopped) {
        sticky-free.is-sticky ::ng-deep ._header._enabled-search:not(.is-meganav-open) {
            transform: translateY(0%);
            transition: transform 0.5s ease;
        }
    }

    body:not(.product-listing-filters) ._header.page-header-transparent {
        height: 0;
    }

    ._header-checkout {
        border-bottom: 1px solid color.get(34);
        box-shadow: 0 0 3px color.get(5);

        ._header-inner {
            grid-template-columns: 1fr auto 1fr;

            .size-m &,
            .size-s & {
                grid-template-columns: auto 2fr auto;
                justify-items: center;
            }
        }

        ._basket-qty {

            .size-s &,
            .size-m & {
                padding-right: 1rem;
                grid-area: icons;
            }
        }

        ._icons {

            .size-s &,
            .size-m & {
                padding-top: 10px;
                padding-bottom: 10px;
                padding-left: 1rem;
                margin-right: 0;
                grid-area: left-icons;
            }
        }
    }

    wishlist-notice i {
        top: core.px-to-rem(3);
    }

    ._custom-input {
        height: 36px;
        border: 1px solid color.get(4);
        border-radius: core.px-to-rem(10);
        background-color: color.get(33);
        transition: border-color 0.5s ease-in-out;

        .page-header-transparent & {
            border-color: color.get(4);
            background-color: rgba(color.get(33), 0.7);
        }

        .page-header-transparent.page-header-light-mode & {
            border-color: #fff;
        }

        sticky-free.is-sticky &,
        body.size-s:not(.is-sticky-up) sticky-free.is-sticky & {
            border-color: color.get(1);
        }
    }

    ._search {
        padding-left: 15px;
        top: 6px;

        .size-s &,
        .size-m & {
            padding-left: 12px;
        }
    }
}
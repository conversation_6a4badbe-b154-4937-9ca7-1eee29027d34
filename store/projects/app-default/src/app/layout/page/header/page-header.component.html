@let isCheckoutProcess = isCheckoutProcess$ | async;
@let searchConfig = searchConfig$ | async;

@if (searchConfig) {
@if (!isCheckoutProcess) {
@if (!!promoBar) {
<promo-bar class="block _promo-bar"
           [data]="promoBar"
           [elementHeight]="'promo-bar'" />
}
<sticky-free [stickyId]="'page-header-sticky'"
             [stickyOff]="client.isCms"
             [order]="0">
    <header class="_header"
            [class._enabled-search]="hasEnableMobileSearch"
            id="page-header"
            [attr.data-cs-override-id]="!app.isPlatformBrowser ? 'universal-page-header' : undefined"
            [pageHeaderBody]="true"
            [toggleClass]="'is-meganav-open'"
            [toggleClassBy]="meganavToggleGroupId"
            [elementHeight]="'page-header'"
            [elementHeightOptions]="{ topOffset: true, local: false }">
        <div class="wrap p-t-2-m p-t-2-s p-b-3-s _header-inner"
             [class.p-b-2-s]="client.isS && !hasEnableMobileSearch"
             [elementHeight]="'page-header-content'">
            @if (client.isS || client.isM || offCanvasOnDesktop()) {
            <!-- menu, search / S, M -->
            <button class="_icon _meganav-button"
                    (click)="openOffCanvas()">
                <i class="icon-nav">
                    <span></span>
                    <span></span>
                    <span></span>
                </i>
            </button>
            }
            <!-- logo -->
            <a class="inline-block _logo"
               [routerLink]="'/'">
                <img [dfImage]="content?.logo_image"
                     [width]="client.isS ? 153 : client.isM ? 180 : client.isL ? 200 : 240"
                     [height]="client.isS ? 14 : client.isM ? 20 : client.isL ? 22.219 : 26.656"
                     [alt]="content?.logo_alt"
                     [placeholder]="false"
                     fill>
            </a>
            <ng-container *ifSize="'LX'">
                <div class="no-s no-m _icons"
                     id="header-icons">
                    <!-- search icon -->
                    <button class="_icon"
                            role="button"
                            [clickEvent]="'search.focus'"
                            [attr.aria-label]="'Search' | translate">
                        <i class="icon-simple-search"
                           aria-hidden="true"
                           cy-searchBtn></i>
                    </button>
                    <!-- location icon -->
                    <a class="_icon"
                       [routerLink]="'/about-us/store-locator'"
                       [attr.aria-label]="'Change location' | translate">
                        <i aria-hidden="true"
                           storeLocatorIcon></i>
                    </a>
                    <ng-container *ifNotSagepayCloudPaymentActive>
                        <!-- my account -->
                        <a class="_icon"
                           [routerLink]="'/account'"
                           [routerLinkActive]="'is-active'"
                           *ifLoggedIn>
                            <i class="icon-account-header"
                               cy-loginBtn
                               aria-hidden="true"></i>
                        </a>
                        <!-- sign in -->
                        <button class="_icon"
                                role="button"
                                type="button"
                                [iCmsModalOpen]="'login-form-modal'"
                                *ifNotLoggedIn>
                            <i class="icon-account-header"
                               cy-loginBtn
                               aria-hidden="true"></i>
                        </button>
                    </ng-container>
                    <!-- wishlist -->
                    <wishlist-qty class="_icon" />
                    <ng-container *ifSagepayCloudPaymentActive>
                        <sagepay-cloud-payment-current-name class="_icon"
                                                            [charsToDisplay]="17" />
                    </ng-container>
                    <!-- basket -->
                    <basket-qty class="_icon"
                                minibasketToggle />
                </div>
                <!-- meganav -->
                <meganav class="no-s no-m"
                         [items]="content?.menu || []" />
                <!-- directly embedded here, on S its a modal -->
                <search-header class="no-s no-m" />

                <!-- meganav content -->
                <ng-container *afterContentInit>
                    <meganav-content class="block"
                                     [content]="content"
                                     [items]="content?.menu || []"
                                     #cmsNodes />
                </ng-container>
            </ng-container>
            <ng-container *ifSize="'SM'">
                <!-- menu / S, M -->
                <div class="no-l no-x _icons"
                     [elementHeight]="'page-header-inner'">
                    <!-- name of the chosen store -->
                    @if (client.isM) {
                    <ng-container *ifSagepayCloudPaymentActive>
                        <sagepay-cloud-payment-current-name class="_icon"
                                                            [charsToDisplay]="17" />
                    </ng-container>
                    }

                    <!-- search icon -->
                    @if (!hasEnableMobileSearch || client.isM) {
                    <button class="_icon"
                            role="button"
                            type="button"
                            (click)="openSearch('Navigation')">
                        <i class="icon-simple-search"
                           cy-searchBtn
                           aria-hidden="true"></i>
                    </button>
                    }
                    <!-- account -->
                    <a class="inline-flex _icon"
                       [routerLink]="'/account'"
                       [attr.aria-label]="'Account' | translate"
                       *ifLoggedIn>
                        <i class="icon-account-header inline-flex"
                           aria-hidden="true"></i>
                    </a>
                    <button class="inline-flex _icon"
                            type="button"
                            [iCmsModalOpen]="'login-form-modal'"
                            *ifNotLoggedIn>
                        <i class="icon-account-header inline-flex"
                           aria-hidden="true"></i>
                    </button>
                    <!-- wishlist -->
                    <wishlist-qty class="_icon" />
                    <!-- basket -->
                    <basket-qty class="_icon"
                                [minibasketToggle]
                                [iCmsModalOpen]="minibasketModalId" />

                    <!--search close button-->
                    <button class="p-a-2 p-t-0 p-b-0 cursor-pointer _icon-search-close"
                            [attr.aria-label]="'Close search' | translate"
                            (click)="closeSearch()"
                            type="button">
                        <i class="icon-close _icon-search-close"
                           aria-hidden="true"></i>
                    </button>
                </div>
                @if (client.isS && (hasEnableMobileSearch)) {
                <div class="pos-relative w-12 m-t-3 p-b-3 _custom-search"
                     role="button"
                     tabindex="0"
                     type="button"
                     (click)="openSearch('Homepage')"
                     (keypress)="openSearch('Homepage')">
                    <span class="pos-absolute left-1 p-l-3 flex flex-middle flex-justify-center _search">
                        <i class="col-1 icon-simple-search"
                           [style.height.px]="24"
                           [style.font-size.px]="24"
                           cy-searchInputSubmit
                           aria-hidden="true"></i>
                    </span>
                    <input class="_custom-input pe-none"
                           type="text"
                           cy-searchInput
                           #input />
                </div>
                }
            </ng-container>

            <!-- search content -->
            <search-outlet class="block ng-hide-animate ng-hide"
                           [class._disabled-mobile-search-form]="!hasEnableMobileSearch"
                           [toggleClass]="'!ng-hide'"
                           [toggleClassBy]="[searchOutletToggleId, 'header']"
                           [searchId]="'header'"
                           [hideOnLocationChange]="true"
                           [enableMobileSearch]="hasEnableMobileSearch"
                           vhFix
                           [onlyCustomProperties]="true"
                           #cmsNodes />
        </div>
    </header>
</sticky-free>
} @else {
<sticky-free [stickyId]="'page-header-checkout'"
             [stickyOff]="client.isCms"
             [order]="0">
    <header class="bg-col-w _header _header-checkout"
            [pageHeaderBody]="true"
            [elementHeight]="'page-header'"
            [elementHeightOptions]="{ topOffset: true, local: false }">
        <div class="wrap p-l-2-m p-l-2-s p-r-2-m p-r-2-s _header-inner">
            <!-- basket qty -->
            <a class="_icon _basket-qty"
               [routerLink]="'/basket'"
               *ifSize="'SM'">
                <basket-qty minibasketToggle />
            </a>
            <a class="inline-block m-t-4-m m-t-5-s m-b-4-m m-b-4-s _logo"
               [routerLink]="'/'">
                <img [dfImage]="content?.logo_image"
                     [alt]="content?.logo_alt"
                     [placeholder]="false"
                     fill>
            </a>
            <span class="flex flex-middle flex-justify-end col-21 _icons">
                <span class="m-r-3 s1 fw-bold"
                      *ifSize="'LX'">{{'Secure checkout' | translate}}</span>
                <i class="icon-secure-lock fs-7"
                   aria-hidden="true"></i>
            </span>
            <checkout-nav class="inline-block m-t-1-m m-t-1-s p-b-9-m p-b-9-s" />
        </div>
    </header>
</sticky-free>
}
}
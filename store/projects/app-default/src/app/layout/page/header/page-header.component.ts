import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, input, Input, OnDestroy, OnInit } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { IConfigData } from '@df/catalog/config/config.interfaces';
import { App } from '@df/core/app';
import { IMetaDataExtended, Meta } from '@df/core/meta/meta';
import { Events } from '@df/core/service/event';
import { ElementHeightDirective } from '@df/dom/element-height/element-height.directive';
import { GtmService } from '@df/module-gtm/gtm.service';
import { DfImageDirective } from '@df/module-image/df-image.directive';
import { Customer } from '@df/session/api/customer';
import { BodyClassService } from '@df/ui/body-class.service';
import { AfterContentInitDirective, AfterContentInitDirectiveService } from '@df/ui/common/after-content-init.directive';
import { ClickEventDirective } from '@df/ui/common/click-event.directive';
import { IfSizeDirective } from '@df/ui/common/if-size.directive';
import { ToggleModule } from '@df/ui/toggle/toggle.module';
import { ToggleService } from '@df/ui/toggle/toggle.service';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ScrollService } from '@df/util/service/scroll.service';
import { ICmsModalService } from '@icms/client/assets/modal/icms-modal.service';
import { ICmsClientModule } from '@icms/client/icms-client.module';
import { ICmsComponent, ICmsContent, NICmsContentComponent } from '@icms/core/content';
import { ICmsConfig } from '@icms/core/resource/config/icms-config';
import { ICmsConfigResource } from '@icms/core/resource/config/icms-config.resource';
import find from 'lodash-es/find';
import { debounceTime, filter, map, of, switchMap, tap } from 'rxjs';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { appComponents } from '../../../app.config';
import { BasketQtyComponent } from '../../../basket/basket-qty.component';
import { MinibasketToggleDirective } from '../../../basket/minibasket-toggle.directive';
import { WishlistQtyComponent } from '../../../catalog/ui/wishlist/qty/wishlist-qty.component';
import { CheckoutNavComponent } from '../../../checkout/checkout-nav.component';
import { AbstractCmsBlockComponent } from '../../../cms/block/abstract-cms-block.component';
import { CMS_CONFIG_OPEN_SEARCH, ICmsConfigOpenSearchDataContent } from '../../../cms/config/open-search.cms-config';
import { StickyFreeComponent } from '../../../common/sticky-free/sticky-free.component';
import { isCustomerAllowed } from '../../../customer/groups/customer-groups';
import { IfNotSagepayCloudPaymentActiveDirective } from '../../../module/sagepay/cloud-payment/if-not-sagepay-cloud-payment-active.directive';
import { IfSagepayCloudPaymentActiveDirective } from '../../../module/sagepay/cloud-payment/if-sagepay-cloud-payment-active.directive';
import { SagepayCloudPaymentCurrentNameComponent } from '../../../module/sagepay/cloud-payment/sagepay-cloud-payment-current-name.component';
import { IfLoggedInDirective } from '../../../module/session/if-logged-in.directive';
import { IfNotLoggedInDirective } from '../../../module/session/if-not-logged-in.directive';
import { SearchHeaderComponent } from '../../../search/header/search-header.component';
import { SEARCH_OUTLET_MODAL_ID, SEARCH_OUTLET_TOGGLE_ID, SearchOutletComponent } from '../../../search/outlet/search-outlet.component';
import { IfCheckoutProcessService } from '../../../ui/common/if-checkout-process.service';
import { StoreLocatorIconDirective } from '../../../ui/display/store-location-icon.directive';
import { VhFixDirective } from '../../../ui/display/vhfix/vh-fix.directive';
import { MeganavContentComponent } from '../../meganav/content/meganav-content.component';
import { MeganavComponent } from '../../meganav/meganav.component';
import { NOffCanvas } from '../../off-canvas/off-canvas.interface';
import { PromoBarComponent } from '../promo-bar/promo-bar.component';
import { PageHeaderBodyDirective } from './page-header-body.directive';
import { PAGE_HEADER_CONFIG } from './page-header.config';
import { NPageHeader } from './page-header.interface';
import { PageHeaderService } from './page-header.service';

@Component({
    selector: 'page-header',
    templateUrl: './page-header.component.html',
    styleUrl: './page-header.component.scss',
    standalone: true,
    imports: [
        ...appComponents,
        AfterContentInitDirective,
        BasketQtyComponent,
        CheckoutNavComponent,
        ClickEventDirective,
        CommonModule,
        DfImageDirective,
        ElementHeightDirective,
        ICmsClientModule,
        IfLoggedInDirective,
        IfNotLoggedInDirective,
        IfNotSagepayCloudPaymentActiveDirective,
        IfSagepayCloudPaymentActiveDirective,
        IfSizeDirective,
        MeganavComponent,
        MeganavContentComponent,
        MinibasketToggleDirective,
        PageHeaderBodyDirective,
        PromoBarComponent,
        RouterModule,
        SagepayCloudPaymentCurrentNameComponent,
        SearchHeaderComponent,
        SearchOutletComponent,
        StickyFreeComponent,
        StoreLocatorIconDirective,
        ToggleModule,
        TranslatePipe,
        VhFixDirective,
        WishlistQtyComponent
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
@ICmsComponent<PageHeaderComponent>(PAGE_HEADER_CONFIG)
export class PageHeaderComponent extends AbstractCmsBlockComponent implements NICmsContentComponent.IComponent, OnInit, OnDestroy {
    @ICmsContent({
        type: 'blockContent'
    })
    override content!: NPageHeader.IBlockDataContent;

    @Input({ required: true })
    offCanvasState!: BehaviorSubject<NOffCanvas.EOpenState>;

    offCanvasOnDesktop = input<boolean>();

    enableMobileSearch?: boolean | undefined;
    lastScrollTop = 0;
    hasContent$ = new BehaviorSubject<boolean>(false);
    nodes$ = new BehaviorSubject<HTMLElement[]>([]);
    promoBar: NPageHeader.IPageHeaderPromoBarData | undefined;
    searchConfig$ = new BehaviorSubject<boolean>(false);

    readonly searchOutletToggleId = SEARCH_OUTLET_TOGGLE_ID;
    readonly minibasketModalId = 'minibasket';
    readonly meganavToggleGroupId = NPageHeader.MEGANAV_GROUP_ID;
    protected autoExpandTimeout?: number;
    protected openSearchConfig?: string[];

    /**
     * Fallback to old saved data
     */
    protected readonly transparent = false; // this.content?.headerTransparent === undefined ? (<any>this.content)?.transparent : this.content?.headerTransparent;

    protected readonly lightMode = false; // this.content?.headerLightMode === undefined ? (<any>this.content)?.light_mode : this.content?.headerLightMode;

    protected readonly hideUp = false; // this.content?.headerHideUp === undefined ? (<any>this.content)?.hide_up : this.content?.headerHideUp;

    protected app = inject(App);
    protected toggleService = inject(ToggleService);
    protected cmsModalService = inject(ICmsModalService);
    protected events = inject(Events);
    protected customer = inject(Customer);
    protected meta = inject(Meta);
    protected pageHeaderService = inject(PageHeaderService);
    protected router = inject(Router);
    protected scrollService = inject(ScrollService);
    protected bodyClassService = inject(BodyClassService);
    protected destroyRef = inject(DestroyRef);
    protected isCheckoutProcess$ = toObservable(inject(IfCheckoutProcessService).hasView);
    protected gtmService = inject(GtmService);
    protected afterContentInitDirectiveService = inject(AfterContentInitDirectiveService);
    protected cmsConfigResource = inject(ICmsConfigResource);

    get hasEnableMobileSearch(): boolean {
        if (!this.searchConfig$.value) {
            return false;
        }

        const pageType = (this.app.meta?.model?.data as IMetaDataExtended)?.pageType || this.app.meta?.model?.type;

        if (pageType === 'default') {
            if (this.meta.model?.data?.url === '/' && this.openSearchConfig?.includes('homepage')) {
                return true;
            }
            if (this.meta.model?.data?.url !== '/' && this.openSearchConfig?.includes('cms-page')) {
                return true;
            }
        }

        if (pageType === 'search') {
            return false;
        }

        if (pageType && this.openSearchConfig?.includes(pageType)) {
            return true;
        }

        return false;
    }

    get isOnCategoryPage(): boolean {
        return this.meta?.model?.type === 'category';
    }

    override async ngOnInit(): Promise<void> {
        super.ngOnInit();

        this.cmsConfigResource
            .loadModelByUrl(CMS_CONFIG_OPEN_SEARCH)
            .then(() => {
                const config = this.cmsConfigResource.getModelByUrl(CMS_CONFIG_OPEN_SEARCH) as ICmsConfig<
                    IConfigData<ICmsConfigOpenSearchDataContent>
                >;
                this.openSearchConfig = config?.data?.content?.enableOpenSearch;

                this.searchConfig$.next(true);
                this.detectChanges();
            })
            .finally(() => {
                if (!this.searchConfig$.value) {
                    this.searchConfig$.next(true);
                    this.detectChanges();
                }
            });

        this.isCheckoutProcess$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
            this.detectChanges();
        });

        this.subscriptions.add(
            this.pageHeaderService.getEnableMobileSearch().subscribe(value => {
                this.enableMobileSearch = value;
                this.detectChanges();
            })
        );

        this.subscriptions.add(
            this.toggleService.getState([this.searchOutletToggleId, 'header']).subscribe(state => {
                if (state) {
                    this.toggleService.closeGroup(this.meganavToggleGroupId);
                    this.sendGtmEvent('openSearch');

                    if (this.client.isS || this.client.isM) {
                        this.cmsModalService.open(undefined, SEARCH_OUTLET_MODAL_ID);
                    }
                }
            })
        );

        const initSubscription = this.afterContentInitDirectiveService.ready.subscribe(ready => {
            if (ready) {
                this.detectChanges();
            }

            this.timeoutService.setTimeout(() => {
                initSubscription.unsubscribe();
            }, 0);
        });

        this.subscriptions.add(
            this.meta.subject
                .pipe(
                    map(state => state?.model?.type === 'category'),
                    switchMap(value => {
                        return value
                            ? this.scrollService.scroll$.pipe(
                                tap(() => {
                                    clearTimeout(this.autoExpandTimeout);
                                    this.autoExpandTimeout = undefined;

                                    this.bodyClassService.remove('is-scrolling-stopped', this);
                                }),
                                debounceTime(100),
                                map(() => {
                                    this.autoExpandTimeout = this.timeoutService.setTimeout(() => {
                                        this.bodyClassService.add('is-scrolling-stopped', this);
                                    }, 2000);
                                })
                            )
                            : of(null);
                    })
                )
                .subscribe()
        );

        this.subscriptions.add(this.scrollService.isScrollDown$.subscribe(value => this.scrollDown(!!value)));
        this.subscriptions.add(this.customer.model.obs.status.subscribe(() => this.detectChanges()));

        this.subscriptions.add(
            this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {
                this.timeoutService.setTimeout(() => this.detectChanges(), 500);
            })
        );

        this.nodes$.next([this.elementRef.nativeElement]);
    }

    override ngOnDestroy(): void {
        super.ngOnDestroy();

        this.bodyClassService.remove('is-sticky-up', this);
    }

    openSearch(location?: string): void {
        this.toggleService.open([this.searchOutletToggleId, 'header']);
        this.sendGtmEvent('openSearch' + location);
    }

    closeSearch(): void {
        this.events.broadcast('search.open', false);
    }

    toggleCmsNode(element: HTMLElement, state: boolean) {
        const nodes = this.nodes$.value;

        if (state) {
            if (!nodes.includes(element)) {
                this.nodes$.next([...nodes, element]);
            }
        } else {
            if (nodes.includes(element)) {
                this.nodes$.next(nodes.filter(node => node !== element));
            }
        }
    }

    openOffCanvas(): void {
        this.offCanvasState.next(NOffCanvas.EOpenState.opened);
        this.sendGtmEvent('openOffCanvas');
    }

    override detectChanges() {
        if (this.initiated && this.searchConfig$.value) {
            const hasContent = !!this.content;

            if (this.hasContent$.value !== hasContent) {
                this.changeDetectorRef.detectChanges();
                this.hasContent$.next(!!hasContent);
                return;
            }
        }

        this.promoBar = this.promobarData();

        super.detectChanges();
    }

    protected scrollDown(value: boolean) {
        if (value) {
            this.bodyClassService.set('is-sticky-up', this, false);
        } else {
            this.bodyClassService.set('is-sticky-up', this, true);
        }
    }

    private sendGtmEvent(event: string): void {
        this.gtmService.push({ event });
    }

    private promobarData(): NPageHeader.IPageHeaderPromoBarData | undefined {
        const promobars = this.content?.promobars;

        if (promobars?.length) {
            const groupId = this.customer.model.data?.group_id;

            return find(promobars, promobar => {
                if (promobar.turnOn && isCustomerAllowed(promobar.visibility, groupId)) {
                    return !!find(
                        promobar.promobarLines,
                        line =>
                            !!find(line.lineItems, item => {
                                switch (item.item_type) {
                                    case 'text_or_link':
                                        return !!item.text;
                                    case 'countdown':
                                        return !!item.end_date && new Date(item.end_date) > new Date();
                                    default:
                                        return false;
                                }
                            })
                    );
                }

                return false;
            }) as NPageHeader.IPageHeaderPromoBarData;
        }

        return undefined;
    }
}

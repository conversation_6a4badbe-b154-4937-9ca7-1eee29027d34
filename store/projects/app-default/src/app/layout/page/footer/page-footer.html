@if (content && !hidden) {
<div [style.background-color]="content?.backgroundColor"
     [elementHeight]="'page-footer-wrapper'">
    <footer class="w-12 bg-col-w footer"
            id="page-footer">
        <div class="b-b b-t-x b-t-l b-col-4">
            <!-- size XL -->
            @if (client.isL || client.isX) {
            <div [grid]="12"
                 [gridRowGap]="0"
                 [gridColumnGap]="0">
                @for (column of content.columns; track $index) {
                <page-footer-column class="m-b-0 b-r b-col-4 p-t-2 p-t-4-m"
                                    [ngClass]="$first ? 's1 col-11' : 'p2 col-12'"
                                    [gridColumn]="3"
                                    [column]="column"
                                    [isFirst]="$first"
                                    [isLast]="$last"
                                    [index]="$index" />
                }
                <ng-container *ngTemplateOutlet="newsletter" />
            </div>
            } @else {
            <ng-container *ngTemplateOutlet="newsletter" />
            <accordion>
                @for (column of content.columnsMobile; track $index) {
                <accordion-item [value]="`footerDropdown-${$index}`"
                                [icon]="column.links.length > 0">
                    <ng-container ngProjectAs="[heading]">
                        <href [link]="column.link">
                            <span class="flex flex-middle _title"
                                  [style.gap.px]="8">
                                <i class="_icon"
                                   [ngClass]="'icon-' + column.icon"></i>
                                <span class="p1"
                                      *iCms="'header' of column">{{column.header}}</span>
                            </span>
                        </href>
                    </ng-container>
                    <page-footer-column-mobile [column]="column" />
                </accordion-item>
                }
                <multisite-switch-mobile [value]="`footerDropdown-${content.columnsMobile.length + 1}`" />
            </accordion>
            <div class="flex flex-justify-between flex-middle b-t p-t-1 p-b-1 p-l-5 p-r-5">
                <!-- social links-->
                <div class="w-5">
                    @for (item of content.socialIcons; track $index) {
                    <href [link]="item.link">
                        <span [ngClass]="$first ? 'm-r-1' : $last ? 'm-l-1' : 'm-l-1 m-r-1'">
                            <i class="col-12"
                               [ngClass]="'icon-' + item.service"></i>
                        </span>
                    </href>
                    }
                </div>

                <!-- reviews-->
                <href class="contents"
                      [link]="content.reviewsLink"
                      [hrefClass]="'w-9 w-6-m'">
                    <div class="flex flex-middle flex-justify-between flex-justify-end-m flex-justify-end-s"
                         [ngClass]="{'w-9 w-6-m': !content.reviewsLink}">
                        @if (content.reviewsImage) {
                        <div class="w-5 w-3-m w-4-s m-l-1 m-r-2-m m-r-2-s">
                            <img class="w-12"
                                 [dfImage]="content.reviewsImage"
                                 [ratio]="'2/1'"
                                 alt="">
                        </div>
                        }
                        @if (rating > 0) {
                        <div class="center">
                            <p class="c1">
                                {{'Read our % reviews' | translate : app.data.reviews.reviews_count}}
                            </p>
                            <rating [value]="rating" />
                        </div>
                        }
                    </div>
                </href>
            </div>
            }
        </div>

        <!-- section bottom -->
        <div class="wrap">
            <div
                 class="flex flex-column-m flex-column-s flex-justify-between flex-middle col-13 c1 m-t-2 m-b-2 m-a-0-s">
                <!-- copyright-->
                <div class="flex"
                     *ifSize="'LX'">
                    <div>© Oliver Bonas {{copyrightDate}}</div>
                    <!-- bottom links / text-->
                    @for (item of content.links; track $index) {
                    <href [link]="item.link"
                          [hrefClass]="'link'">
                        <div class="m-l col-13">{{item.text}}</div>
                    </href>
                    }
                </div>
                <!-- payment logos -->
                <div
                     class="payment-logos flex-middle flex-justify-center-s m-t-4-s m-b-4-s m-t-2-m m-b-2-m m-r-16 m-r-0-m m-r-0-s">
                    <div class="logo-visa"></div>
                    <div class="logo-ae"></div>
                    <div class="logo-mc"></div>
                    <div class="logo-paypal"></div>
                    <div class="logo-maestro"></div>
                    <div class="logo-applepay"></div>
                </div>

                <!-- copyright -->
                <div class="w-12-s c1 flex-s flex-justify-between-s flex-middle-s">
                    <div *ifSize="'SM'">© Oliver Bonas {{copyrightDate}}</div>
                    <div>
                        Made by
                        <a class="link-2 col-13"
                           href="https://www.tomandco.co.uk/"
                           target="_blank"
                           title="A London based Magento E-commerce Agency">Tom&Co Magento E-commerce</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>
}

<ng-template #newsletter>
    <div class="p-l-7 p-r-10 p-r-6-m p-r-6-s p-t-7 p-t-4-m p-t-4-s p-b-5-m p-b-5-s"
         [style.background]="content.newsletterBackgroundColor"
         [gridColumn]="6">
        <div class="w-8 w-12-s flex flex-middle">
            <h2 class="h2 p-r-4"
                [ngClass]="content.newsletterHeaderColor || 'col-11'"
                *iCms="'newsletterHeader' of content"></h2>
            @if (content.newsletterHeaderImage) {
            <img class="w-1"
                 [dfImage]="content.newsletterHeaderImage"
                 ratio="1">
            }
        </div>
        <p class="p1"
           [ngClass]="content.newsletterCopyColor || 'col-12'"
           *iCms="'newsletterCopy' of content">
        </p>
        <div class="p-t-1 p-r-3-x p-r-3-l">
            <newsletter-form [hidePrivacyPolicy]="true"
                             [footerNewsletter]="true"
                             [source]="'footer_GDPR'" />
        </div>
        <p class="c1 p-t-2"
           [ngClass]="content.newsletterCopyColor || 'col-12'"
           forceTargetBlank
           [innerHTML]="content.newsletterTermsCopy"></p>
        <ng-container *ifSize="'XL'">
            <div class='w-8 m-t-6 flex flex-middle flex-justify-between'>
                <div class="flex">
                    @for (item of content.socialIcons; track $index) {
                    <href [link]="item.link">
                        <span [ngClass]="$first ? 'm-r-1' : $last ? 'm-l-1' : 'm-l-1 m-r-1'">
                            <i class="col-12"
                               [ngClass]="'icon-' + item.service"></i>
                        </span>
                    </href>
                    }
                </div>
                <href [style.display]="'contents'"
                      [link]="content.reviewsLink"
                      [hrefClass]="'w-7'">
                    <div class="flex flex-middle flex-justify-between"
                         [class.w-7]="!content.reviewsLink">
                        <img class="w-5"
                             [dfImage]="content.reviewsImage"
                             ratio="3/1">
                        @if (rating > 0) {
                        <div class="center">
                            <p class="c1">
                                {{'Read our % reviews' | translate : app.data.reviews.reviews_count}}
                            </p>
                            <rating [value]="rating" />
                        </div>
                        }
                    </div>
                </href>
            </div>
        </ng-container>
    </div>
</ng-template>
<div class="flex-column height-100 sign-up-popup-content"
     [class.is-open]="openMode"
     [ngClass]="!content.is_big_popup && content.position === 'bottom' ? 'flex-justify-end flex-bottom' : 'flex-justify-center flex-justify-end-s flex-middle'">
    @if (!content.is_big_popup) {
    @if (!openMode) {
    <div class="p-a-1 bg-col-23 b-radius-7 inline-flex flex-middle left animated-block overflow-hidden sign-up-popup-content__inner"
         [ngClass]="content.position === 'bottom' ? 'flex-justify-between float-right' : 'flex-justify-center'"
         [class.is-position-center]="content.position === 'center'">
        <div class="w-3 m-t-1">
            <button class="cursor-pointer"
                    role="button"
                    aria-label="toggle"
                    (click)="toggle()">
                <i class="fs-16 svg-icon--5-off"
                   aria-hidden="true"></i>
            </button>
        </div>

        <div class="sign-up-block overflow-hidden cursor-pointer"
             (click)="signIn()">
            <div *iCms="'popup_heading' of content; content: 'html'; skipEmpty: true"></div>
        </div>

        <!-- close -->
        <button class="m-r-1 cursor-pointer"
                role="button"
                aria-label="close"
                (click)="close()">
            <span class="fs-3 icon-close"
                  aria-hidden="true"></span>
        </button>
    </div>
    } @else {
    <div class="p-a-2 p-a-4-s float-right bg-col-35 b-radius-7 left sign-up-popup-content__inner"
         [class.is-position-center]="content.position === 'center'">
        <div class="flex flex-top">
            @if (content.icon) {
            <img class="w-3"
                 [dfImage]="content.icon"
                 [ratio]="1">
            }
            <div *iCms="'popup_heading' of content; content: 'html'; skipEmpty: true"></div>

            <!-- close -->
            <button class="w-1 cursor-pointer"
                    role="button"
                    aria-label="close"
                    (click)="close()">
                <span class="fs-3 icon-close"
                      aria-hidden="true"></span>
            </button>
        </div>

        <!-- Example of an e-mail input field, still without logic  -->
        <form class="m-t-2 m-t-4-s"
              method="POST"
              name="create_account_popup"
              #form="ngForm">
            <input-wrap [canShowValidate]="emailInputRef?.value?.length > 0 && form.submitAttempt"
                        [messages]="{
                                        email: ('Valid email is required' | translate),
                                        required: ('Valid email is required' | translate),
                                        LOQATE_INVALID_EMAIL: ('Valid email is required' | translate)
                                    }">
                <input class="input b-radius-7 b-a-0"
                       type="email"
                       name="email"
                       sl-input="email"
                       [attr.placeholder]="'Email address' | translate"
                       required
                       validatePattern="email"
                       [loqateEmail]="true"
                       [(ngModel)]="email"
                       [ngModelOptions]="{updateOn: 'blur'}"
                       (keyup)="handleKeyUp($event, form)"
                       (blur)="handleKeyUp()"
                       #emailInputRef>
            </input-wrap>

            <div class="m-t-2 m-t-4-s m-b-2">
                <button class="button w-12"
                        (click)="submit()">
                    <span>{{'Create an account' | translate}}</span>
                </button>
            </div>
        </form>
    </div>
    }
    } @else {
    <sign-up-popup-big class="w-12 sign-up-popup-big-content__inner"
                       [content]="content" />
    }
</div>
import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { SessionStorageService } from '@df/browser/storage/session-storage.service';
import { Category, ICategoryDataV2 } from '@df/catalog/category/category';
import { Logger } from '@df/core/service/logger';
import { MultisiteService } from '@df/core/service/multisite';
import { lastValueFrom } from 'rxjs';

export const NEW_CATEGORIES_AB_KEY = 'newCategories';

const NEW_CATEGORIES_URL = {
    uk: 'https://n8n.tomandco.uk/webhook/getNewCategories',
    ie: 'https://n8n.tomandco.uk/webhook/ie/getNewCategories',
    us: 'https://n8n.tomandco.uk/webhook/us/getNewCategories'
};

export interface NewCategoriesData {
    updatedAt: number;
    categories: {
        url: string;
        count: number;
        visited: boolean;
    }[];
}

interface NewCategoriesDataResponse {
    data: {
        [key: string]: number;
    }[];
}

@Injectable({
    providedIn: 'root'
})
export class OffCanvasService {
    data = signal<NewCategoriesData | undefined>(undefined);
    data$ = toObservable(this.data);
    visited = new Map<string, boolean>();

    protected multisite = inject(MultisiteService);
    protected http = inject(HttpClient);
    protected logger = inject(Logger);
    protected sessionStorage = inject(SessionStorageService);

    get dataUrl() {
        if (this.multisite.siteId) {
            return NEW_CATEGORIES_URL[this.multisite.siteId];
        }

        return NEW_CATEGORIES_URL['uk'];
    }

    get storageKey() {
        return `categoriesData-${this.multisite.siteId}`;
    }

    async initialize() {
        const now = new Date();
        const wednesday = new Date(now.setDate(now.getDate() - now.getDay() + 3)); // Set to this week's Wednesday
        wednesday.setHours(4, 10, 0, 0); // Set time to 4:10 AM
        const timestamp = wednesday.getTime();

        const storageData = this.sessionStorage.get(this.storageKey);

        if (!storageData) {
            await this.loadData();
        } else {
            const data = storageData as NewCategoriesData;

            this.data.set(data);

            if (this.data() && Date.now() >= timestamp && timestamp - this.data()!.updatedAt >= 0) {
                await this.loadData();
            }
        }
    }

    updateVisited() {
        if (this.data()) {
            const data = this.data()!;

            data.categories.forEach(category => {
                if (this.visited.has(category.url)) {
                    category.visited = true;
                }
            });

            this.updateData(data);
        }

        this.visited.clear();
    }

    markVisited(url: string) {
        if (!this.visited.has(url)) {
            this.visited.set(url, true);
        }
    }

    onModelChange(model: Category<ICategoryDataV2>) {
        if (this.data()) {
            const data = this.data()!;
            const url = model.data?.url;

            const category = data.categories.find(category => category.url === url);

            if (category) {
                category.visited = true;

                this.updateData(data);
            }
        }
    }

    protected updateData(data?: NewCategoriesData) {
        this.data.set(data);

        this.sessionStorage.set(this.storageKey, data);
    }

    protected async loadData() {
        try {
            const response = await lastValueFrom(this.http.get<NewCategoriesDataResponse[]>(this.dataUrl));

            if (response) {
                let newData: NewCategoriesData = {
                    updatedAt: Date.now(),
                    categories: []
                };

                const data = response[0]['data'][0];

                Object.keys(data).forEach(key => {
                    newData.categories.push({
                        url: key,
                        count: data[key],
                        visited: false
                    });
                });

                this.updateData(newData);
            }
        } catch (error) {
            this.logger.error('Failed to load data', error);

            this.updateData(undefined);
        }
    }
}

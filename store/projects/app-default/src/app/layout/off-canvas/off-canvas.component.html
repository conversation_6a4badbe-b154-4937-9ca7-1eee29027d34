<div class="pos-relative w-12 height-100-vh flex-column bg-col-w _content z-2">
    @if (content) {
    <div class="w-12 height-100 flex-grow">
        <div class="fill flex-column">
            <div class="pos-relative flex-grow flex-column overflow-hidden">

                <!-- level 1 -->
                <div class="ng-hide-animate hide-left flex-grow flex-column overflow-hidden"
                     [toggleClass]="'ng-hide'"
                     [toggleClassBy]="toggleGroupId"
                     #scroll>

                    <!-- My account -->
                    @if (!burgerMenu && !mobileNavExp) {
                    <ng-container *ifNotSagepayCloudPaymentActive>
                        <div class="p-a flex flex-middle"
                             *ifLoggedIn>
                            <span class="flex-span-1">
                                @if(client.isS || client.isM) {
                                <a class="inline-flex"
                                   [routerLink]="'/about-us/store-locator'"
                                   [attr.aria-label]="'Change location' | translate">
                                    <i class="icon-location inline-flex"
                                       [style.font-size.rem]="1.75"
                                       aria-hidden="true"></i>
                                </a>
                                }
                            </span>
                            <span>
                                <a [sizeClass]="'SM:h5, LX: h2'"
                                   [routerLink]="'/account'"
                                   [attr.aria-label]="'Account' | translate">
                                    {{'Hi,' | translate}} {{client.isS || client.isM ? userName : fullUserName ||
                                    userName}}
                                </a>
                            </span>
                            <ng-container [ngTemplateOutlet]="closeBtn" />
                        </div>
                        <!-- Log in -->
                        <div class="p-a p-t-2-m p-t-2-s p-b-2-m p-b-2-s flex flex-middle"
                             *ifNotLoggedIn>
                            @if (client.isS || client.isM) {
                            <span class="flex-span-1">
                                <a class="inline-flex"
                                   [routerLink]="'/about-us/store-locator'"
                                   [attr.aria-label]="'Change location' | translate">
                                    <i class="icon-location inline-flex"
                                       [style.font-size.rem]="1.75"
                                       aria-hidden="true"></i>
                                </a>
                            </span>
                            <button class="h5"
                                    type="button"
                                    [iCmsModalOpen]="'login-form-modal'">{{'Log in' | translate}}</button>
                            }
                            <ng-container [ngTemplateOutlet]="closeBtn" />
                        </div>
                    </ng-container>
                    <ng-container *ifSagepayCloudPaymentActive>
                        <div class="p-a flex flex-middle">
                            <sagepay-cloud-payment-current-name [charsToDisplay]="15" />
                            <ng-container [ngTemplateOutlet]="closeBtn" />
                        </div>
                    </ng-container>
                    } @else {
                    <div class="p-a p-t-3 p-b-3 flex flex-middle"
                         [elementHeight]="'offcanvas-search'">
                        <button class="w-12 pos-relative overflow-hidden form-small p-r-2"
                                type="button"
                                (click)="openSearch('Offcanvas')">
                            <input class="input no-focus p-t p-b p-l-10"
                                   [ngClass]="burgerMenu ? 'b-radius-5 bg-col-13' : 'b-radius-10 b-col-4-s b-col-4-m bg-col-33-s bg-col-33-m'"
                                   [style.height.px]="35"
                                   type="text"
                                   readonly="true"
                                   #input>
                            <span class="pos-absolute top-1 left-0 bottom-0 p-l-2 flex flex-middle"
                                  [ngClass]="burgerMenu ? 'p-l-2' : 'p-l-4'">
                                <i class="icon-simple-search"
                                   aria-hidden="true"
                                   [style.font-size.px]="24"></i>
                            </span>
                        </button>
                        <ng-container [ngTemplateOutlet]="closeBtn" />
                    </div>
                    }
                    <div class="flex-column flex-grow overflow-y-scroll">
                        <!-- Search -->
                        @if (!burgerMenu && !mobileNavExp && (app.client.isS || app.client.isM)) {
                        <div class="p-b p-t-1 p-l p-r">
                            <button class="w-12 pos-relative overflow-hidden form-small header-search b-radius-10"
                                    type="button"
                                    (click)="openSearch('Offcanvas')">
                                <input class="input no-focus p-l-10 b-radius-10 _off-canvas-search-input"
                                       type="text"
                                       readonly="true"
                                       #input>
                                <span class="pos-absolute top-0 left-0 bottom-0 p-l-3 flex flex-middle">
                                    <i class="icon-simple-search"
                                       [style.font-size.px]="24"
                                       [style.height.px]="24"
                                       aria-hidden="true"></i>
                                </span>
                            </button>
                        </div>
                        }

                        <!-- level 1 menu items -->
                        <ul class="b-col-4 b-t-x b-t-l">
                            <!-- menu item -->
                            <ng-container *abIf="'(mobileNav) A'">
                                @for (item of content?.menu; track $index) {
                                @if (ifCustomerGroup(item.visibility)) {
                                <li class="p-l p-r-5 p-t-3 p-b-3 b-col-4"
                                    [class.b-b]="!burgerMenu"
                                    [class.p-b-6]="$last"
                                    [ngClass]="{'bg-col-5': item.is_separator}"
                                    csOverrideId
                                    [elementId]="item.text">

                                    <!-- link -->
                                    <href
                                          [link]="item.second_level_turn_on && item.second_level_items?.length ? undefined : item.link">
                                        <div class="flex flex-middle cursor-pointer"
                                             (click)="handleOpen($index, item, 1)">

                                            <!-- image -->
                                            @if (!burgerMenu && item.image) {
                                            <div class="m-r m-r-3-x m-r-3-l overflow-hidden b-radius-max _item-image">
                                                <img class="w-12 b-radius-max"
                                                     [dfImage]="item.image"
                                                     [dfSrcset]="'48w, 64w, 80w, 96w'"
                                                     [ratio]="1">
                                            </div>
                                            }

                                            <!-- text -->
                                            <div class="flex flex-grow flex-middle flex-justify-between _item">
                                                <ng-template [offCanvasItem]="item"
                                                             let-item
                                                             let-count="count">
                                                    <div class="flex-grow">
                                                        @if (burgerMenu) {
                                                        <span [ngClass]="[item.text_color || '', item.isTextBold ? 'h2-bold' : 'h2']"
                                                              *iCms="'text' of item"></span>
                                                        @if (count && !item.hideNotifications) {
                                                        <span class="_notification-dot"></span>
                                                        }
                                                        } @else {
                                                        <span [ngClass]="item.text_color || ''"
                                                              [sizeClass]="'SM:h4, LX: h2'"
                                                              *iCms="'text' of item"></span>
                                                        @if (count && !item.hideNotifications) {
                                                        <span class="_notification-dot"></span>
                                                        }
                                                        <span class="w-12 p-t-1 c1"
                                                              [ngClass]="item.copy_text_color || ''"
                                                              *iCms="'copy_text' of item; skipEmpty: true"></span>
                                                        }
                                                    </div>
                                                    @if (count && !item.hideNotifications) {
                                                    <span class="c1 _notification">
                                                        {{'% new style% waiting' | translate : count : count === 1 ? ''
                                                        :
                                                        's'}}
                                                    </span>
                                                    }
                                                </ng-template>
                                                @if (!burgerMenu && item.display_arrow) {
                                                <div class="_item-arrow">
                                                    <i class="icon-arrow-forward"
                                                       [style.height.px]="16"
                                                       aria-hidden="true"></i>
                                                </div>
                                                } @else if (burgerMenu && item.second_level_turn_on) {
                                                <div class="_item-arrow">
                                                    <i class="icon-chevron-right"
                                                       [style.height.px]="12"
                                                       [style.font-size.px]="12"
                                                       aria-hidden="true"></i>
                                                </div>
                                                }
                                            </div>
                                        </div>
                                    </href>
                                </li>
                                }
                                }
                            </ng-container>
                            <div *abIf="'(mobileNav) B'"
                                 class="wrap bg-col-4 p-b-20 p-t-4"
                                 [grid]="3"
                                 [gridColumnGap]="3"
                                 [gridRowGap]="8">
                                @for (item of content?.menu; track $index) {
                                @if (ifCustomerGroup(item.visibility)) {
                                <href
                                      [link]="item.second_level_turn_on && item.second_level_items?.length ? undefined : item.link">
                                    <div class="w-12 bg-col-w _custom-border"
                                         (click)="handleOpen($index, item, 1)"
                                         (keydown)="handleOpen($index, item, 1)"
                                         role="button"
                                         tabindex="0">
                                        <div class="w-12 ratio-1-1">
                                            <img class="w-12"
                                                 style="border-radius:4px;"
                                                 [dfImage]="item.image"
                                                 [dfSrcset]="'48w, 64w, 80w, 96w, 130w, 260w'"
                                                 [ratio]="1"
                                                 [alt]="item.text">
                                        </div>
                                        <div class="p-l-2 p-r-2 p-t-1 p-b-1"
                                             style="min-height:60px;">
                                            <span [ngClass]="item.text_color || ''"
                                                  class="s1"
                                                  *iCms="'text' of item"></span>
                                        </div>
                                    </div>
                                </href>
                                }}
                            </div>
                        </ul>
                        @if (burgerMenu) {
                        @if (content?.newEditsSectionTurnOn && content?.newEditsSectionItems?.length) {
                        <div class="p-t p-b bg-col-33">
                            <div class="p-b-2 m-l m-r b-b o1 col-1"
                                 *iCms="'newEditsSectionTitle' of content; skipEmpty: true"></div>
                            @for (editSection of content?.newEditsSectionItems; track $index) {
                            <href [link]="editSection.link">
                                <div class="p1 m-l m-r m-t-3"
                                     [ngClass]="editSection.textColor || 'col-1'"
                                     *iCms="'text' of editSection">
                                </div>
                            </href>
                            }
                        </div>
                        }
                        @if (content?.bannersSectionTurnOn && content?.bannerSectionItems?.length) {
                        <div class="p-l p-r p-t-2 p-b-2 bg-col-33">
                            @for (bannerSection of content?.bannerSectionItems; track $index) {
                            <href [link]="bannerSection.link">
                                <div class="bg-col-w flex flex-middle p-l-3 p-r-3 p-t p-b box-shadow m-b"
                                     [class.m-t-2]="!$first">
                                    <img [dfImage]="bannerSection.image"
                                         [height]="64"
                                         [width]="94"
                                         [alt]="bannerSection.text">
                                    <div class="m-l-3">
                                        <div class="h3"
                                             [ngClass]="bannerSection.textColor || 'col-1'"
                                             *iCms="'text' of bannerSection">
                                        </div>
                                        <div class="s1"
                                             *iCms="'textCTA' of bannerSection">
                                        </div>
                                    </div>
                                </div>
                            </href>
                            }
                        </div>
                        }
                        }
                    </div>

                    <!-- Quick links level 1 -->
                    @if (!burgerMenu && content?.quick_links_turn_on && content?.quick_links_items?.length) {
                    <div class="off-canvas-body bg-col-w">
                        <div class="p-t-6 p-b b-t b-col-4">
                            <div class="m-b p-l c1 col-13"
                                 *iCms="'quick_links_text' of content; skipEmpty: true"></div>
                            <div dragScroll>
                                @for (quick_link of content?.quick_links_items; track $index) {
                                @if (ifCustomerGroup(quick_link.visibility)) {
                                <href [link]="quick_link.link">
                                    <div class="inline-block va-t"
                                         [class.p-r]="$last"
                                         [ngClass]="$first ? 'p-l' : 'p-l-3'">
                                        <div class="_item-image">
                                            <img class="w-12 b-radius-max"
                                                 [dfImage]="quick_link.image"
                                                 [dfSrcset]="'48w, 64w, 80w, 96w'"
                                                 [ratio]="1"
                                                 [alt]="quick_link.text">
                                        </div>
                                        <div class="c1 m-t-2 center yes-wrap"
                                             [ngClass]="quick_link.text_color || ''"
                                             *iCms="'text' of quick_link">
                                        </div>
                                    </div>
                                </href>
                                }
                                }
                            </div>
                        </div>
                    </div>
                    }
                </div>

                <!-- level 2 -->
                @for (item of content?.menu; track i; let i = $index) {
                @if (ifCustomerGroup(item.visibility) && (client.isCms || (item.second_level_turn_on &&
                item.second_level_items?.length))) {
                <div class="fill ng-hide ng-hide-animate hide-left bg-col-w flex-column flex-grow flex-justify-between"
                     [toggleClass]="'!ng-hide'"
                     [toggleClassBy]="[toggleGroupId, i]">
                    <div class="flex-column overflow-y-scroll">
                        <ng-container [ngTemplateOutlet]="menuHeader"
                                      [ngTemplateOutletContext]="{toggleGroup: [toggleGroupId, i], item: item}" />
                        <!-- level 2 - menu items -->
                        @if (canPrintContentBody(i)) {
                        <div class="b-t b-col-4 overflow-y-scroll"
                             [ngClass]="item.second_level_background_color">
                            <!-- level 2 menu item -->
                            <div class="_off-canvas-links-container">
                                @for (secondLevelItem of item.second_level_items; track j; let j = $index) {
                                @if (ifCustomerGroup(secondLevelItem.visibility)) {
                                <ng-container [ngTemplateOutlet]="menuItem"
                                              [ngTemplateOutletContext]="{elementId: (item.text + '_' + secondLevelItem.text), item: secondLevelItem, level: 2, openIndex: i + '-' + j, hasSubmenu: secondLevelItem.third_level_turn_on && secondLevelItem.third_level_items?.length, isLast: $last}" />
                                }
                                }
                            </div>
                            @if (burgerMenu) {
                            @if (canPrintContentBody(i) && item?.newEditsSectionTurnOn &&
                            item?.newEditsSectionItems?.length) {
                            <div class="p-t p-b bg-col-33">
                                <div class="p-b-2 m-l m-r b-b o1 col-1"
                                     *iCms="'newEditsSectionTitle' of content; skipEmpty: true"></div>
                                @for (editSection of item?.newEditsSectionItems; track $index) {
                                <href [link]="editSection.link">
                                    <div class="p1 m-l m-r m-t-3"
                                         [ngClass]="editSection.textColor || 'col-1'"
                                         *iCms="'text' of editSection">
                                    </div>
                                </href>
                                }
                            </div>
                            }
                            @if (item?.bannersSectionTurnOn && item?.bannerSectionItems?.length) {
                            <div class="p-l p-r p-t-2 p-b-2 bg-col-33">
                                @for (bannerSection of item?.bannerSectionItems; track $index) {
                                <href [link]="bannerSection.link">
                                    <div class="bg-col-w flex flex-middle p-l-3 p-r-3 p-t p-b box-shadow m-b m-t">
                                        <img [dfImage]="bannerSection.image"
                                             [height]="64"
                                             [width]="94"
                                             [alt]="bannerSection.text">
                                        <div class="m-l-3">
                                            <div class="h3"
                                                 [ngClass]="bannerSection.textColor || 'col-1'"
                                                 *iCms="'text' of bannerSection">
                                            </div>
                                            <div class="s1"
                                                 *iCms="'textCTA' of bannerSection">
                                            </div>
                                        </div>
                                    </div>
                                </href>
                                }
                            </div>
                            }
                            @if (content?.footerItemsShowBVariant && content?.footerItemsBVariant?.length && level
                            !== 0) {
                            <div class="w-12 off-canvas-body bg-col-w flex b-t b-col-4 c1"
                                 [elementHeight]="'offcanvas-bottom-bar'">
                                @for (item of content.footerItemsBVariant; track $index) {
                                @if (item.icon !== 'icon-login') {
                                <href class="contents"
                                      [link]="item.ctaLink"
                                      [hrefClass]="'w-4'">
                                    <div class="w-12 p-t-3 p-b-3 flex-column center"
                                         [class.b-r]="!$last">
                                        @if (item.icon) {
                                        <i [ngClass]="item.icon"
                                           [style.font-size.px]="24"
                                           aria-hidden="true"></i>
                                        }
                                        <div class="p-t-1"
                                             [style.color]="item?.fontColor"
                                             *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                                        </div>
                                    </div>
                                </href>
                                } @else {
                                <href class="contents"
                                      [link]="'/account'"
                                      [hrefClass]="'w-4'"
                                      *ifLoggedIn>
                                    <div class="p-t-3 p-b-3 flex-column center"
                                         [class.b-r]="!$last">
                                        @if (item.icon) {
                                        <i [ngClass]="item.icon"
                                           [style.font-size.px]="24"
                                           aria-hidden="true"></i>
                                        }
                                        <div class="p-t-1"
                                             [style.color]="item?.fontColor"
                                             *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                                        </div>
                                    </div>
                                </href>
                                <div class="w-4 p-t-3 p-b-3 flex-column center"
                                     [class.b-r]="!$last"
                                     [iCmsModalOpen]="'login-form-modal'"
                                     *ifNotLoggedIn>
                                    @if (item.icon) {
                                    <i [ngClass]="item.icon"
                                       [style.font-size.px]="24"
                                       aria-hidden="true"></i>
                                    }
                                    <div class="p-t-1"
                                         [style.color]="item?.fontColor"
                                         *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                                    </div>
                                </div>
                                }
                                }
                            </div>
                            }
                            }
                        </div>
                        }
                    </div>
                    @if (!burgerMenu && item.quick_links_turn_on && item.quick_links_items?.length) {
                    <!-- Quick links level 2 -->
                    <div class="off-canvas-body bg-col-w">
                        <div class="p-t-6 p-b b-t b-col-4">
                            <div class="m-b p-l c1 col-13"
                                 *iCms="'quick_links_text' of item; skipEmpty: true"></div>
                            <div dragScroll>
                                @for (quick_link of item.quick_links_items; track $index) {
                                @if (ifCustomerGroup(quick_link.visibility)) {
                                <href [link]="quick_link.link">
                                    <div class="inline-block va-t"
                                         [class.p-r]="$last"
                                         [ngClass]="$first ? 'p-l' : 'p-l-3'">
                                        <div class="_item-image">
                                            <img class="w-12 b-radius-max"
                                                 [dfImage]="quick_link.image"
                                                 [dfSrcset]="'48w, 64w, 80w, 96w'"
                                                 [ratio]="1"
                                                 [alt]="quick_link.text">
                                        </div>
                                        <div class="c1 m-t-2 center yes-wrap _item-image"
                                             [ngClass]="quick_link.text_color || ''"
                                             *iCms="'text' of quick_link">
                                        </div>
                                    </div>
                                </href>
                                }
                                }
                            </div>
                        </div>
                    </div>
                    }
                </div>
                }
                }

                <!-- level 3 -->
                @for (item of content?.menu; track i; let i = $index) {
                @if (ifCustomerGroup(item.visibility)) {
                @for (secondLevelItem of item.second_level_items; track j; let j = $index) {
                @if (canPrintContentBody(i) && ifCustomerGroup(secondLevelItem.visibility) && (client.isCms ||
                (secondLevelItem.third_level_turn_on && secondLevelItem.third_level_items?.length))) {
                <div class="fill ng-hide ng-hide-animate hide-left bg-col-w flex-column flex-grow flex-justify-between"
                     [toggleClass]="'!ng-hide'"
                     [toggleClassBy]="[toggleGroupId, i + '-' + j]">
                    <div class="flex-column overflow-y-scroll">
                        <ng-container [ngTemplateOutlet]="menuHeader"
                                      [ngTemplateOutletContext]="{toggleGroup: [toggleGroupId, i + '-' + j], item: secondLevelItem}" />
                        <!-- level 3 - menu items -->
                        @if (canPrintContentBody(i + '-' + j)) {
                        <div class="b-t b-col-4 overflow-y-scroll">
                            <div style="min-height: 68vh;">
                                @for (thirdLevelItem of secondLevelItem.third_level_items; track k; let k = $index) {
                                @if (ifCustomerGroup(thirdLevelItem.visibility)) {
                                <ng-container [ngTemplateOutlet]="menuItem"
                                              [ngTemplateOutletContext]="{elementId: (item.text + '_' + thirdLevelItem.text), item: thirdLevelItem, level: 3, openIndex: i + '-' + j + '-' + k, hasSubmenu: thirdLevelItem.fourth_level_turn_on && thirdLevelItem.fourth_level_items?.length}" />
                                }
                                }
                            </div>
                            @if (burgerMenu) {
                            @if (secondLevelItem?.newEditsSectionTurnOn &&
                            secondLevelItem?.newEditsSectionItems?.length) {
                            <div class="p-t p-b bg-col-33">
                                <div class="p-b-2 m-l m-r b-b o1 col-1"
                                     *iCms="'newEditsSectionTitle' of secondLevelItem; skipEmpty: true"></div>
                                @for (editSection of secondLevelItem?.newEditsSectionItems; track $index) {
                                <href [link]="editSection.link">
                                    <div class="p1 m-l m-r m-t-3"
                                         [ngClass]="editSection.textColor || 'col-1'"
                                         *iCms="'text' of editSection">
                                    </div>
                                </href>
                                }
                            </div>
                            }
                            @if (secondLevelItem?.bannersSectionTurnOn &&
                            secondLevelItem?.bannerSectionItems?.length) {
                            <div class="p-l p-r p-t-2 p-b-2 bg-col-33">
                                @for (bannerSection of secondLevelItem?.bannerSectionItems; track $index) {
                                <href [link]="bannerSection.link">
                                    <div class="bg-col-w flex flex-middle p-l-3 p-r-3 p-t p-b box-shadow m-b m-t">
                                        <img [dfImage]="bannerSection.image"
                                             [height]="64"
                                             [width]="94"
                                             [alt]="bannerSection.text">
                                        <div class="m-l-3">
                                            <div class="h3"
                                                 [ngClass]="bannerSection.textColor || 'col-1'"
                                                 *iCms="'text' of bannerSection">
                                            </div>
                                            <div class="s1"
                                                 *iCms="'textCTA' of bannerSection">
                                            </div>
                                        </div>
                                    </div>
                                </href>
                                }
                            </div>
                            }
                            @if (content?.footerItemsShowBVariant && content?.footerItemsBVariant?.length && level
                            !== 0) {
                            <div class="w-12 off-canvas-body bg-col-w flex b-t b-col-4 c1 pos-absolute bottom-0">
                                @for (item of content.footerItemsBVariant; track $index) {
                                @if (item.icon !== 'icon-login') {
                                <href class="contents"
                                      [link]="item.ctaLink"
                                      [hrefClass]="'w-4'">
                                    <div class="w-12 p-t-3 p-b-3 flex-column center"
                                         [class.b-r]="!$last">
                                        @if (item.icon) {
                                        <i [ngClass]="item.icon"
                                           [style.font-size.px]="24"
                                           aria-hidden="true"></i>
                                        }
                                        <div class="p-t-1"
                                             [style.color]="item?.fontColor"
                                             *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                                        </div>
                                    </div>
                                </href>
                                } @else {
                                <href class="contents"
                                      [link]="'/account'"
                                      [hrefClass]="'w-4'"
                                      *ifLoggedIn>
                                    <div class="p-t-3 p-b-3 flex-column center"
                                         [class.b-r]="!$last">
                                        @if (item.icon) {
                                        <i [ngClass]="item.icon"
                                           [style.font-size.px]="24"
                                           aria-hidden="true"></i>
                                        }
                                        <div class="p-t-1"
                                             [style.color]="item?.fontColor"
                                             *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                                        </div>
                                    </div>
                                </href>
                                <div class="w-4 p-t-3 p-b-3 flex-column center"
                                     [class.b-r]="!$last"
                                     [iCmsModalOpen]="'login-form-modal'"
                                     *ifNotLoggedIn>
                                    @if (item.icon) {
                                    <i [ngClass]="item.icon"
                                       [style.font-size.px]="24"
                                       aria-hidden="true"></i>
                                    }
                                    <div class="p-t-1"
                                         [style.color]="item?.fontColor"
                                         *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                                    </div>
                                </div>
                                }
                                }
                            </div>
                            }
                            }
                        </div>
                        }
                    </div>

                    <!-- Quick links level 3 -->
                    @if (!burgerMenu && canPrintContentBody(i + '-' + j) && secondLevelItem.quick_links_turn_on &&
                    secondLevelItem.quick_links_items?.length) {
                    <div class="off-canvas-body bg-col-w">
                        <div class="p-t-6 p-b b-t b-col-4">
                            <div class="m-b p-l c1 col-13"
                                 *iCms="'quick_links_text' of secondLevelItem; skipEmpty: true">
                            </div>
                            <div dragScroll>
                                @for (quick_link of secondLevelItem.quick_links_items; track $index) {
                                @if (ifCustomerGroup(quick_link.visibility)) {
                                <href [link]="quick_link.link">
                                    <div class="inline-block va-t"
                                         [class.p-r]="$last"
                                         [ngClass]="$first ? 'p-l' : 'p-l-3'">
                                        <div class="_item-image">
                                            <img class="w-12 b-radius-max"
                                                 [dfImage]="quick_link.image"
                                                 [dfSrcset]="'48w, 64w, 80w, 96w'"
                                                 [ratio]="1"
                                                 [alt]="quick_link.text">
                                        </div>
                                        <div class="c1 m-t-2 center yes-wrap _item-image"
                                             [ngClass]="quick_link.text_color || ''"
                                             *iCms="'text' of quick_link">
                                        </div>
                                    </div>
                                </href>
                                }
                                }
                            </div>
                        </div>
                    </div>
                    }
                </div>
                }
                }
                }
                }

                <!-- level 4 -->
                @for (item of content?.menu; track i; let i = $index) {
                @if (ifCustomerGroup(item.visibility)) {
                @for (secondLevelItem of item.second_level_items; track j; let j = $index) {
                @if (ifCustomerGroup(secondLevelItem.visibility)) {
                @for (thirdLevelItem of secondLevelItem.third_level_items; track k; let k = $index) {
                @if (ifCustomerGroup(thirdLevelItem.visibility) && canPrintContentBody(i + '-' + j) && (client.isCms ||
                (thirdLevelItem.fourth_level_turn_on &&
                thirdLevelItem.fourth_level_items?.length))) {
                <div class="fill ng-hide ng-hide-animate hide-left bg-col-w"
                     [toggleClass]="'!ng-hide'"
                     [toggleClassBy]="[toggleGroupId, i + '-' + j + '-' + k]">
                    <div class="flex-column">
                        <ng-container [ngTemplateOutlet]="menuHeader"
                                      [ngTemplateOutletContext]="{toggleGroup: [toggleGroupId, i + '-' + j + '-' + k], item: thirdLevelItem}" />
                        <!-- level 4 - menu items -->
                        @if (canPrintContentBody(i + '-' + j + '-' + k)) {
                        <ul class="b-t b-col-4 overflow-y-scroll no-quick-links">
                            <!-- level 4 menu item -->
                            @for (fourthLevelItem of thirdLevelItem.fourth_level_items; track $index) {
                            @if (ifCustomerGroup(fourthLevelItem.visibility)) {
                            <li csOverrideId
                                [elementId]="(item.text + '_' + fourthLevelItem.text)">
                                <!-- link -->
                                <href [link]="fourthLevelItem.link">
                                    <span class="flex flex-middle b-b b-col-4 p-t-5 p-b-5 p-l p-r"
                                          [ngClass]="{'bg-col-5': fourthLevelItem.is_separator}">
                                        <!-- text -->
                                        <span class="h4"
                                              [ngClass]="fourthLevelItem.text_color"
                                              csOverrideId
                                              [elementId]="secondLevelItem.text + '_' + thirdLevelItem.text + '_' + fourthLevelItem.text"
                                              *iCms="'text' of fourthLevelItem"></span>
                                    </span>
                                </href>
                            </li>
                            }
                            }
                        </ul>
                        }
                    </div>
                </div>
                }
                }
                }
                }
                }
                }
            </div>

            <!-- Menu bottom  -->
            @if (!burgerMenu && !mobileNavExp && content?.footerItemsShow && content?.footerItems?.length) {
            <div class="off-canvas-body bg-col-w flex b-t b-col-4 c1">
                @for (item of content.footerItems; track $index) {
                <div class="w-12 b-r">
                    @if (item.type === 'link') {
                    <href [link]="item.ctaLink">
                        <div class="p-t-5 p-b-5 p-l-1 p-r-1 center b-col-4"
                             [style.color]="item?.fontColor"
                             *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                        </div>
                    </href>
                    }

                    @if (item.type === 'countrySelector') {
                    <div class="p-t p-b center b-col-4"
                         multisiteModalOpen>
                        <multisite-label />
                    </div>
                    }
                </div>
                }
            </div>
            }
            <!-- Menu bottom  -->
            @if ((burgerMenu || mobileNavExp) && content?.footerItemsShowBVariant &&
            content?.footerItemsBVariant?.length && level === 0)
            {
            <div class="w-12 off-canvas-body bg-col-w flex b-t b-col-4 c1 pos-absolute bottom-0">
                @for (item of content.footerItemsBVariant; track $index) {
                @if (item.icon !== 'icon-login') {
                <href class="contents"
                      [link]="item.ctaLink"
                      [hrefClass]="'w-4'">
                    <div class="w-12 p-t-3 p-b-3 flex-column center"
                         [class.b-r]="!$last">
                        @if (item.icon) {
                        <i [ngClass]="item.icon"
                           [style.font-size.px]="24"
                           aria-hidden="true"></i>
                        }
                        <div class="p-t-1"
                             [style.color]="item?.fontColor"
                             *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                        </div>
                    </div>
                </href>
                } @else {
                <a class="w-4"
                   [routerLink]="'/account'"
                   *ifLoggedIn>
                    <div class="p-t-3 p-b-3 flex-column center"
                         [class.b-r]="!$last">
                        @if (item.icon) {
                        <i [ngClass]="item.icon"
                           [style.font-size.px]="24"
                           aria-hidden="true"></i>
                        }
                        <div class="p-t-1"
                             [style.color]="item?.fontColor"
                             *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                        </div>
                    </div>
                </a>
                <div class="w-4 p-t-3 p-b-3 flex-column center"
                     [class.b-r]="!$last"
                     [iCmsModalOpen]="'login-form-modal'"
                     *ifNotLoggedIn>
                    @if (item.icon) {
                    <i [ngClass]="item.icon"
                       [style.font-size.px]="24"
                       aria-hidden="true"></i>
                    }
                    <div class="p-t-1"
                         [style.color]="item?.fontColor"
                         *iCms="'ctaText' of item; content: 'html'; skipEmpty: true">
                    </div>
                </div>
                }
                }
            </div>
            }
        </div>
    </div>
    }
</div>
<!-- overlay -->
<overlay class="z-1"
         (click)="close()"
         [visible]="offCanvasState" />

<ng-template #closeBtn>
    <span class="flex flex-middle flex-span-1 flex-justify-end">
        <button class="p-a-2 p-a-1-x p-a-1-l p-r-0 inline-flex"
                type="button"
                aria-label="Close"
                (click)="close()">
            <i class="icon-close inline-flex"
               aria-hidden="true"></i>
        </button>
    </span>
</ng-template>


<ng-template #menuHeader
             let-toggleGroup="toggleGroup"
             let-item="item">
    @if (!burgerMenu) {
    <div class="p-a ng-hide flex flex-middle"
         [toggleClass]="'!ng-hide'"
         [toggleClassBy]="toggleGroup">
        <span class="flex-span-1 flex flex-middle">
            <button class="inline-flex p-a-1"
                    (click)="toggleClose(toggleGroup)"
                    style="transform: rotate(180deg);"
                    type=button>
                <i class="icon-arrow-forward inline-flex"></i>
            </button>
        </span>
        <href [link]="item.link">
            <span class="col-13 col-12-l col-12-x"
                  [sizeClass]="'SM:h5, LX:h2'">{{item.text}}</span>
        </href>
        <ng-container [ngTemplateOutlet]="closeBtn" />
    </div>
    } @else {
    <div class="p-a p-t-3 p-b-3 flex flex-middle bg-col-33">
        <button class="w-12 pos-relative overflow-hidden form-small p-r-2"
                type="button"
                (click)="openSearch('Offcanvas')">
            <input class="input no-focus bg-col-13 p-t p-b p-l-10 b-radius-5"
                   [style.height.px]="35"
                   type="text"
                   readonly="true"
                   #input>
            <div class="pos-absolute top-1 left-0 bottom-0 p-l-2 flex flex-middle">
                <i class="icon-simple-search"
                   aria-hidden="true"
                   [style.font-size.px]="24"></i>
            </div>
        </button>
        <ng-container [ngTemplateOutlet]="closeBtn" />
    </div>
    <div class="bg-col-33 p-l p-b-2 flex"
         [elementHeight]="'offcanvas-breadcrumbs'">
        <button class="center"
                [style.width.px]="24"
                [style.line-height.px]="24"
                type="button"
                [attr.aria-label]="'Back' | translate"
                (click)="toggleClose(toggleGroup)">
            <i class="icon-chevron-left"
               [style.font-size.px]="12"
               aria-hidden="true"></i>
        </button>
        @if (clickedItems.length > 1) {
        <href class="m-l-2"
              [link]="item.link">
            <span class="h2-bold">{{clickedItems.slice(-2).join(' | ')}}</span>
        </href>
        } @else {
        <href class="m-l-2"
              [link]="item.link">
            <span class="h2-bold">{{clickedItems[0]}}</span>
        </href>
        }
    </div>
    }
</ng-template>

<ng-template #menuItem
             let-item="item"
             let-elementId="elementId"
             let-level="level"
             let-openIndex="openIndex"
             let-hasSubmenu="hasSubmenu"
             let-isLast="isLast">
    <div csOverrideId
         [elementId]="elementId">
        <!-- link -->
        @if (!burgerMenu) {
        <href [link]="hasSubmenu ? undefined : item.link"
              [hrefClass]="'block'">
            <span class="flex flex-middle flex-justify-between b-col-4 p-t-5 p-b-5 p-t-3-x p-t-3-l p-b-3-x p-b-3-l p-l p-r b-b"
                  [ngClass]="{'bg-col-5': item.is_separator}"
                  (click)="handleOpen(openIndex, item, level)">
                <!-- text -->
                @if (item.is_separator && (app.client.isL || app.client.isX)) {
                <!-- to make it the same height as item with content on L and X -->
                <span class="h2 _dummy-content">&nbsp;</span>
                } @else {
                @if (app.isPlatformBrowser) {
                <ng-template [offCanvasItem]="item"
                             let-item
                             let-count="count">
                    <div class="flex-grow">
                        <span [sizeClass]="'SM:h4, LX:h2'"
                              [ngClass]="item.text_color || ''"
                              csOverrideId
                              [elementId]="item.text"
                              *iCms="'text' of item"></span>
                        @if (count && !item.hideNotifications) {
                        <span class="_notification-dot"></span>
                        }
                    </div>
                    @if (count && !item.hideNotifications) {
                    <span class="c1 _notification">
                        {{'% new style% waiting' | translate : count : count === 1 ? '' : 's'}}
                    </span>
                    }
                </ng-template>
                } @else {
                <span [sizeClass]="'SM:h4, LX:h2'"
                      [ngClass]="item.text_color || ''"
                      csOverrideId
                      [elementId]="item.text"
                      *iCms="'text' of item"></span>
                }
                @if (item.display_arrow) {
                <div class="_item-arrow">
                    <i class="icon-arrow-forward"
                       [style.height.px]="16"
                       aria-hidden="true"></i>
                </div>
                }
                }
            </span>
        </href>
        } @else {
        <href [link]="hasSubmenu ? undefined : item.link"
              [hrefClass]="'block'">
            <span class="flex flex-middle flex-justify-between b-col-4 p-t-3 p-b-3 p-l p-r"
                  [class.p-b-6]="isLast"
                  (click)="handleOpen(openIndex, item, level)">
                <!-- text -->
                @if (app.isPlatformBrowser) {
                <ng-template [offCanvasItem]="item"
                             let-item
                             let-count="count">
                    <div class="flex-grow">
                        <span [ngClass]="[item.text_color || '', item.isTextBold ? 'h2-bold' : 'h2']"
                              csOverrideId
                              [elementId]="item.text"
                              *iCms="'text' of item"></span>
                        @if (count && !item.hideNotifications) {
                        <span class="_notification-dot"></span>
                        }
                    </div>
                    @if (count && !item.hideNotifications) {
                    <span class="c1 _notification">
                        {{'% new style% waiting' | translate : count : count === 1 ? '' : 's'}}
                    </span>
                    }
                </ng-template>
                } @else {
                <span [ngClass]="[item.text_color || '', item.isTextBold ? 'h2-bold' : 'h2']"
                      csOverrideId
                      [elementId]="item.text"
                      *iCms="'text' of item"></span>
                }
                @if (item.display_arrow) {
                <div class="_item-arrow">
                    <i class="icon-chevron-right"
                       [style.height.px]="12"
                       [style.font-size.px]="12"
                       aria-hidden="true"></i>
                </div>
                }
            </span>
        </href>
        }
    </div>
</ng-template>
import { CommonModule, NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, inject, Input, OnDestroy, OnInit, Renderer2 } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Category, ICategoryDataV2 } from '@df/catalog/category/category';
import { App } from '@df/core/app';
import { DfEventService } from '@df/core/service/df-event';
import { Events } from '@df/core/service/event';
import { DfDomModule } from '@df/dom/df-dom.module';
import { AbModule } from '@df/module-ab/ab.module';
import { AbService } from '@df/module-ab/ab.service';
import { GtmService } from '@df/module-gtm/gtm.service';
import { DfModuleImageModule } from '@df/module-image/df-module-image.module';
import { Customer } from '@df/session/api/customer';
import { GridModule } from '@df/ui/atomic/atom/grid/grid.module';
import { DragScrollDirective } from '@df/ui/common/drag-scroll.directive';
import { HrefComponent } from '@df/ui/common/href/href.component';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { DfUiModule } from '@df/ui/df-ui.module';
import { ModalRef } from '@df/ui/modal/modal-ref';
import { ToggleModule } from '@df/ui/toggle/toggle.module';
import { NToggle, ToggleService } from '@df/ui/toggle/toggle.service';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { UiComponent } from '@df/ui/ui.component';
import { ICmsClientModule } from '@icms/client/icms-client.module';
import { ICmsComponent, ICmsContent, NICmsContentComponent } from '@icms/core/content';
import { ICmsDataChangeService, NICmsDataChange } from '@icms/core/data/change/icms-data-change.service';
import { NICmsBlock } from '@icms/core/resource/block/icms-block';
import { map, of, switchMap } from 'rxjs';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { NCustomerGroups } from '../../customer/groups/customer-groups';
import { CustomerGroupsService } from '../../customer/groups/customer-groups.service';
import { IfNotSagepayCloudPaymentActiveDirective } from '../../module/sagepay/cloud-payment/if-not-sagepay-cloud-payment-active.directive';
import { IfSagepayCloudPaymentActiveDirective } from '../../module/sagepay/cloud-payment/if-sagepay-cloud-payment-active.directive';
import { SagepayCloudPaymentCurrentNameComponent } from '../../module/sagepay/cloud-payment/sagepay-cloud-payment-current-name.component';
import { IfLoggedInDirective } from '../../module/session/if-logged-in.directive';
import { IfNotLoggedInDirective } from '../../module/session/if-not-logged-in.directive';
import { MultisiteModalOpenDirective } from '../../multisite/modal/multisite-modal.directive';
import { MultisiteLabelComponent } from '../../multisite/multisite-label.component';
import { SEARCH_OUTLET_TOGGLE_ID } from '../../search/outlet/search-outlet.component';
import { OverlayComponent } from '../../ui/common/overlay/overlay.component';
import { CsOverrideIdDirective } from '../meganav/cs-override-id.directive';
import { OffCanvasItemDirective } from './off-canvas-item.directive';
import { OFF_CANVAS_CONFIG } from './off-canvas.config';
import { NOffCanvas } from './off-canvas.interface';
import { NEW_CATEGORIES_AB_KEY, OffCanvasService } from './off-canvas.service';

let uid = 0;

@ICmsComponent<OffCanvasComponent>(OFF_CANVAS_CONFIG)
@Component({
    selector: 'off-canvas',
    templateUrl: './off-canvas.component.html',
    styleUrl: './off-canvas.scss',
    standalone: true,
    imports: [
        AbModule,
        CommonModule,
        CsOverrideIdDirective,
        DfDomModule,
        DfModuleImageModule,
        DfUiModule,
        DragScrollDirective,
        HrefComponent,
        ICmsClientModule,
        IfLoggedInDirective,
        IfNotLoggedInDirective,
        IfNotSagepayCloudPaymentActiveDirective,
        IfSagepayCloudPaymentActiveDirective,
        MultisiteLabelComponent,
        MultisiteModalOpenDirective,
        HrefComponent,
        OffCanvasItemDirective,
        OverlayComponent,
        RouterModule,
        SagepayCloudPaymentCurrentNameComponent,
        SizeClassDirective,
        ToggleModule,
        TranslatePipe,
        GridModule,
        NgClass
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class OffCanvasComponent extends UiComponent implements NICmsContentComponent.IComponent, OnInit, OnDestroy {
    @ICmsContent({
        type: 'blockContent'
    })
    content!: NOffCanvas.IContent;

    @Input({ required: true })
    offCanvasState!: BehaviorSubject<NOffCanvas.EOpenState>;

    readonly toggleGroupId = `offCanvas-${uid++}`;
    readonly searchOutletToggleId = SEARCH_OUTLET_TOGGLE_ID;

    burgerMenu = false;
    mobileNavExp = false;
    level = 0;
    clickedItems: string[] = [];

    readonly app = inject(App);
    protected events = inject(Events);
    protected dfEvent = inject(DfEventService);
    protected toggleService = inject(ToggleService);
    protected customer = inject(Customer);
    protected renderer = inject(Renderer2);
    protected elementRef = inject(ElementRef);
    protected customerGroupsService = inject(CustomerGroupsService);
    protected modalRef = inject(ModalRef<OffCanvasComponent>, { optional: true });
    protected abService = inject(AbService);
    protected iCmsDataChangeService = inject(ICmsDataChangeService);
    protected offCanvasService = inject(OffCanvasService);
    protected gtmService = inject(GtmService);

    /**
     * Meganav items that were ever visible
     */
    private everVisibleItemsIndex: Record<string, boolean> = {};

    get userName(): string | undefined {
        return this.customer.model.data?.firstname;
    }

    get fullUserName(): string | undefined {
        return this.customer.model.data?.firstname && this.customer.model.data?.lastname
            ? `${this.customer.model.data.firstname} ${this.customer.model.data.lastname}`
            : undefined;
    }

    override ngOnInit(): void {
        this.subscriptions.add(
            this.customer.model.obs.status.subscribe(() => {
                this.timeoutService.setTimeout(() => this.detectChanges(), 100);
            })
        );

        this.subscriptions.add(
            this.iCmsDataChangeService.afterChange$.subscribe((event: NICmsDataChange.IChangeDataEvent) => {
                if (this.initiated && (<NICmsBlock.IData>event.model.data).url === NOffCanvas.ID) {
                    this.detectChanges();
                }
            })
        );

        this.subscriptions.add(
            this.events.on('offcanvas:open').subscribe(value => {
                const change = !this.everVisibleItemsIndex[value.index];

                if (change) {
                    this.everVisibleItemsIndex[value.index] = true;

                    this.detectChanges();
                }
            })
        );

        this.subscriptions.add(
            this.client.isCms$.subscribe(open => {
                if (!open) {
                    this.toggleService.closeGroup(this.toggleGroupId);
                }
            })
        );

        this.subscriptions.add(this.app.meta.routerEvents.subscribe(() => this.close()));

        this.subscriptions.add(
            this.abService.getSubject('burgerMenu').subscribe(value => {
                if (value.variant === 'B') {
                    this.burgerMenu = true;
                } else {
                    this.burgerMenu = false;
                }

                this.detectChanges();
            })
        );
        this.subscriptions.add(
            this.abService.getSubject('mobileNav').subscribe(value => {
                if (value.variant === 'B') {
                    this.mobileNavExp = true;
                } else {
                    this.mobileNavExp = false;
                }

                this.detectChanges();
            })
        );

        this.subscriptions.add(
            this.abService
                .getSubject(NEW_CATEGORIES_AB_KEY)
                .pipe(
                    map(experiment => experiment.variant === 'B'),
                    switchMap(value => {
                        return value
                            ? this.offCanvasState.pipe(
                                map(state => {
                                    if (state === NOffCanvas.EOpenState.opened) {
                                        this.offCanvasService.initialize();
                                    } else {
                                        this.offCanvasService.updateVisited();
                                    }
                                })
                            )
                            : of();
                    })
                )
                .subscribe()
        );

        this.subscriptions.add(
            this.dfEvent.success(event => {
                if (event.id === 'dfViewModelChange' && event.data.model?.type === 'category') {
                    this.offCanvasService.onModelChange(event.data.model as Category<ICategoryDataV2>);
                }
            })
        );

        super.ngOnInit();
    }

    handleOpen(index: string | number, item: NOffCanvas.IMenuItem, level: 1 | 2 | 3): void {
        this.level = level;

        const hasDeeperLevels = item.second_level_turn_on || item.third_level_turn_on || item.fourth_level_turn_on;

        if (hasDeeperLevels) {
            if (this.clickedItems.length > level - 1) {
                this.clickedItems.splice(level - 1);
            }

            this.clickedItems.push(item.text);
        }

        let levelTurnOn = false;
        let levelHasItems = false;

        switch (level) {
            case 2:
                levelTurnOn = item.third_level_turn_on;
                levelHasItems = Boolean(item.third_level_items?.length);
                break;
            case 3:
                levelTurnOn = item.fourth_level_turn_on;
                levelHasItems = Boolean(item.fourth_level_items?.length);
                break;
            default:
                levelTurnOn = item.second_level_turn_on;
                levelHasItems = Boolean(item.second_level_items?.length);
        }

        if (this.client.isCms || (levelTurnOn && levelHasItems)) {
            this.toggleService.open([this.toggleGroupId, index]);
            this.events.broadcast('offcanvas:open', { index: `${index}`, item });
        }

        this.detectChanges();
    }

    handleOpenSecond(index: string | number, item: NOffCanvas.IMenuSecondLevelItem): void {
        if (this.client.isCms || (item.third_level_turn_on && item.third_level_items?.length)) {
            this.toggleService.open([this.toggleGroupId, index]);

            this.events.broadcast('offcanvas:open', { index: `${index}`, item });
        }
    }

    handleOpenThird(index: string | number, item: NOffCanvas.IMenuThirdLevelItem): void {
        if (this.client.isCms || (item.fourth_level_turn_on && item.fourth_level_items?.length)) {
            this.toggleService.open([this.toggleGroupId, index]);

            this.events.broadcast('offcanvas:open', { index: `${index}`, item });
        }
    }

    toggleClose(value: NToggle.TId): void {
        this.toggleService.close(value);

        const [groupId, index] = value;
        const tmpIndex = `${index}`.split('-');

        if (tmpIndex?.length > 1) {
            // open previous level
            this.toggleService.open([groupId, tmpIndex.slice(0, -1).join('-')]);

            this.level = tmpIndex.length - 1;
            this.clickedItems.pop();
        } else {
            this.level = 0;
            this.clickedItems = [];
        }

        this.detectChanges();
    }

    ifCustomerGroup(value: NCustomerGroups.TId | NCustomerGroups.TId[]): boolean {
        return this.customerGroupsService.isAllowed(value);
    }

    getToggleId(index: NToggle.TId) {
        return [this.toggleGroupId, index];
    }

    /**
     * Check if menu item body can be printed (if was requested already)
     */
    canPrintContentBody(index: number | string): boolean {
        if (this.client.isPrerender || this.client.isUniversal || this.client.isCms) {
            return true;
        }

        return this.everVisibleItemsIndex[`${index}`];
    }

    /**
     * Open size selection modal
     */
    openSearch(location?: string): void {
        this.close();
        this.sendGtmEvent('openSearch' + location);
        this.toggleService.open([this.searchOutletToggleId, 'header']);
    }

    close() {
        if (this.offCanvasState.value) {
            this.offCanvasState.next(NOffCanvas.EOpenState.closed);
            this.sendGtmEvent('closeOffCanvas');
        }
    }

    private sendGtmEvent(event: string): void {
        this.gtmService.push({ event });
    }
}

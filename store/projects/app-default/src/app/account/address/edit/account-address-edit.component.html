<account-view-content title="Address edit"
                      url="/account/address-book">
    <div class="p-b b-t-x b-t-l b-t-m p-t-1-s">
        <form autocomplete="off"
              method="POST"
              name="account_address"
              #form="ngForm">
            <address-fieldset class="w-12 m-b"
                              [form]="form"
                              [submitSubject]="submitSubject"
                              [data]="data"
                              [phoneFlags]="true" />

            <checkbox class="checkbox m-b w-6 w-12-s"
                      name="is_billing"
                      cy-addressAddBilling
                      [(ngModel)]="data.is_billing">{{'Set as default billing address' | translate}}
            </checkbox>

            <checkbox class="checkbox m-b w-6 w-12-s"
                      name="is_shipping"
                      cy-addressAddShipping
                      [(ngModel)]="data.is_shipping">
                {{'Set as default shipping address' | translate}}
            </checkbox>

            <button class="button w-12-s"
                    cy-addressAddSave
                    [action]="status"
                    (click)="submit()">
                <span>{{'Save address' | translate}}</span>
            </button>

            <result class="block"
                    [status]="status"
                    [success]="'Address saved' | translate" />
        </form>
    </div>
</account-view-content>
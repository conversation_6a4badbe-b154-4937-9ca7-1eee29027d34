import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { AccountAddressEditComponentDef } from '@df/account/account-address-edit.component.def';
import { IRequestCallback } from '@df/api/request/request.service';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { Subject } from 'rxjs/internal/Subject';
import { ActionComponent } from '../../../ui/api/action.component';
import { ResultComponent } from '../../../ui/api/result.component';
import { AddressFieldsetComponent } from '../../../ui/form/address-fieldset/address-fieldset.component';
import { CheckboxComponent } from '../../../ui/form/checkbox';
import { AccountViewContentComponent } from '../../view/content/account-view-content.component';

@Component({
    selector: 'account-address-edit',
    templateUrl: './account-address-edit.component.html',
    standalone: true,
    imports: [
        AccountViewContentComponent,
        ActionComponent,
        AddressFieldsetComponent,
        CheckboxComponent,
        FormsModule,
        ResultComponent,
        TranslatePipe
    ]
})
export class AccountAddressEditComponent extends AccountAddressEditComponentDef {
    submitSubject = new Subject<boolean>();

    override submit(onSuccess: IRequestCallback = [], onError?: IRequestCallback): void {
        this.submitSubject.next(true);
        super.submit(onSuccess, onError);
    }
}

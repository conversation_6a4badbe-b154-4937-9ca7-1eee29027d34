<account-view-content [title]="pageTitle"
                      [url]="'/account'">
    <!-- default addresses -->
    <address-book-defaults class="block b-t-x b-t-l b-t-m p-t p-t-1-s" />
    <!-- additional addresses -->
    @if (addresses.length > 0) {
    @for (address of additionalAddresses; track address.id) {
    <address-wrap class="c-6-set c-12-set-s p-a-3 p-a-4-l p-l-7-s p-t-4-s bg-col-33 b-radius-20 m-b-5-s"
                  [model]="address"
                  (addressRemoved)="handleRemoveAddress()" />
    }
    } @else {
    <div class="m-b p1 m-l-5-s">{{'You have no addresses saved yet' | translate}}</div>
    }
    <!-- add new button -->
    <div class="w-12 m-t-1">
        <a class="button w-3-l w-12-s"
           cy-addNewAddressBtn
           [routerLink]="'/account/address'">
            <span>{{'Add new address' | translate}}</span>
        </a>
    </div>
</account-view-content>
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AbstractCustomerComponent } from '@df/account/v2/abstract-customer.component';
import { Address } from '@df/session/api/address-book/address';
import { AddressBook } from '@df/session/api/address-book/address-book';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import uniq from 'lodash-es/uniq';
import { AddressBookDefaultsComponent } from '../../../ui/display/address-book-defaults.component';
import { AddressWrapComponent } from '../../../ui/display/address-wrap.component';
import { AccountViewContentComponent } from '../../view/content/account-view-content.component';

@Component({
    selector: 'account-address-book',
    templateUrl: './account-address-book.component.html',
    standalone: true,
    imports: [AccountViewContentComponent, AddressBookDefaultsComponent, AddressWrapComponent, RouterModule, TranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AccountAddressBookComponent extends AbstractCustomerComponent {
    readonly addressBook = inject(AddressBook);

    private isAddress = (address: Address | undefined): address is Address => !!address;
    addresses: Address[] = [];
    additionalAddresses: Address[] = [];

    get pageTitle(): string {
        return this.client.isS ? 'My Addresses' : 'Address book';
    }

    override ngOnInit(): void {
        this.onCustomerDataChange();

        super.ngOnInit();
    }

    handleRemoveAddress() {
        this.customer.model.load(true);
    }

    protected override onCustomerDataChange(): void {
        const addressBook = this.addressBook;
        this.addresses = uniq([addressBook.shipping, addressBook.billing, ...addressBook.additional].filter(this.isAddress));
        this.additionalAddresses = addressBook.additional.filter(this.isAddress);

        super.onCustomerDataChange();
    }
}

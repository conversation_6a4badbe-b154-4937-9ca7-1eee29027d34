<account-view-content [title]="showTitle ? 'Email preferences' : undefined"
                      [url]="'/account'">
    @if (content) {
    <div class="p-t-2 p-t-0-s"
         [ngClass]="{'b-t-x b-t-l b-t-m': showTitle}">
        <div class="bg-col-34 p-a-9 p-l-5-s p-r-5-s p-t-6-s p-b-6-s b-radius-20">
            <div class="w-5 w-12-s">
                <div class="pos-relative">
                    <p class="h2">{{content.title}}</p>
                    <i class="svg-icon--envelope fs-11 fs-8-s pos-absolute right-0 top--2 right-2-s top-0-s"></i>
                </div>
                <p [sizeClass]="'!S:p1, S:c1'"
                   class="m-t-2 m-t-3-s">{{content.copy}}</p>
                <div class="m-t-5"
                     *ifSize="'S'">
                    <ng-container *ngTemplateOutlet="contentSections" />
                </div>
                <href [link]="exponeaLink"
                      [hrefClass]="'m-t-3 m-t-6-s button-1 block w-12'"
                      [hrefTarget]="'_blank'">
                    <span>{{content.ctaLabel}}</span>
                </href>
                <p [sizeClass]="'!S:p2, S:c1'"
                   class="m-t-3 m-t-4-s"
                   [innerHTML]="content.unsubscribeCopy"></p>
            </div>
            <div class="w-7 p-l p-t p-l-9-x p-l-9-l"
                 *ifSize="'!S'">
                <ng-container *ngTemplateOutlet="contentSections" />
            </div>
        </div>
    </div>

    <ng-template #contentSections>
        @for (item of content.sections; track $index) {
        <div class="w-4 center">
            <img class="w-9 w-8-s"
                 [dfImage]="item.image"
                 [ratio]="1/1">
            <p class="c1 m-t-1"
               [innerHTML]="item.title"></p>
        </div>
        }
    </ng-template>
    }
</account-view-content>
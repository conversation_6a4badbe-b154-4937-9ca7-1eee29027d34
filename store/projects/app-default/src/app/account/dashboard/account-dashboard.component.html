@if (client.isS || client.isM) {
<div class="w-12 m-b-8 m-t-8">
    <p class="h2">
        {{'Hello' | translate}}, <span data-cs-mask>{{customer.model.data.firstname}}
            {{customer.model.data.lastname}}!</span>
    </p>
    <p class="flex flex-justify-between">
        <span class="s1"
              data-cs-mask>{{customer.model.data.email}}</span>
        <account-ui-logout-button-inline />
    </p>
</div>
}
<div class="w-12 bg-col-33-x bg-col-33-l">
    <account-ui-futura-barcode />

    <cms-outlet-block name="Account top components"
                      [variant]="'account-top-components'"
                      [allowOnly]="allowOnly" />

    @for (item of dashboardList; track $index) {
    <href class="c-6-set account-card"
          [ngClass]="[$even ? 'm-r-0-s' : 'm-r-2-s', item.additionalClass]"
          [link]="client.isS ? item.link : undefined">
        @if (item.type === 'gift-card') {
        <div class="account-card--border p-a-2 p-t-5-s p-b-5-s p-l-3-s p-r-3-s center m-b-0-s flex flex-middle flex-justify-center"
             style="min-height: inherit;"
             [ngClass]="customer.model.data.store_credit_balance ? 'bg-col-23' : 'bg-col-w bg-col-5-m bg-col-5-s'">
            <div class="flex flex-column flex-gow flex-justify-between">
                <div>
                    <i class="fs-3 fs-5-s block-s p-t-1-s"
                       [ngClass]="item.icon"
                       aria-label="true"></i>
                    @if (client.sizeId>0) {
                    <div class="s1 p-t-1">{{item.title | translate}}</div>
                    }
                    <div class="p-t-1-s m-b-2-s">
                        @if (client.sizeId<1) {
                            <span
                            class="s2">{{item.title | translate}}&nbsp;</span>
                            }
                            <price class="col-11 p-t-2"
                                   [sizeClass]="'S: p2,!S: hero'"
                                   [precision]="2"
                                   [value]="customer.model.data.store_credit_balance || 0" />
                    </div>
                </div>
            </div>
        </div>
        } @else {
        <div class="p-a-5 bg-col-w bg-col-5-s"
             style="min-height: inherit;">
            <div class="flex-column flex-justify-between">
                <div class="flex flex-column-s flex-middle-s m-b-2 m-b-0-s">
                    <i class="m-l--2 m-l-0-s m-r-2 m-r-0-s fs-7 fs-5-s block-s p-t-2-s m-b-2"
                       [ngClass]="item.icon"
                       aria-hidden="true"></i>
                    <div class="s1">
                        {{item.title | translate}}</div>
                </div>
                @if (client.sizeId>0) {
                <p class="m-l">{{item.subtitle | translate}}</p>
                <a class="p1 m-t-10 m-l"
                   [routerLink]="item.link">{{item.link_text | translate}}</a>
                }
            </div>
        </div>
        }
    </href>
    @if ($index === [0,0,1,1][client.sizeId]) {
    <cms-outlet-block name="Account middle components"
                      [variant]="'account-middle-components'"
                      [allowOnly]="allowOnly" />
    }
    }
    <cms-outlet-block name="Account bottom components"
                      [variant]="'account-bottom-components'"
                      [allowOnly]="allowOnly" />
</div>
<account-dashboard-carousel />
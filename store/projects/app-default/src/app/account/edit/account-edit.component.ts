import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { AccountEditComponentDef } from '@df/account/account-edit.component.def';
import { LoqateEmailValidateDirective } from '@df/module-loqate/email/loqate-email.validator';
import { MatchCheckDirective } from '@df/ui/form/match-check.directive';
import { ValidatePatternDirective } from '@df/ui/form/validate-pattern.directive';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ActionComponent } from '../../ui/api/action.component';
import { ResultComponent } from '../../ui/api/result.component';
import { CalendarDropdownComponent } from '../../ui/form/calendar-dropdown.component';
import { InputWrapBodyDirective } from '../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../ui/form/input-wrap.component';
import { MuiInputDirective } from '../../ui/form/mui-input.directive';

declare module '@df/account/account.interfaces' {
    namespace Account {
        interface IEditFormData {
            dob?: string;
        }
    }
}

@Component({
    selector: 'account-edit',
    templateUrl: './account-edit.component.html',
    standalone: true,
    imports: [
        ActionComponent,
        CalendarDropdownComponent,
        FormsModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        LoqateEmailValidateDirective,
        MatchCheckDirective,
        MuiInputDirective,
        ResultComponent,
        TranslatePipe,
        ValidatePatternDirective
    ]
})
export class AccountEditComponent extends AccountEditComponentDef implements OnInit {
    dobDisabled = false;

    get dob(): Date | undefined {
        return this._dob;
    }

    set dob(value: Date | undefined) {
        this._dob = value;
        if (value?.getTime()) {
            this.data.dob = `${value.getFullYear()}-${value.getMonth() + 1}-${value.getDate()}`;
        } else {
            this.data.dob = undefined;
        }
    }

    private _dob?: Date;

    @ViewChild('emailInput', { static: false }) emailInput!: ElementRef<HTMLInputElement>;

    override ngOnInit() {
        super.ngOnInit();
        this._dob = new Date(this.data.dob);
    }

    override reset() {
        super.reset();
        this.dobDisabled = !!this.data.dob;
        this.detectChanges();
    }

    handleKeyUp(event?: KeyboardEvent) {
        if (event?.key === 'Enter') {
            this.emailInput?.nativeElement?.blur();

            this.submit();
        }

        if (event && this.form['submitAttempt']) {
            this.form['submitAttempt'] = false;

            this.detectChanges();
        } else if (!event && this.data?.email?.length) {
            if (!this.form['submitAttempt']) {
                this.form['submitAttempt'] = true;
                this.form.ngSubmit.emit();
            } else {
                this.form['submitAttempt'] = false;
            }

            this.detectChanges();
        }
    }
}

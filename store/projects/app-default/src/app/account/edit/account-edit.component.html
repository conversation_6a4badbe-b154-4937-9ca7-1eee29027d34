<form #form="ngForm"
      method="POST"
      novalidate="true"
      name="account_details">

    <div class="w-8 w-12-m w-12-s">
        <!-- First & Last name -->
        <div class="c-6 c-12-s m-r-0-s pos-relative">
            <input-wrap>
                <input class="input"
                       type="text"
                       name="firstname"
                       [muiInput]="'First name' | translate"
                       [(ngModel)]="data.firstname"
                       required
                       firstname
                       data-cs-mask
                       #firstnameInput>
            </input-wrap>
        </div>

        <div class="c-6 c-12-s m-r-0 pos-relative">
            <input-wrap>
                <input class="input"
                       type="text"
                       name="lastname"
                       [muiInput]="'Surname' | translate"
                       [(ngModel)]="data.lastname"
                       required
                       lastname
                       data-cs-mask
                       #lastnameInput>
            </input-wrap>
        </div>

        <!--E-mail address-->
        <div class="w-12 block pos-relative">
            <input-wrap [canShowValidate]="emailInput?.value?.length > 0 && form.submitAttempt"
                        [messages]="{
                            email: (('Valid email is required' | translate) | translate),
                            required: (('Valid email is required' | translate) | translate),
                            LOQATE_INVALID_EMAIL: (('Valid email is required' | translate) | translate)
                        }">
                <input class="input"
                       type="text"
                       name="email"
                       required
                       data-cs-mask
                       validatePattern="email"
                       [loqateEmail]="true"
                       [muiInput]="'Email' | translate"
                       [(ngModel)]="data.email"
                       [ngModelOptions]="{updateOn: 'blur'}"
                       (keyup)="handleKeyUp($event)"
                       (blur)="handleKeyUp()"
                       #emailInput>
            </input-wrap>
        </div>

        <h2 class="h2 m-t-6">{{'My birthday' | translate}}</h2>
        <calendar-dropdown [(ngModel)]="dob"
                           [disabled]="dobDisabled"
                           name="dob" />

        <!--Password-->
        <h5 class="h2 m-t-6">{{'Change Password' | translate}}</h5>

        <p class="p2">{{'Leave the field blank if you don\'t want to change password' | translate}}</p>

        <input-wrap class="c-6 c-12-s">
            <input class="input"
                   type="password"
                   name="password"
                   data-cs-mask
                   [attr.placeholder]="'Enter password' | translate"
                   [muiInput]="'New password' | translate"
                   [(ngModel)]="data.password">
        </input-wrap>

        <!--Password confirmation-->
        <input-wrap class="c-6 c-12-s m-r-0">
            <input class="input"
                   type="password"
                   name="confirmPassword"
                   data-cs-mask
                   [attr.placeholder]="'Confirm password' | translate"
                   matchCheck="password"
                   [muiInput]="'Confirm new password' | translate"
                   [(ngModel)]="data.confirmPassword">
        </input-wrap>

        <!--Action-->
        <button class="button m-t-2 p-r-8 p-l-8 w-6 w-12-s"
                sl-button="save"
                [action]="status"
                (click)="submit()">
            <span>{{'Save details' | translate}}</span>
        </button>

        <result [status]="status"
                sl-result="save"
                [success]="'Your account data has been saved' | translate"
                class="block m-t-1"></result>
    </div>
</form>
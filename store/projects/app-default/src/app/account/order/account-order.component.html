<account-view-content title="My orders"
                      url="/account">
    @if (!!orders.length) {
    <div class="b-t b-b m-r--4-s m-l--4-s">
        <div class="w-6 center p-t-2 p-t-3-l p-b-2 cursor-pointer"
             [sizeClass]="'XL:h2, SM:s1'"
             [toggle]="['orders', 'online']"
             [toggleClass]="'!bg-col-34 bg-col-w'">{{'Online orders' | translate}}</div>
        <div class="w-6 center bg-col-34 p-t-2 p-t-3-l p-b-2 cursor-pointer"
             [sizeClass]="'XL:h2, SM:s1'"
             [toggle]="['orders', 'instore']"
             [toggleClass]="'bg-col-w !bg-col-34'">{{'In store orders' | translate}}</div>
    </div>
    <!-- orders list -->
    <div [hideUp]="['orders', 'online']"
         [open]="true">
        @if (client.sizeId>0) {
        <div class="w-12 p-t-3 p-b-3 p-l-2 b-b b-col-4 h3">
            <div class="w-2 w-4-m fw-bold">{{'Order number' | translate}}</div>
            <div class="w-2 w-3-m fw-bold">{{'Date' | translate}}</div>
            <div class="w-2 w-3-m fw-bold">{{'Delivery to' | translate}}</div>
            <div class="w-2 fw-bold">{{'Price' | translate}}</div>
            <div class="w-2 fw-bold"
                 *ifSize="'!M'">{{'Order status' | translate}}</div>
        </div>
        }
        @for (order of orders | orderBy : ['id'] : ['desc']; track order.id) {
        <div class="p-t-3 p-l-2 p-l-4-s p-r-4-s p-l-4-m p-r-4-m p-b-2 p-b-4-s b-b b-col-4 m-t-2-s m-t-2-m m-l--4-s m-r--4-s"
             [sizeClass]="'XL:p1, SM:s1'">
            <div class="w-2 w-4-m w-12-s p-l-5-s">
                @if (client.isS) {
                <span class="fw-bolder">{{'Order number' | translate}}:</span>
                }
                {{order.data.number}}
                @if (client.isM) {
                <p class="m-t-2">
                    <a class="link"
                       [routerLink]="'/account/order/' + order.id">{{'View order' | translate}}</a>
                </p>
                }
            </div>
            <div class="w-2 w-3-m w-12-s flex-s flex-justify-between p-l-5-s p-r-4-s">
                @if (client.isS) {
                <span class="fw-regular">{{'Date' | translate}}:</span>
                }
                <span class="fw-regular">{{order.data.date | date : 'mediumDate'}}</span>
            </div>
            <div class="w-2 w-3-m w-12-s p-l-5-s p-r-4-s">
                @if (order.data.billing) {
                <span class="flex-s flex-justify-between">
                    @if (client.isS) {
                    <span class="fw-regular">{{'Delivery to' | translate}}:</span>
                    }
                    <span class="fw-regular"
                          data-cs-mask>
                        {{order.data.billing.firstname}} {{order.data.billing.lastname}}
                    </span>
                </span>
                }
            </div>
            <div class="w-2 w-12-s flex-s flex-justify-between p-l-5-s p-r-4-s">
                @if (client.isS) {
                <span class="fw-regular">{{'Price' | translate}}:</span>
                }
                <price class="fw-regular"
                       [value]="order.data.totals.subtotal"
                       [precision]="2"></price>
            </div>
            @if (!client.isM) {
            <div class="w-1 w-12-s flex-s flex-justify-between p-l-5-s p-r-4-s">
                @if (client.isS) {
                <span class="fw-regular">{{'Status' | translate}}:</span>
                }
                <span class="fw-bold">{{order.data.status}}</span>
            </div>
            <div class="w-3 w-12-s right">
                <a class="m-t-2-s"
                   [sizeClass]="'!S:button-1 button-size-2 b-radius-max b-col-4-i, S: button w-12'"
                   style="margin-top:-1em;"
                   [routerLink]="'/account/order/' + order.id">
                    <span>{{'View Order' | translate}}</span>
                </a>
            </div>
            }
        </div>
        }
    </div>
    <div [hideUp]="['orders', 'instore']">
        <p class="p1 m-t m-l-5-s">{{'You haven\'t ordered any items yet' | translate}}</p>
    </div>
    } @else {
    <!-- no orders -->
    <div class="center-x center-l center-m">
        <p class="p1 m-t m-l-5-s">{{'You haven\'t ordered any items yet' | translate}}</p>
        <a class="m-t button w-12-s"
           [routerLink]="'/'"><span>{{'Start shopping' | translate}}</span></a>
    </div>
    }
</account-view-content>
<account-view-content [title]="'My orders'"
                      [url]="'/account/my-orders'">
    @if (order) {
    <div class="b-t-x b-t-l b-t-m">
        <div class="w-6 w-12-s p-t-2 p-t-0-s p-b-2 p-l-5 fw-bold-s fw-bold-m">
            <p [sizeClass]="'XL:h2, SM:h3'">{{'Order number' | translate}}: {{order.data.number}}</p>
            <p class="fw-regular-s fw-regular-m"
               [sizeClass]="'XL:h3, SM:p1'">{{'Date' | translate}}: {{order.data.date | date : 'mediumDate'}}</p>
        </div>

        <div class="w-6 w-12-s bg-col-34 p-t-2 p-t-3-l p-b-2 center fw-bold h3 m-t-1-s">
            {{'Status' | translate}}: {{order.data.status}}
        </div>
    </div>

    <div class="m-t-2 m-t-4-s m-t-4-m">
        @if (order.shipping) {
        <div class="c-6-set p-l-5-l p-l-5-x p-l-4-s p-l-4-m m-t-0-l">
            <h6 class="fw-bold p1">{{'Shipping address' | translate}}</h6>
            <address class="m-t-2"
                     [model]="order.shipping"></address>
        </div>
        }
        @if (order.billing) {
        <div class="c-6-set p-l-4-s p-l-4-m">
            <h6 class="fw-bold p1">{{'Billing address' | translate}}</h6>
            <address class="m-t-2"
                     [model]="order.billing"></address>
        </div>
        }

        @if (order.data.shipping_method) {
        <div class="c-6-set m-t-3 p1 p-l-5-l p-l-5-x p-l-4-s p-l-4-m">
            <h6 class="fw-bold">Shipping method</h6>
            <div class="m-t-1-s m-t-1-m"
                 [innerHtml]="order.data.shipping_method.description">
            </div>
        </div>
        <div class="c-6-set m-t-3 p1 p-l-4-s p-l-4-m">
            <h6 class="fw-bold">{{'Payment' | translate}}</h6>
            <div class="m-t-1-s m-t-1-m">
                {{order.data.payment_method.description}}
            </div>
        </div>
        }
    </div>
    <div class="p-b-2 b-b b-col-4 fw-bold m-t h3">
        @if (client.isS) {
        <h6 class="p-l-4-s">{{'Products' | translate}}</h6>
        } @else {
        <div class="c-6 m-b-0 p-l-5-l p-l-5-x p-l-4-m">{{'Product' | translate}}</div>
        <div class="c-2 left c-4-s m-b-0 p-l-4-m">{{'Price' | translate}}</div>
        <div class="c-2 center c-4-s m-b-0">{{'Qty' | translate}}</div>
        <div class="c-2 m-r-0 m-b-0 right p-r-6-l p-r-6-x p-r-3-m">{{'Total' | translate}}</div>
        }
    </div>
    @for (item of order.items; track item.id) {
    <div class="b-b b-col-4 p-b-2 p-t-2">
        <div class="c-6 c-12-s m-b-0 p1 p-l-5-l p-l-5-x p-l-4-s p-l-4-m">
            @if (client.isS) {
            {{item.data.qty_ordered}} x
            }
            <href [link]="item.product?.data?.url">{{item.product?.data?.name || item.data.name}}</href>
        </div>
        <div class="c-2 c-6-s m-b-0 left p1 p-l-4-s p-l-4-m">
            {{'Price' | translate}}:&nbsp;
            <price [value]="item.data.price"
                   [precision]="2" />
        </div>
        @if (client.sizeId>0) {
        <div class="c-2 m-b-0 center p1">{{item.data.qty_ordered}}</div>
        }
        <div class="c-2 c-6-s m-r-0 m-b-0 right p1 p-r-6-l p-r-6-x p-r-3-s p-r-3-m">
            {{'Total:' | translate}}&nbsp;
            <price [value]="item.data.row_total_incl_tax"
                   [precision]="2" />
        </div>
    </div>
    }
    <div class="right p1 p-r-6-l p-r-6-x p-r-3-s p-r-3-m">
        <p class="m-t-7">
            {{'Total items Price' | translate}}:&nbsp;
            <price [value]="order.data.totals.subtotal"
                   [precision]="2" />
        </p>
        <p class="m-t-1">
            {{'Delivery Cost' | translate}}:&nbsp;
            <price [value]="order.data.totals.delivery"
                   [precision]="2" />
        </p>
        @if (order.data.totals.discount) {
        <p class="m-t-1">
            {{'Discount' | translate}}:&nbsp;
            <price [value]="order.data.totals.discount"
                   [precision]="2" />
        </p>
        }
        <p class="m-t-1 fw-bold h3">
            {{'Total price' | translate}}:&nbsp;
            <price [value]="order.data.totals?.grand_total || 0"
                   [precision]="2" />
        </p>
    </div>
    }
</account-view-content>
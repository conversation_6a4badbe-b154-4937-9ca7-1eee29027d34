@if (display) {
<div class="c-12 p-a-8 p-t-12-s p-b-12-s flex-column flex-middle p-l-6-s p-r-6-s"
     [ngStyle]="{'background': content?.background || ''}">
    <p class="m-b-6 center p1"
       *iCms="'copy' of content; content: 'html'; skipEmpty: true"></p>
    <div class="center w-9-s p-r-2-s p-l-2-s w-4 p-r-3 p-l-3">
        <img class="w-12"
             [image]="barcode">
        <p class="flex flex-justify-between p-t-1 fs-4-s">
            @for (letter of futuraIdNumbers; track $index) {
            <span>{{letter}}</span>
            }
        </p>
    </div>
    <div class="center w-12 pos-relative">
        <action class="button m-t"
                [status]="sendStatus"
                (click)="sendBarcode()">
            <span>{{content?.cta_copy || 'Send to my email address'}}</span>
        </action>
        <result class="pos-absolute top-100 p-t-1 left-0 right-0 center"
                [status]="sendStatus"
                [success]="content?.success_message" />
    </div>
</div>
}
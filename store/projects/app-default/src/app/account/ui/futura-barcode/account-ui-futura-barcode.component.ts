import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { App } from '@df/core/app';
import { Status } from '@df/core/status';
import { ImageDirective } from '@df/module-image/image.directive';
import { ICmsClientModule } from '@icms/client/icms-client.module';
import { ICmsComponent, ICmsContent, NICmsContentComponent, NICmsContentField } from '@icms/core/content';
import { AbstractCmsBlockComponent } from '../../../cms/block/abstract-cms-block.component';
import { ActionComponent } from '../../../ui/api/action.component';
import { ResultComponent } from '../../../ui/api/result.component';

export interface IAccountFuturaBarcodeContentBlockData extends NICmsContentComponent.IData {
    enable?: boolean;
    copy?: string;
    cta_copy: string;
    background?: string;
    success_message?: string;
}

declare module '@df/session/customer/customer.interfaces' {
    namespace Customer {
        interface ICustomerData {
            futura_customer_barcode?: string;
            futura_customer_id_formatted?: number;
        }
    }
}

@Component({
    selector: 'account-ui-futura-barcode',
    templateUrl: './account-ui-futura-barcode.component.html',
    standalone: true,
    imports: [ActionComponent, CommonModule, ICmsClientModule, ImageDirective, ResultComponent],
    changeDetection: ChangeDetectionStrategy.OnPush
})
@ICmsComponent<AccountUiFuturaBarcodeComponent>({
    id: 'account-futura-barcode',
    block: {
        url: 'account-futura-barcode'
    },
    onDemandOnly: true,
    name: 'Account Futura Barcode',
    fields: [
        <NICmsContentField.IField>{
            type: NICmsContentField.EFieldType.toggle,
            key: 'enable',
            title: 'Enable barcode'
        },
        <NICmsContentField.IField>{
            type: NICmsContentField.EFieldType.text,
            key: 'copy',
            title: 'Copy'
        },
        <NICmsContentField.IField>{
            type: NICmsContentField.EFieldType.text,
            key: 'cta_copy',
            title: 'CTA Copy',
            default: 'Send to my email address',
            required: true
        },
        <NICmsContentField.IField>{
            type: NICmsContentField.EFieldType.text,
            key: 'success_message',
            title: 'Success Message',
            default: 'Barcode was successfully sent to your email address'
        },
        <NICmsContentField.IField>{
            type: NICmsContentField.EFieldType.colorPicker,
            key: 'background',
            title: 'Background'
        }
    ]
})
export class AccountUiFuturaBarcodeComponent extends AbstractCmsBlockComponent implements NICmsContentComponent.IComponent, OnInit {
    @ICmsContent({
        type: 'blockContent'
    })
    override content!: IAccountFuturaBarcodeContentBlockData;

    sendStatus = new Status();

    protected app = inject(App);

    override ngOnInit() {
        super.ngOnInit();
        this.subscriptions.add(this.sendStatus.subscribe(() => this.detectChanges()));
    }

    get barcode(): string | undefined {
        return this.app.customer.model.data?.futura_customer_barcode;
    }

    get futuraId(): number | undefined {
        return this.app.customer.model.data?.futura_customer_id_formatted;
    }

    /**
     * Returns futura id as array of strings
     * for styling only
     * @returns {string[] | undefined}
     */
    get futuraIdNumbers(): string[] | undefined {
        const id = this.futuraId;
        if (id) {
            return id.toString().split('');
        }
        return undefined;
    }

    get display(): boolean {
        return !!(this.futuraIdNumbers && this.barcode && this.content?.enable);
    }

    /**
     * Send request to api to send email with barcode
     */
    sendBarcode(): Promise<undefined> {
        return this.app.request.send(this.sendStatus, {
            path: 'barcode/email'
        });
    }

    protected _isEmpty(): boolean {
        return !this.display;
    }
}

<div class="wrap-x wrap-l">
    @if (!!wishlist.items.length) {
    <div class="flex flex-justify-between flex-bottom m-t p-b-2">
        <div class="p2 col-13 w-2">
            {{wishlist.items.length}} {{wishlist.items.length === 1 ? 'product' : 'products'}}
        </div>
        <h2 class="h2">{{'Wishlist' | translate}}</h2>
        <p class="w-2"></p>
    </div>
    <!-- orders list -->
    <div class="m-t flex flex-wrap">
        @for (item of wishlist.items | wishlistSort; track $index) {
        <div class="c-6-set-s c-4-set-x c-4-set-m c-4-set-l p-b-2 flex">
            <product class="pos-relative flex flex-column w-12"
                     [listPosition]="$index"
                     [wishlistItem]="item"
                     [wishlistCarouselItem]="true"
                     [modelId]="item.productId"
                     [listPosition]="$index"
                     [listId]="'wishlist'"
                     [listName]="'Wishlist'" />
        </div>
        }
    </div>
    } @else {
    <!-- no items -->
    <p>{{'You haven\'t added any products to your list yet.' | translate}}</p>
    }
</div>

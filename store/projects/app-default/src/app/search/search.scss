@use '~@tomandco/atomic/scss/atom/color';
@use '~@tomandco/atomic/scss/core';

:host {
    display: flex;
    width: 100%;
    justify-content: flex-end;

    .search__bg {
        position: absolute;
        z-index: 1;
        background-color: #fff;
        inset: 0;
        opacity: 0;
        pointer-events: none;
        transition: opacity 250ms ease-out 250ms;

        .size-m &,
        .size-s & {
            display: none;
        }

        &::before {
            position: absolute;
            display: block;
            background-color: #fff;
            content: '';
            inset: 0;
            opacity: 0;
            transform: translateY(0%);
            transition: transform 0ms ease-out 500ms, opacity 250ms ease-out 250ms;
        }
    }

    .search__icon {
        display: flex;
        width: core.px-to-rem(48);
        height: core.px-to-rem(48);
        align-items: center;
        justify-content: center;
    }

    .search__close {
        .size-x &,
        .size-l & {
            opacity: 0;
            pointer-events: none;
            transition: opacity 0ms ease-out 0ms;
        }
    }

    .search__typewrite {
        opacity: 0;
        transition: opacity 50ms ease-out;

        .size-m &,
        .size-s & {
            opacity: 1;
        }
    }

    .search__input {
        z-index: 1;
        width: 0;
        margin-right: 0;
        margin-left: auto;
        opacity: 0;
        transform: translateX(0);
        transition: width 250ms ease-out, margin-right 250ms ease-out, opacity 50ms ease-out 200ms, transform 250ms ease-out;

        .size-m &,
        .size-s & {
            width: calc(100% - core.px-to-rem(16)) !important;
            margin-left: 0 !important;
            opacity: 1;
            transform: translateX(0) !important;
        }

        .input {
            height: 36px;
            padding: 0 0 0 50px;
            border-color: var(--atomic-background-color-4);
            border-radius: core.px-to-rem(10);
            background-color: var(--atomic-background-color-33);
            line-height: 36px;
            opacity: 0;
            transition: opacity 50ms ease-out, box-shadow 250ms linear;


            &:focus {
                border-color: color.get(21);
                box-shadow: 0 0 0 2px color.get(34);
            }

            .size-m &,
            .size-s & {
                height: core.px-to-rem(36);
                padding: 0 0 0 45px !important;
                line-height: core.px-to-rem(36);
                opacity: 1;
            }
        }
    }

    .search-is-focus & {
        color: var(--atomic-color-1);
        pointer-events: auto;

        .search__bg {
            opacity: 1;
            pointer-events: auto;
            transition: opacity 100ms ease-in 20ms;

            &::before {
                opacity: 1;
                transform: translateY(-100%);
                transition: transform 0ms, opacity 0ms;
            }
        }

        .search__typewrite {
            opacity: 1;
            transition: opacity 250ms ease-in;
        }

        .search__input {
            width: core.px-to-rem(626);
            margin-right: 50%;
            opacity: 1;
            transform: translateX(50%);
            transition: width 250ms ease-in, margin-right 250ms ease-in, opacity 10ms ease-in, transform 250ms ease-in;

            .input {
                height: 36px;
                padding: 0 0.5em 0 40px;
                opacity: 1;
                transition: opacity 50ms ease-in;
            }
        }

        .search__close {
            opacity: 1;
            pointer-events: auto;
            transition: opacity 250ms ease-in 250ms;
        }
    }

    ._search,
    ._loading {
        width: 40px;

        .size-m &,
        .size-s & {
            width: 45px;
        }
    }

    ._custom-search-bar {
        .size-m &,
        .size-s & {
            max-height: 60px;
            padding-bottom: 12px;
        }
    }
}

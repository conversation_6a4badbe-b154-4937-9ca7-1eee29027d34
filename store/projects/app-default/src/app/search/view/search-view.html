@if (hasProductResults) {

@if(!app.client.isS) {
<search-banner class="w-12 p-b"
               [term]="result?.term"
               [showOnCategoryPage]="true" />
}

<sticky-free class="sticky-free-search"
             [stickyId]="'search-view-sticky'"
             [stickyOff]="app.client.isCms"
             [order]="1">
    <search-view-input class="wrap flex-column bg-col-w search-view-sticky"
                       [status]="status"
                       [searchId]="'search_page'"
                       [searchTerm]="result?.term" />
</sticky-free>
<h1 class="center h2 _title">{{'Search results: %' | translate : result?.term}}</h1>
<div class="m-t-6 m-t-2-m m-t-2-s"
     [ngClass]="{'p-t-6': !listing?.getTotal()}">
    <p class="wrap-x wrap-l m-l-4-m m-l-4-s p-t-2 p-b-3 p-l-2 p-l-0-s col-13 lh-2 p2"
       cy-searchResultSummary>
        {{listing?.getTotal()}} {{listing?.getTotal() === 1 ? 'result' : 'results' | translate}}
    </p>
    <sticky-free class="sticky-free-listing"
                 [stickyId]="'search-listing-filters'"
                 [stickyOff]="app.client.isCms"
                 [order]="2">
        <product-listing-filters class="w-12"
                                 [listing]="listing"
                                 [productListingComponent]="productListingRef" />
    </sticky-free>
    <product-listing [context]="'search'"
                     [searchResult]="result"
                     [listing]="listing"
                     #productListingRef />
</div>

} @else {

<cms-outlet-block name="Search Page - no results components"
                  [variant]="'search-view-page-no-results'" />
}
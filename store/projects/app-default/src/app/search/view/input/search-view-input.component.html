<div class="w-12 search">
    <!-- search input -->
    <div class="pos-relative search__input m-b-1"
         [sizeClass]="'S:form-size-1'">

        <input class="col-1 p1 input"
               type="text"
               [attr.placeholder]="searchTerm"
               cy-searchInput
               [(ngModel)]="query"
               (keydown)="handleKeyDown()"
               (keyup)="handleKeyUp($event)"
               autofocus="true"
               #input>

        @if (status.busy) {
        <div class="pos-absolute top-0 right-1 bottom-0 flex flex-middle flex-justify-center p-a-2 col-13">
            <i class="icon-loading"
               aria-hidden="true"></i>
        </div>
        } @else {
        <button class="pos-absolute top-0 left-1 bottom-0 flex flex-middle flex-justify-center p-a-2"
                [attr.aria-label]="'Search' | translate"
                (click)="goToSearch()">
            <i class="icon-simple-search flex"
               [style.font-size.px]="24"
               aria-hidden="true"
               cy-searchInputSubmit></i>
        </button>
        }
    </div>
</div>

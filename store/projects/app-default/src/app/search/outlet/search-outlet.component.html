<div [class.p-t-1-s]="!enableMobileSearch"
     [style.--search-top-offset]="enableMobileSearch ? '1.25rem' : '1rem'">
    <!-- directly embedded here, on S its a modal -->
    @if (app.client.isS || app.client.isM) {
    <search-header class="_search-height" />
    }

    <div class="flex-grow custom-scrollbar _search-outlet ng-hide"
         [toggleClass]="'!ng-hide'"
         [toggleClassBy]="[searchOutletToggleId, 'header']">
        <div class="wrap center">
            <div class="w-10-x w-10-l w-12 left"
                 [style.maxHeight.vh]="'85'"
                 [sizeClass]="'S: overflow-y-auto overflow-x-hidden scrollbar-none'">
                @if (!!(result?.categories || result?.pages || productListing?.models?.length)) {
                <!-- results -->
                <search-banner [term]="result?.term"
                               [showOnQuickSearch]="true"
                               [position]="'top'" />

                <div class="m-t-0-m m-t-0-s m-t-12 animation-reveal is-animate"
                     [grid]="12">

                    <!-- search terms -->
                    @if (result?.terms) {
                    <div [gridColumn]="[6,6,2,2][app.client.sizeId]">
                        <h6 class="s1">{{'Related Terms' | translate}}</h6>

                        @for (term of result.terms | slice : 0 : [4,4,8,8][app.client.sizeId]; track $index) {
                        <div class="m-t-1">
                            <a class="block m-t-1-s p-t-1 p-b-1 p1 _term"
                               [routerLink]="term.redirect || ['search', term.barePhrase]"
                               [innerHtml]="term.phrase"></a>
                        </div>
                        }
                    </div>
                    }

                    <!-- categories -->
                    @if (result?.categories) {
                    <div [gridColumn]="[6,6,2,2][app.client.sizeId]">
                        <h6 class="s1">{{result?.terms ? '&nbsp;' : ('Related Categories' | translate)}}</h6>

                        @for (model of result.categories | slice : 0 : [4,4,8,8][app.client.sizeId]; track model.id) {
                        <div class="m-t-1">
                            <a class="block m-t-1-s p-t-1 p-b-1 p1 term"
                               [routerLink]="model.data.url"
                               [innerHtml]="model.data.name"></a>
                        </div>
                        }
                    </div>
                    }

                    @if (productListing?.models?.length) {
                    <div class="animation-reveal is-animate"
                         [gridColumn]="result?.terms ? result?.categories ? [12,12,8,8][app.client.sizeId] : [12,12,10,10][app.client.sizeId] : result?.categories ? [12,12,10,10][app.client.sizeId] : 12"
                         cy-quickSearchProductListing>

                        <h6 class="s1">{{'Related items' | translate}}</h6>

                        <div class="m-t-2 m-b-12-m m-b-20-s"
                             [grid]="[2,2,4,4][app.client.sizeId]"
                             [gridColumnGap]="[2,2,4,4][app.client.sizeId]"
                             [gridRowGap]="[4,4,8,8][app.client.sizeId]"
                             [gtmTrackViewItemList]="gtmTrackViewItemList"
                             [gtmTrackViewItemListOff]="app.client.isCms">
                            @for (model of productListing?.models | slice : 0 : 4; track model.id) {
                            <product [model]="model"
                                     [searchTerm]="result?.term"
                                     [listPosition]="$index"
                                     [listId]="listId"
                                     [listName]="listName"
                                     [gridColumn]="1"
                                     [isSearchOutlet]="true"
                                     [hidePrefix]="true" />
                            }
                        </div>
                    </div>
                    }
                </div>

                <search-banner class="w-12 m-t"
                               [term]="result?.term"
                               [showOnQuickSearch]="true"
                               [position]="'bottom'" />
                } @else {
                <!-- no results -->
                <search-popular-content-block class="w-12 m-t-12 m-t-0-m m-t-0-s p-b-12-m p-b-20-s animation-reveal is-animate" />
                }
            </div>
        </div>
    </div>
</div>
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, forwardRef, HostListener, inject, Input, OnDestroy, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { App } from '@df/core/app';
import { Ab } from '@df/module-ab/ab.namespace';
import { AbService } from '@df/module-ab/ab.service';
import { Gtm } from '@df/module-gtm/gtm.namespace';
import { GtmService } from '@df/module-gtm/gtm.service';
import { GtmTrackViewItemListDirective } from '@df/module-gtm/track/view-item-list/gtm-track-view-item-list.directive';
import { KlevuSearchService } from '@df/module-klevu-search/klevu-search.service';
import { SearchOutletComponentDef } from '@df/search/search-outlet.component.def';
import { GridModule } from '@df/ui/atomic/atom/grid/grid.module';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { ToggleModule } from '@df/ui/toggle/toggle.module';
import { ToggleService } from '@df/ui/toggle/toggle.service';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { BehaviorSubject } from 'rxjs';
import { Subscription } from 'rxjs/internal/Subscription';
import { ProductComponent } from '../../catalog/ui/product/product.component';
import { NOffCanvas } from '../../layout/off-canvas/off-canvas.interface';
import { SearchBannerComponent } from '../banner/search-banner.component';
import { SearchHeaderComponent } from '../header/search-header.component';
import { SearchPopularContentBlockComponent } from '../popular-content-block/search-popular-content-block.component';

export const SEARCH_OUTLET_TOGGLE_ID = 'search-outlet';
export const SEARCH_OUTLET_MODAL_ID = 'search-outlet-modal';

@Component({
    selector: 'search-outlet',
    templateUrl: './search-outlet.component.html',
    styleUrl: './search-outlet.component.scss',
    standalone: true,
    imports: [
        CommonModule,
        GtmTrackViewItemListDirective,
        GridModule,
        forwardRef(() => ProductComponent),
        RouterModule,
        SearchBannerComponent,
        SearchHeaderComponent,
        SearchPopularContentBlockComponent,
        SizeClassDirective,
        ToggleModule,
        TranslatePipe
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SearchOutletComponent extends SearchOutletComponentDef implements OnInit, OnDestroy {
    @Input()
    offCanvasState?: BehaviorSubject<NOffCanvas.EOpenState>;

    @Input()
    enableMobileSearch = false;

    protected subscriptions = new Subscription();

    searchExperimentVariant?: Ab.TVariant;

    readonly searchOutletToggleId = SEARCH_OUTLET_TOGGLE_ID;

    readonly app = inject(App);
    private abService = inject(AbService);
    private gtmService = inject(GtmService);
    private toggleService = inject(ToggleService);

    override ngOnInit() {
        super.ngOnInit();

        (<KlevuSearchService>this.searchService).loadKlevuVars(() => this._ref.detectChanges());

        this.subscriptions.add(
            this.abService.getSubject('search').subscribe(value => {
                const variantId = value.variant;

                if (this.searchExperimentVariant !== variantId) {
                    this.searchExperimentVariant = variantId;

                    this._ref.detectChanges();
                }
            })
        );

        this.subscriptions.add(
            this.app.events.on('search.open').subscribe(value => {
                if (!value) {
                    this.hide();
                    this.gtmService.push({ event: 'closeSearch' });
                }
            })
        );

        this.subscriptions.add(
            this.toggleService.getState([this.searchOutletToggleId, 'header']).subscribe(state => {
                if (state) {
                    this.app.events.broadcast('search.focus');
                }
            })
        );
    }

    override ngOnDestroy() {
        super.ngOnDestroy();

        this.hide();

        this.subscriptions.unsubscribe();
    }

    get listId(): string {
        return 'search';
    }

    get listName(): string {
        return 'Search';
    }

    get gtmTrackViewItemList(): Gtm.ITrackViewItemList {
        return {
            listId: this.listId,
            listName: this.listName,
            models: this.productListing?.models || []
        };
    }

    @HostListener('touchstart')
    onTouchStart() {
        this.app.events.broadcast('search.blur');
    }

    isVariant(variant: string): boolean {
        return this.searchExperimentVariant === variant;
    }

    override hide() {
        super.hide();

        this.toggleService.close([this.searchOutletToggleId, 'header']);

        this._ref.detectChanges();
    }
}

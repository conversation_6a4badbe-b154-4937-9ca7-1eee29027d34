<div class="w-12 _custom-search-bar"
     [class.is-focus]="isFocus">
    <div class="search__bg"></div>
    <div class="pos-relative-m pos-relative-s">

        <!-- search input -->
        <div class="pos-relative search__input"
             [sizeClass]="'S:form-size-1'">
            @if (!status?.busy) {
            <button class="cursor-pointer pos-absolute top-0 left-0 bottom-0 flex flex-middle flex-justify-center _search"
                    type="button"
                    (click)="goToSearch()">
                <i class="icon-simple-search"
                   [style.font-size.px]="24"
                   [style.padding-left.px]="12"
                   [style.line-height]="0"
                   cy-searchInputSubmit></i>
            </button>
            } @else {
            <div
                 class="pos-absolute top-0 left-0 bottom-0 flex flex-middle flex-justify-center col-13 pe-none _loading">
                <i class="fs-3 fs-5-m fs-5-s icon-loading"></i>
            </div>
            }

            <!-- typewrite -->
            @if (content?.texts?.length) {
            <div class="fill flex flex-middle p-l-8 p-l-12-m p-l-12-s pe-none search__typewrite"
                 typewriteEffect
                 [disabled]="!isFocus || !!term.length">
                @for (text of content.texts; track $index) {
                <div class="pos-absolute col-13 p1 no-wrap overflow-hidden"
                     [typewriteEffectText]="text.text"></div>
                }
            </div>
            }

            <input class="b-col-4 input"
                   type="text"
                   cy-searchInput
                   [(ngModel)]="term"
                   (keydown)="handleKeyDown()"
                   (keyup)="handleKeyUp($event)"
                   #input>
            @if (canShowPressEnter) {
            <div class="pos-absolute top-0 right-0 bottom-0 flex flex-middle p-a-2 fs-1 fs-3-m fs-3-s col-13 pe-none"
                 [class._is-busy]="status?.busy">
                {{'Press enter to see more' | translate}}
            </div>
            }
        </div>

        @if (app.client.isX || app.client.isL) {
        <div class="flex flex-middle flex-justify-center pos-absolute top-0 bottom-0 z-1 right-7 search__close">
            <button class="p-a-2 cursor-pointer header__icon"
                    [attr.aria-label]="'Close search' | translate"
                    type="button"
                    (click)="reset()">
                <i class="fs-3 icon-close"
                   [style.line-height]="0"
                   aria-hidden="true"></i>
            </button>
        </div>
        }
    </div>
</div>
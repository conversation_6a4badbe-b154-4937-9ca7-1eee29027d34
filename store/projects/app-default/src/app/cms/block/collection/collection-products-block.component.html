@if (component.isCarousel) {
<div class="flex flex-justify-between p-l p-r m-t m-t-2-m m-t-2-s">
    <div>
        <span *iCms="'title' of component"
              class="h3 m-r-2"></span>
        @if (component.link) {
        <a class="p2 col-13"
           [optHref]="component.link">{{'View all' | translate}}</a>
        }
    </div>
    <div>
        <button class="m-r-2"
                [ngClass]="'_previous' + uid"
                aria-label="Previous slide">
            <i class="icon-chevron-left fs-3"
               aria-hidden="true"></i>
        </button>
        <button [ngClass]="'_next' + uid"
                aria-label="Next slide">
            <i class="icon-chevron-right fs-3"
               aria-hidden="true"></i>
        </button>
    </div>
</div>

<carousel class="m-t-2"
          [style.--pc-slides-ceil]="pcSlidesCeil"
          [style.--pc-slides]="pcSlides"
          [style.--pc-gutter.px]="pcGutter"
          [slidesPerView]="pcSlides"
          [spaceBetween]="pcGutter"
          [loop]="true"
          [navigation]="{
            nextEl: '._next' + uid,
            prevEl: '._previous' + uid
        }">

    @if(models.length) {
    @for (item of models; track item?.data?.id) {
    <ng-template carouselSlide>
        <product [model]="item"
                 [disableCarousel]="true" />
    </ng-template>
    }

    } @else {
    @for (item of component.ids; track $index) {
    <ng-template carouselSlide>
        <product [modelId]="item"
                 [disableCarousel]="true" />
    </ng-template>
    }
    }
</carousel>
}

@else {
<div class="flex flex-justify-between p-l p-r m-t m-t-2-m m-t-2-s">
    <div>
        <span *iCms="'title' of component"
              class="h3 m-r-2"></span>
        @if (component.link) {
        <a class="p2 col-13"
           [optHref]="component.link">{{'View all' | translate}}</a>
        }
    </div>
</div>
<div class="w-12 m-t m-t-2-m m-t-2-s"
     [grid]="client.isS ? 2 : 4"
     [gridColumnGap]="2"
     [gridRowGap]="7">

    @if(models.length) {
    @for (item of models; track item?.data?.id) {
    <product class="w-12 h-100"
             [disableCarousel]="true"
             [model]="item" />
    }
    }

    @else {
    @for (item of component.ids; track $index) {
    <product class="w-12 h-100"
             [disableCarousel]="true"
             [modelId]="item" />
    }
    }
</div>
}
<div class="w-12 center">
    <button class="button-1"
            (click)="closeCollection()"><span class="button__body">{{'Close collection' | translate}}</span>
    </button>
</div>

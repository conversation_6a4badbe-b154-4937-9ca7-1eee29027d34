<div class="bg-col-34 b-radius-4 pos-relative left"
     [ngClass]="!isSuccessPage ? 'm-t m-t-6-s p-a-3' : 'm-t-2'"
     [ngStyle]="{'background-color': content?.bgColor || '#FFFFFF'}">
    <checkbox class="label block w-1 p-l-0 p-t-1"
              name="newsletter"
              sl-input="newsletter"
              [ngModel]="newsletter"
              (ngModelChange)="handleModelChange($event)" />
    <h6 class="p1"
        [ngClass]="content?.image ? 'w-9 m-r-2' : 'w-11'"
        *iCms="'title' of content; content: 'html'; skipEmpty: true"></h6>
    @if (content?.image) {
    <div class="w-1 _image">
        <img class="w-12"
             [dfImage]="content?.image"
             [priority]="content.cwvLoading === 'eager' || content.cwvPriority"
             [ratio]="1">
    </div>
    }
</div>
@if (submitButton && content?.ctaText) {
<action class="w-12 button-size-1 cursor-pointer m-t-1 m-t-4-s"
        [ngClass]="[content.ctaStyle || 'button']"
        [class.disabled]="!newsletter"
        sl-button="subscribe"
        cy-subscribeSubmit
        [status]="submitStatus"
        (click)="submit()">
    <span class="p1"
          *iCms="'ctaText' of content"></span>
</action>
<result class="block col-2 p2"
        sl-result="subscribe"
        cy-subscribeResultMessage
        [status]="submitStatus"
        [success]="'Your email has been submitted.' | translate" />
}

@if (listingAsPages?.models) {
@if (component?.heading) {
<div class="wrap center">
    <h2 class="m-t-6 p-b-6 p-b-10-s"
        [ngClass]="[component.color || 'col-11', component.text_style || 'h2']"
        *iCms="'heading' of component">
    </h2>
</div>
}
@if (listing && tags?.length) {
<div class="center">
    <div class="drag-links"
         dragScroll>
        @for (filter of listing.filters; track filter.id) {
        @if (filter.id === 'primary-tag') {
        <filter-primary-tag class="inline-block m-l-3-s"
                            [filter]="$any(filter)" />
        @for (tag of tags; track $index) {
        <filter-primary-tag class="inline-block"
                            [class.m-r]="$last"
                            [filter]="$any(filter)"
                            [primaryTag]="tag" />
        }
        }
        }
    </div>
</div>
}
<div class="wrap-x p-b center listing-component"
     (infiniteScroll)="listing.increaseLimit()"
     [infinitScrollActive]="listing.getLimit() < listing.getTotal()">
    <div class="w-10-x w-10-l w-12 left">
        @for (group of listingAsBlogPosts?.models | partition : 5 : true; track $index; let isEven = $even; let isOdd =
        $odd) {
        <div class="m-t-8"
             [grid]="client.isS ? 1 : 10"
             [gridColumnGap]="client.isS ? 0 : 2"
             [gridRowGap]="client.isS ? 16 : 10">
            @for (model of group; track model.id) {
            @if ($first) {
            <!-- hero post -->
            <div class="flex"
                 [ngClass]="isEven ? 'p-r-8-x p-r-8-l' : 'p-l-8-x p-l-8-l'"
                 [gridColumn]="client.isS ? 0 : 4"
                 [gridRowSpan]="client.isS ? 1 : 2"
                 [gridColumnOffset]="isOdd ? client.isS ? 0 : 6 : 0"
                 [gridColumnPushRight]="isOdd ? 1 : 0">
                <div class="pos-relative w-12 flex-grow">
                    <div class="pos-absolute top-0 bottom-0 z-1 bg-col-34 blog-listing-background-width"
                         [ngClass]="isEven ? 'right-0' : 'left-0'"></div>
                    <href [link]="model.data.url">
                        <div class="pos-relative z-2 w-12 p-b">
                            <img class="w-12"
                                 [dfImage]="model?.data?.typeData?.image || model.data.image"
                                 [ratio]="1">
                            <div class="m-t wrap-s"
                                 [class.right]="isOdd">
                                <div class="w-9-x w-9-l w-10-m w-12 left">
                                    <!-- date -->
                                    @if (model.data.typeData.date) {
                                    <p class="m-t-2 m-t-3-s c1 uppercase">
                                        {{model.data.typeData.date | date: 'dd' | ordinal}}
                                        {{model.data.typeData.date | date: 'MMMM yyyy'}}</p>
                                    }
                                    <!-- title -->
                                    <h1 class="m-t m-t-1-s h1">
                                        {{model.data?.typeData?.title || model.data.name}}
                                    </h1>
                                    @if (model.data?.typeData?.short_description) {
                                    <div class="m-t-2 m-t-4-s"
                                         [sizeClass]="'!S: p2, S:p3'"
                                         [innerHtml]="model.data?.typeData?.short_description"></div>
                                    }
                                    <!-- button -->
                                    <action class="m-t-3 m-t-4-s button-inline">
                                        {{'Read Now' | translate}}
                                    </action>
                                </div>
                            </div>
                        </div>
                    </href>
                </div>
            </div>
            } @else {
            <!-- standard posts -->
            <div class="wrap-s"
                 [ngClass]="isEven ? 'p-r-8-x p-r-8-l' : 'p-l-8-x p-l-8-l'"
                 [gridColumn]="3">
                <href [link]="model.data.url">
                    <div class="w-12">
                        <img class="w-12"
                             [dfImage]="model.data?.typeData?.image"
                             [ratio]="1">
                        <div class="m-t-2 m-t-3-s p-b-4-s">
                            <!-- date -->
                            @if (model.data.typeData.date) {
                            <p class="c1 uppercase">
                                {{model.data.typeData.date | date: 'dd' | ordinal}}
                                {{model.data.typeData.date | date: 'MMMM yyyy'}}</p>
                            }
                            <!-- title -->
                            <h2 class="m-t-2 h3">
                                {{model.data?.typeData?.title || model.data.name}}
                            </h2>
                            @if (model.data?.typeData?.short_description) {
                            <div class="m-t-2 m-t-4-s"
                                 [sizeClass]="'!S: p2, S:p3'"
                                 [innerHtml]="model.data?.typeData?.short_description"></div>
                            }
                            <!-- read more button -->
                            <action class="m-t-3 m-t-6-s button-inline">
                                {{'Read Now' | translate}}
                            </action>
                        </div>
                    </div>
                </href>
            </div>
            }
            }
        </div>
        }
    </div>
</div>
}
@if (listing?.hasMore) {
<div class="center m-t-4-s">
    <button class="button p-a-3 p-a-6-s m-b-8-s m-b-10"
            (click)="showMore()">
        @if (!client.isS) {
        <span>{{'View more stories' | translate}}</span>
        } @else {
        <span>{{'View more' | translate}}</span>
        }
    </button>
</div>
}
@if (!listingAsBlogPosts?.models) {
<p class="p-t p-b center">{{'No posts to display' | translate}}</p>
}
<ng-form #form="ngForm">
    <div>
        @for(input of inputs; track input.key){

        <div class="c-3-set">
            <p class="p2 m-b-2">Text</p>
            <input-wrap class="input-wrap"
                        [label]="'Label'"
                        [disabled]="disabled()">
                <input class="input"
                       [ngClass]="input.key"
                       type="text"
                       name="text"
                       [required]="required()"
                       placeholder="Placeholder"
                       [(ngModel)]="textModel" />
            </input-wrap>
        </div>

        <div class="c-3-set">
            <p class="p2 m-b-2">Select</p>
            <input-wrap class="input-wrap"
                        [label]="'Label'"
                        [disabled]="disabled()">
                <select class="select"
                        [ngClass]="input.key"
                        name="select"
                        [required]="required()"
                        [(ngModel)]="selectModel">
                    @for (option of options; track option.value) {
                    <option [value]="option.value">{{option.title}}</option>
                    }
                </select>
            </input-wrap>
        </div>

        <div class="c-3-set">
            <p class="p2 m-b-2">Password</p>
            <input-wrap class="input-wrap"
                        [label]="'Label'"
                        [disabled]="disabled()">
                <input class="input"
                       [ngClass]="input.key"
                       type="password"
                       name="password"
                       [required]="required()"
                       placeholder="Placeholder"
                       [(ngModel)]="passwordModel" />
            </input-wrap>
        </div>

        <div class="c-3-set">
            <p class="p2 m-b-2">Textarea</p>
            <input-wrap class="input-wrap"
                        [label]="'Label'"
                        [disabled]="disabled()">
                <textarea class="bg-col-w block"
                          [ngClass]="input.key"
                          name="textarea"
                          [required]="required()"
                          style="max-height: 400px; min-height: 220px; max-width: 306px;"
                          placeholder="Placeholder"
                          [(ngModel)]="textareaModel"></textarea>
            </input-wrap>
        </div>
        }
    </div>
    <div class="c-3-set">
        <p class="p2 m-b-2">Radio</p>
        <input-wrap [disabled]="disabled()">
            <radio-group name="radioGroup"
                         [required]="required()"
                         [(ngModel)]="radioGroupModel">
                @for(radio of ['A', 'B', 'C']; track radio ){
                <radio class="w-12 m-t"
                       [value]="radio">
                    <p class="p1 w-10">
                        {{radio}}. Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor
                        incididunt ut labore
                    </p>
                </radio>
                }

            </radio-group>
        </input-wrap>
    </div>
    <div class="c-3-set">
        <p class="p2 m-b-2">Checkbox</p>
        <input-wrap>
            <checkbox class="checkbox"
                      [(ngModel)]="checkboxModel"
                      name="checkbox"
                      (change)="onCheckboxChange()"
                      [required]="required()"
                      [disabled]="disabled()">
                <span class="inline-block">
                    Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor
                    incididunt ut labore
                </span>
            </checkbox>
        </input-wrap>
    </div>

    <div class="c-3-set">
        <p class="p2 m-b-2">Toggle</p>
        <input-wrap>
            <toggle name="toggle"
                    [(ngModel)]="toggleModel"
                    (change)="onToggleChange()"
                    [disabled]="disabled()"
                    [required]="required()">
                Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor
                incididunt ut labore
            </toggle>
        </input-wrap>
    </div>
    <div class="flex m-t">
        <button class="m-r button-inline"
                (click)="validate(form)">Validate</button>
        <button class="m-r button-inline"
                (click)="toggleDisabled()">Toggle disabled</button>
        <button class="m-r button-inline"
                (click)="toggleRequired()">Toggle required</button>
        <button class="m-r button-inline"
                (click)="toggleBusy()">Toggle busy</button>
    </div>
</ng-form>
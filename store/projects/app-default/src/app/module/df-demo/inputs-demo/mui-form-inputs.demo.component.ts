import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Dfd } from '@df/demo/core/dfd.decorator';
import { InputWrapBodyDirective } from '../../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../../ui/form/input-wrap.component';
import { FormInputsDemoAbstract } from './form-inputs.demo.abstract';

@Component({
    selector: 'mui-form-inputs-demo',
    templateUrl: 'mui-form-inputs.demo.html',
    standalone: true,
    imports: [CommonModule, FormsModule, InputWrapBodyDirective, InputWrapComponent]
})
@Dfd({
    componentName: 'Mui form fields',
    path: ['Forms', 'Inputs'],
    order: 106,
    background: 'dark'
})
export class MuiFormInputsDemoComponent extends FormInputsDemoAbstract {}

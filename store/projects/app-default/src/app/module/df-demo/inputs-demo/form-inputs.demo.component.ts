import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Dfd } from '@df/demo/core/dfd.decorator';
import { RadioGroupDirective } from '@df/ui/form/radio/radio.component.def';
import { CheckboxComponent } from '../../../ui/form/checkbox';
import { InputWrapBodyDirective } from '../../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../../ui/form/input-wrap.component';
import { RadioComponent } from '../../../ui/form/radio';
import { ToggleComponent } from '../../../ui/form/toggle/toggle.component';
import { FormInputsDemoAbstract } from './form-inputs.demo.abstract';

@Component({
    selector: 'form-inputs-demo',
    templateUrl: 'form-inputs.demo.html',
    standalone: true,
    imports: [
        CheckboxComponent,
        CommonModule,
        FormsModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        RadioComponent,
        RadioGroupDirective,
        ToggleComponent
    ]
})
@Dfd({
    componentName: 'Form fields',
    path: ['Forms', 'Inputs'],
    order: 105,
    background: 'dark'
})
export class FormInputsDemoComponent extends FormInputsDemoAbstract {}

import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { Status } from '@df/core/status';
import { Dfd } from '@df/demo/core/dfd.decorator';
import { TimeoutService } from '@df/ng/timeout.service';
import { atomicButton } from '@df/ui/atomic/atomic-options';
import { ActionComponent } from '../../../ui/api/action.component';
import { ResultComponent } from '../../../ui/api/result.component';

@Component({
    selector: 'buttons-demo',
    template: `
        @for (button of buttons; track button) {
            <div class="w-12 flex p-l-10">
                <p class="s1">{{ button.title }}</p>
                <p class="p2 m-l">.{{ button.key }}</p>
            </div>
            <div class="p-t p-l-10" [ngClass]="button.libraryDarkBackground ? 'dfs-bg-5' : ''">
                <div class="c-3-set">
                    <p class="p2 m-b-2">Basic</p>
                    <action
                        [status]="getStatus(button.key + 'basic')"
                        [ngClass]="button.key"
                        (click)="submit(getStatus(button.key + 'basic'))"
                    >
                        Button label
                    </action>
                    <result class="block" [status]="getStatus(button.key + 'basic')"></result>
                </div>
                <div class="c-3-set">
                    <p class="p2 m-b-2">Disabled</p>
                    <button [ngClass]="button.key" disabled>Button label</button>
                </div>
            </div>
        }
    `,
    standalone: true,
    imports: [ActionComponent, CommonModule, ResultComponent]
})
@Dfd({
    componentName: 'Buttons Style',
    path: ['Forms', 'Buttons'],
    order: 110
})
export class ButtonsDemoComponent {
    private timeoutService = inject(TimeoutService);

    buttons = atomicButton;

    private statuses: { [key: string]: Status } = {};

    getStatus(key: string) {
        if (!this.statuses[key]) {
            this.statuses[key] = new Status();
        }
        return this.statuses[key];
    }

    submit(status: Status) {
        status.busy = true;
        this.timeoutService.setTimeout(() => {
            status.error = 'Lorem ipsum dolor sit amet consectetur adipiscing elit';
        }, 2000);
    }
}

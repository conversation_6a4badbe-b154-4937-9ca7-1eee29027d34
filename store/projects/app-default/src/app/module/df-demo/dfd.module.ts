import { NgModule } from '@angular/core';
import { dfsOptionsProvider } from '@df/demo/library/dfd-library.component';
import { DfdLibraryModule } from '@df/demo/library/dfd-library.module';
import { surviveTreeShaking } from '@df/ng/survive-tree-shaking';
import { atomicColor, atomicTextStyle } from '@df/ui/atomic/atomic-options';
import { ICmsClientModule } from '@icms/client/icms-client.module';
import keyBy from 'lodash-es/keyBy';
import { IconsDemoComponent } from '../../common/icons/icons-style.demo.component';
import { LogoDemoComponent } from '../../common/logo/logo.demo1.component';
import { PaymentIconDemo1Component } from '../../common/payment-icon/payment-icon.demo1.component';
import { ButtonsDemoComponent } from './buttons-demo/buttons.demo.component';
import { FormInputsDemoComponent } from './inputs-demo/form-inputs.demo.component';
import { MuiFormInputsDemoComponent } from './inputs-demo/mui-form-inputs.demo.component';

const demos = [
    ButtonsDemoComponent,
    FormInputsDemoComponent,
    IconsDemoComponent,
    LogoDemoComponent,
    MuiFormInputsDemoComponent,
    PaymentIconDemo1Component
];

@NgModule({
    imports: [
        ICmsClientModule,
        DfdLibraryModule.forChild({
            path: '_library',
            title: 'Oliverbonas Component Library',
            sideTitle: 'Oliverbonas',
            color: keyBy(
                atomicColor
                    .filter(i => i.value && typeof i.value === 'string' && typeof i.key === 'string')
                    .map(i => ({
                        key: (i.key as string).replace('col-', '') + '',
                        group: i.group,
                        value: i.value as string,
                        title: i.title || (i.value as string)
                    })),
                'key'
            ),
            text: atomicTextStyle.map(i => ({ value: i.key as string, title: i.title as string, group: i.group }))
        })
    ],
    providers: [dfsOptionsProvider]
})
export class DfdModule {
    constructor() {
        surviveTreeShaking(...demos);
    }
}

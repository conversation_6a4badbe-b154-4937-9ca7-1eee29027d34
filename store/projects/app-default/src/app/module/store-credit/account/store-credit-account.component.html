<account-view-content [title]="'Account balance'"
                      [url]="'/account'">
    <div class="b-t-x b-t-l b-t-m p-t p-t-2-s p-l-4-s p-r-4-s">
        <div class="c-6 c-12-s bg-col-23 b-radius-20 p-a-6 p-b-7-l p-b-8-s m-b-0 center">
            <p [sizeClass]="'S:c1'">%</p>
            <p class="m-t-1 m-t-4-s fw-bold-s"
               [sizeClass]="'XLM:s1, S:c1'">{{'Account balance' | translate}}</p>
            <price class="hero block m-t-2"
                   [value]="balance"
                   [precision]="2" />
        </div>
        <div class="c-6 c-12-s m-r-0 m-b-0">
            <!-- Store credit history -->
            @if (history?.length) {
            <div class="m-t-6 m-t-4-s">
                @if (!client.isS) {
                <div class="flex fw-bold b-b p-b-2 w-12">
                    <p class="w-4 p1 p-l-3-l">{{'Date' | translate}}</p>
                    <p class="w-6 p1">{{'Description' | translate}}</p>
                    <p class="w-2 p1">{{'Value' | translate}}</p>
                </div>
                } @else {
                <div class="fw-bold">{{'Card history' | translate}}</div>
                }
                @for (item of history | slice: 0 : (displayAll ? history.length : 3); track $index) {
                <div class="p-t-2 p1 flex flex-column-s w-12">
                    <p class="w-4 w-12-s p-l-3-l">
                        @if (client.isS) {
                        {{'Date' | translate}}:&nbsp;
                        }
                        {{item.created_at | date: 'dd/MM/yy'}}
                    </p>
                    <p class="w-6 w-12-s">
                        @if (client.isS) {
                        {{'Description' | translate}}:&nbsp;
                        }
                        @if (!item.order_id) {
                        {{item.note}}
                        } @else {
                        <a [routerLink]="'/account/order/' + item.order_id">{{item.note}}</a>
                        }
                    </p>
                    <p class="w-2 w-12-s">
                        @if (client.isS) {
                        {{'Value' | translate}}:&nbsp;
                        }
                        <price [value]="item.amount"
                               [precision]="2" />
                    </p>
                </div>
                }
                @if (!displayAll && history?.length > 3) {
                <button class="button-inline m-t-3 m-t-3-l fs-action-button"
                        type="button"
                        (click)="showAll()">{{'View all' | translate}}</button>
                }
            </div>
            }
        </div>
    </div>
    <div class="b-t p-t m-t-6 m-t-4-s m-l--4-s m-r--4-s">
        <div class="c-6 c-12-s m-r-0 m-b-0 m-t-5-s p-l-3-s p-r-3-s">
            <p class="p-l-8 p-l-3-s m-b-3 fw-bold-s"
               [sizeClass]="'XLM:h3'">{{'Use your account credit in store' | translate}}</p>
            <p class="p1">{{'View the barcode and present it at the till' | translate}}</p>
            <div class="bg-col-23 b-radius-20 p-a-6 p-b-10-m p-b-8-l p-b-10-x m-t m-l-5-s m-r-5-s">
                <div class="ratio-3-1 ratio-2-1-s m-b-9-m">
                    @if (!showBarcode) {
                    <div class="flex flex-justify-center flex-middle fill">
                        <div class="w-9 center pos-relative p-b-4-x p-b-6-x p-b-10-s">
                            <div class="store-credit-logo w-12 m-t--4-x m-t--4-l m-t-4-m"></div>
                            <div class="left-0 right-0 bottom-0 bottom-1-s pos-absolute">
                                <button class="p-l-6 p-r-6 button-2 button-size-2 b-radius-max"
                                        type="button"
                                        (click)="displayBarcode()">
                                    <span>{{'View barcode' | translate}}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    } @else {
                    <div class="fill flex flex-justify-center flex-middle">
                        <div class="w-12 center m-t-10-m">
                            <img [src]="barcode">
                            <p class="fw-bold ls-1 m-t-1">{{code}}</p>
                            <p class="fw-bold ls-1 m-t-2">
                                {{'Balance' | translate}}:&nbsp;
                                <price [value]="balance"
                                       [precision]="2" />
                            </p>
                        </div>
                    </div>
                    }
                </div>
            </div>
            <div class="m-t center">
                <action class="button p-r-0 p-l-0 w-7 w-8-m w-12-s"
                        [status]="sendStatus"
                        (click)="sendCreditsToEmail()">
                    {{'Email me my account credit' | translate}}
                </action>
                <result class="block m-t-1"
                        [status]="sendStatus" />
            </div>
        </div>
    </div>
</account-view-content>
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, ViewChild, inject } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { App } from '@df/core/app';
import { Status } from '@df/core/status';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { PriceComponent } from '@df/ui/price/price.component';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ICmsComponent, ICmsContent, NICmsContentComponent, NICmsContentField } from '@icms/core/content';
import { AccountViewContentComponent } from '../../../account/view/content/account-view-content.component';
import { AbstractCmsBlockComponent } from '../../../cms/block/abstract-cms-block.component';
import { ActionComponent } from '../../../ui/api/action.component';
import { ResultComponent } from '../../../ui/api/result.component';
import { StoreCredit } from '../store-credit.namespace';
import { StoreCreditService } from '../store-credit.service';

export interface IStoreCreditAccountContentBlock extends NICmsContentComponent.IData {
    copy?: string;
    use_copy?: string;
}

@Component({
    selector: 'store-credit-account',
    templateUrl: './store-credit-account.component.html',
    styleUrl: './store-credit-account.component.scss',
    standalone: true,
    imports: [
        AccountViewContentComponent,
        ActionComponent,
        CommonModule,
        FormsModule,
        PriceComponent,
        ResultComponent,
        RouterModule,
        SizeClassDirective,
        TranslatePipe
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
@ICmsComponent<StoreCreditAccountComponent>({
    id: 'store-credit-account-content-block',
    block: {
        url: 'store-credit-account-content-block'
    },
    onDemandOnly: true,
    name: 'Store credit account content block',
    fields: [
        {
            type: NICmsContentField.EFieldType.textarea,
            key: 'copy',
            title: 'Copy'
        },
        {
            key: 'use_copy',
            type: NICmsContentField.EFieldType.textarea,
            title: 'Use credits copy'
        }
    ]
})
export class StoreCreditAccountComponent extends AbstractCmsBlockComponent implements NICmsContentComponent.IComponent, OnInit {
    @ICmsContent({
        type: 'blockContent'
    })
    override content!: IStoreCreditAccountContentBlock;

    addStatus = new Status();
    sendStatus = new Status();
    displayAll = false;
    showBarcode = false;

    @ViewChild('form', { static: false })
    form!: NgForm;

    protected storeCredit = inject(StoreCreditService);
    protected app = inject(App);

    override ngOnInit() {
        super.ngOnInit();
        this.subscriptions.add(this.app.customer.model.obs.data.subscribe(() => this.detectChanges()));
    }

    get balance(): number {
        return this.storeCredit.balance;
    }

    get history(): StoreCredit.IHistoryItem[] {
        return this.storeCredit.history;
    }

    get barcode(): string | undefined {
        return this.app.customer.model.data.store_credit_barcode;
    }

    get code(): string | undefined {
        return this.app.customer.model.data.store_credit_code;
    }

    sendCreditsToEmail(): void {
        this.storeCredit.sendCreditsToEmail(this.sendStatus);
    }

    showAll(): void {
        this.displayAll = true;
        this.detectChanges();
    }

    displayBarcode(): void {
        this.showBarcode = true;
        this.detectChanges();
    }
}

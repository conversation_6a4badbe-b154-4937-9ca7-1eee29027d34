<button class="b-radius-basket w-12 p-a-2 left pos-relative"
        [ngClass]="welcomeOffer ? 'bg-col-23' : 'bg-col-w b-a b-col-4'"
        [class.disabled]="status.busy"
        type="button"
        (click)="toggle()">
    <div>
        <i class="icon-cards m-l-1"
           aria-hidden="true"></i>
        <span class="p-l-2 p2">
            {{'Apply' | translate}}
            @if (welcomeOffer) {
            &nbsp;<price [value]="balance"
                   precision="2" />
            }
            &nbsp;{{welcomeOffer ? 'credit' : 'Store Credit'}}
        </span>
    </div>
    @if (status.busy) {
    <span class="flex pos-absolute top-0 right-4 bottom-0 flex-middle flex-justify-center">
        <i class="icon-loading"
           aria-hidden="true"></i>
    </span>
    }
</button>
<result class="block m-t-1"
        [status]="status" />
<form #form="ngForm"
      method="POST"
      class="block p-a-3 p-t-0">

    <input-wrap>
        <input #codeInput
               class="input"
               type="text"
               inputmode="numeric"
               name="code"
               required
               [muiInput]="'Enter gift card code' | translate"
               [(ngModel)]="code"
               (keydown.enter)="submit()">
    </input-wrap>

    <!-- open -->
    <div class="flex">
        <!-- coupon code -->
        <div class="w-8">

            <input-wrap>
                <input #pinInput
                       class="input"
                       type="text"
                       inputmode="numeric"
                       name="pin"
                       required
                       [muiInput]="'PIN' | translate"
                       [(ngModel)]="pin"
                       (keydown.enter)="submit()">
            </input-wrap>
        </div>

        <div class="w-4 p-l-3 pos-relative mui-input-padding-top">
            <!-- submit -->
            <action class="w-12 button"
                    (click)="submit()"
                    [status]="status">
                {{'Apply' | translate}}
            </action>
        </div>
    </div>
    <result class="m-t-1 fs-3 block"
            [status]="status" />
</form>
<h3 class="flex flex-middle m-b-2">
    <span class="p1 flex flex-middle flex-justify-center m-r-2 bg-col-23 b-radius-max giftcard-bullet">2</span>
    <span class="p3">{{'Select amount' | translate}}</span>
</h3>

<!-- amounts -->
@if (amounts || maxAmount) {
<div class="w-12 m-t-2 m-t-3-m m-t-3-s flex flex-wrap">

    <!-- amount -->
    @for (value of amounts; track $index) {
    <button class="cursor-pointer flex flex-middle flex-justify-center b-a b-col-5 m-b-2-s giftcard-amount"
            [ngClass]="{'bg-col-34': request?.am_giftcard_amount == value}"
            type="button"
            (click)="selectAmount(value)">
        <price class="p1"
               [value]="value"
               [precision]="0" />
    </button>
    }

    <div class="cursor-pointer flex flex-middle flex-justify-center b-a b-col-5 m-b-2-s giftcard-amount"
         [ngClass]="{'bg-col-34': request.am_giftcard_amount_custom && !request.am_giftcard_amount && !show}"
         (click)="showCustomAmountField()">
        @if (!show) {
        @if (!request.am_giftcard_amount_custom) {
        <p class="p1">{{'Other amount' | translate}}</p>
        } @else {
        <price class="p1"
               [value]="request.am_giftcard_amount_custom"
               [precision]="0" />
        }
        } @else {
        <!-- input custom amount -->
        <div>
            {{app.multisite.current.currency_symbol}}
            <input class="giftcard-custom-amount no-spinner"
                   name="am_giftcard_amount_custom"
                   required="{{!request?.am_giftcard_amount}}"
                   type="number"
                   inputmode="numeric"
                   [attr.max]="maxAmount"
                   [attr.min]="minAmount"
                   [attr.placeholder]="maxAmount ? 'Enter amount (up to ' + (maxAmount | price : 0 : app.multisite.current.currency_symbol) + ')' : 'Enter amount'"
                   [(ngModel)]="request.am_giftcard_amount_custom"
                   (change)="onCustomAmountChange()"
                   (blur)="onCustomAmountBlur()"
                   (keypress)="onKeypress($event)"
                   (keyup.enter)="onCustomAmountBlur()"
                   #customAmount />
        </div>
        }
    </div>
</div>
}

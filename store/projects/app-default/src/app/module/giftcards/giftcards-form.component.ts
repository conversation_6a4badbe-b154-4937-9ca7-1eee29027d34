import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Input, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ICallableObject } from '@df/api/request/request.service';
import { Giftcards } from '@df/module-giftcards/giftcards';
import { NOTICES } from '@df/ui/api/notice/notice.const';
import { AbstractFormComponent } from '@df/ui/form/abstract-form.component';
import { ToggleService } from '@df/ui/toggle/toggle.service';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ActionComponent } from '../../ui/api/action.component';
import { ResultComponent } from '../../ui/api/result.component';
import { InputWrapBodyDirective } from '../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../ui/form/input-wrap.component';
import { MuiInputDirective } from '../../ui/form/mui-input.directive';
import { BasketDiscountButtonsComponent } from './../../basket/basket-discount-buttons.component';

NOTICES['INVALID_GIFTCARD'] = `Sorry, we don't recognise this code`;
NOTICES['INCORRECT_PIN'] = 'Please enter correct pin number';

@Component({
    selector: 'giftcards-form',
    templateUrl: './giftcards-form.html',
    standalone: true,
    imports: [
        ActionComponent,
        CommonModule,
        FormsModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        MuiInputDirective,
        ResultComponent,
        TranslatePipe
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class GiftcardsFormComponent extends AbstractFormComponent<{
    code: string;
    checkout?: boolean;
    pin: string;
}> {
    protected submitPath = Giftcards.Endpoints.Add;

    @Input()
    isCheckout?: boolean;

    @ViewChild('pinInput')
    pinInputElement!: ElementRef<HTMLInputElement>;

    @ViewChild('codeInput')
    codeInputElement!: ElementRef<HTMLInputElement>;

    constructor(
        ref: ChangeDetectorRef,
        protected toggleService: ToggleService,
        protected basketDiscountButtonsComponent: BasketDiscountButtonsComponent
    ) {
        super(ref);
    }

    protected override getSubmitAction() {
        const superSubmitAction = super.getSubmitAction() as ICallableObject;
        if (this.isCheckout) {
            superSubmitAction.path = Giftcards.Endpoints.CheckoutAdd;
        }
        return superSubmitAction;
    }

    protected override onSubmitSuccess(response?: any): void {
        this.toggleService.close([this.basketDiscountButtonsComponent.toggleGroup, this.basketDiscountButtonsComponent.giftcardsToggleId]);
        super.onSubmitSuccess(response);
    }

    get pin(): string | undefined {
        return this.data.pin;
    }

    set pin(value: string) {
        this.data.pin = value.replace(/\D/g, '');
        if (this.pinInputElement?.nativeElement) {
            this.pinInputElement.nativeElement.value = this.data.pin;
        }
        this.detectChanges();
    }

    get code(): string | undefined {
        return this.data.code;
    }

    set code(value: string) {
        this.data.code = value.replace(/\D/g, '');
        if (this.codeInputElement?.nativeElement) {
            this.codeInputElement.nativeElement.value = this.data.code;
        }
        this.detectChanges();
    }
}

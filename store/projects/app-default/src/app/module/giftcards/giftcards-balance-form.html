<div class="flex flex-justify-between flex-column-s p-t p-b p-b-9-s p-l-5 p-r-5 m-t-9 m-t-5-s m-l--4-s m-r--4-s"
     [ngClass]="component.balance_checker_bg || 'bg-col-5'">
    <div class="w-4 w-12-s p-r-4-x p-r-4-l p-t-2 p-t-5-s center-s flex flex-column flex-justify-between">
        <div>
            <!-- balance checker title -->
            <h2 class="h2"
                *iCms="'balance_checker_title' of component"></h2>

            <!-- balance checker copy -->
            <p class="p1 m-t m-t-5-s"
               *iCms="'balance_checker_copy' of component"></p>
        </div>
        @if (!app.client.isS && hasBalance) {
        <div>
            {{'Have another card to add?' | translate}}&nbsp;
            <button class="button-inline s1"
                    type="button"
                    (click)="reset(true)">{{'Click here' | translate}}</button>
        </div>
        }
    </div>

    <!-- balance checker form -->
    <div class="w-6 w-12-s p-l-6-x p-l-6-l m-t-9-s">
        <div class="bg-col-w giftcard-balance">
            <div class="p-l-6 p-r-6 p-t-2 p-b-5 p-b-8-s pos-relative">
                <!-- Gift card form -->

                @if (!hasBalance) {
                <form #form="ngForm"
                      method="POST">
                    <input-wrap class="block">
                        <input class="input"
                               name="code"
                               required
                               [muiInput]="'Gift card number' | translate"
                               [(ngModel)]="code"
                               #codeInput />
                    </input-wrap>

                    <div class="m-t--1 flex-s">
                        <input-wrap class="block c-12-set m-b-0">
                            <input class="input"
                                   name="pin"
                                   required
                                   [muiInput]="'PIN'"
                                   [(ngModel)]="pin"
                                   #pinInput />
                        </input-wrap>
                    </div>
                    <div class="flex flex-justify-center m-t-3 p-l-3 p-r-3">
                        <action class="button w-6"
                                [status]="status"
                                (click)="submit()">{{'Check balance' | translate}}</action>
                    </div>

                    <result class="pos-absolute bottom-2 left-0 right-0 center m-t-1"
                            [status]="status" />
                </form>
                }
                <!-- balance check success prompt-->
                <div [class.show]="hasBalance"
                     class="pos-absolute giftcard-balance fill bg-col-23 z-2 flex flex-justify-center flex-middle flex-column ng-hide-animate ng-hide">
                    <div class="p-b-2 col-11">{{'Total remaining balance' | translate}}</div>
                    <price class="hero col-11 block"
                           [value]="displayedBalance"
                           [precision]="2" />
                    <div class="c1 col-12 center p-t-2 p-b-2 p-t-3-s p-b-4-s">
                        <div class="p-b-1-s">
                            {{'Card Number' | translate}}: {{displayedCode}}
                        </div>
                    </div>
                    <div class="fs-4">
                        <action class="button b-radius-max block giftcard-transfer-button"
                                type="button"
                                [success]="'Successfully added to your account!'"
                                [status]="giftcardTransferStatus"
                                (click)="reset(true)">{{'Check another card' | translate}}</action>
                    </div>
                    <result class="pos-absolute bottom-2 left-0 right-0 center m-t-1"
                            [status]="giftcardTransferStatus" />
                </div>
            </div>
        </div>
        @if (app.client.isS && hasBalance) {
        <div class="w-12 center m-t-8">
            <span class="p1">{{'Have another card to add?' | translate}}</span>&nbsp;
            <button class="button-inline s1"
                    type="button"
                    (click)="reset(true)">{{'Click here' | translate}}</button>
        </div>
        }
    </div>
</div>
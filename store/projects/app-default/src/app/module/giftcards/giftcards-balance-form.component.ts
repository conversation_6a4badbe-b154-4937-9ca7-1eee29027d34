import { CommonModule } from '@angular/common';
import { Component, ElementRef, inject, Input, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Status } from '@df/core/status';
import { Giftcards } from '@df/module-giftcards/giftcards';
import { GiftcardsBalanceFormComponentDef } from '@df/module-giftcards/giftcards-balance-form.component.def';
import { NeedLoginService } from '@df/session/need-login.service';
import { PriceComponent } from '@df/ui/price/price.component';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ICmsClientModule } from '@icms/client/icms-client.module';
import { IPageComponentGiftcardsData } from '../../catalog/ui/page/component/giftcards/giftcards.interface';
import { ActionComponent } from '../../ui/api/action.component';
import { ResultComponent } from '../../ui/api/result.component';
import { InputWrapBodyDirective } from '../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../ui/form/input-wrap.component';
import { MuiInputDirective } from '../../ui/form/mui-input.directive';
import { StoreCreditService } from '../store-credit/store-credit.service';
import { StoreCredit } from './../../module/store-credit/store-credit.namespace';

@Component({
    selector: 'giftcards-balance-form',
    templateUrl: './giftcards-balance-form.html',
    standalone: true,
    imports: [
        ActionComponent,
        CommonModule,
        FormsModule,
        ICmsClientModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        MuiInputDirective,
        PriceComponent,
        ResultComponent,
        TranslatePipe
    ]
})
export class GiftcardsBalanceFormComponent extends GiftcardsBalanceFormComponentDef {
    displayedBalance?: number;
    displayedCode?: string;
    giftcardTransfered = false;
    giftcardTransferStatus = new Status();

    @Input({ required: true })
    component!: IPageComponentGiftcardsData;

    @ViewChild('pinInput')
    pinInputElement!: ElementRef<HTMLInputElement>;

    @ViewChild('codeInput')
    codeInputElement!: ElementRef<HTMLInputElement>;

    protected storeCredit = inject(StoreCreditService);
    protected needLoginService = inject(NeedLoginService);

    get pin(): string | undefined {
        return this.data.pin;
    }

    set pin(value: string) {
        this.data.pin = value.replace(/\D/g, '');
        if (this.pinInputElement?.nativeElement) {
            this.pinInputElement.nativeElement.value = this.data.pin;
        }
        this.detectChanges();
    }

    get code(): string | undefined {
        return this.data.code;
    }

    set code(value: string) {
        this.data.code = value.replace(/\D/g, '');

        if (this.codeInputElement?.nativeElement) {
            this.codeInputElement.nativeElement.value = this.data.code;
        }

        this.detectChanges();
    }

    protected override onSubmitSuccess(response?: Giftcards.IBalanceCheckResponse) {
        super.onSubmitSuccess(response);
        this.displayedBalance = response && response.balance;
        this.displayedCode = this.code;
    }

    transferGiftCardToStoreCredit(): void {
        if (this.app.customer.model.isLoggedIn) {
            this.storeCredit.mergeCard(this.form, this.giftcardTransferStatus, <StoreCredit.IMergeCardData>this.data, () => {
                this.giftcardTransfered = true;
                // balance was transfered to store credit so it is 0 now;
                this.displayedBalance = 0;
                this.detectChanges();
            });
        } else {
            this.needLoginService.needLogin(() => {
                if (this.app.customer.model.isLoggedIn) {
                    this.transferGiftCardToStoreCredit();
                }
            });
        }
    }

    override reset(hard?: boolean) {
        if (hard) {
            this.giftcardTransferStatus.reset();

            super.reset();
        }
    }
}

@if (listing.models?.length) {
<div class="wrap m-t-6">
    <div class="m-t-7 m-b-3 m-b-7-s">
        @if (client.sizeId > 1) {
        <div class="c-1"></div>
        }
        <h1 class="h1 inline">{{'Gift Cards & E-Gift Cards' | translate}}</h1>
    </div>
    @if (client.sizeId > 1) {
    <div class="c-6"></div>
    }
    <div class="c-5 c-12-m c-12-s p-l-5-x p-l-5-l">
        @if (client.sizeId > 1) {
        <div [grid]="2"
             [gridColumnGap]="2">
            @for (model of listing.models; track model.id) {
            <div (click)="selectGiftcard(model.id)">
                <div class="w-12 b-a b-col-4 p-l-2 p-r-2 p1 col-12 flex flex-middle flex-justify-center cursor-pointer giftcard-type"
                     [ngClass]="{'bg-col-23': model.id === currentGiftcard}">
                    {{model.data.name}}
                </div>
            </div>
            }
        </div>
        } @else {
        <sticky-free [stickyId]="'sticky-giftcards-category-view'"
                     [stickyOffset]="'page-header-wrapper'"
                     [stickyOff]="client.isCms">
            <div class="flex p-t-2 p-b-2 b-b b-col-5 bg-col-w z-1">
                @for (model of listing.models; track model.id) {
                <div class="m-r"
                     (click)="selectGiftcard(model.id)">
                    <div class="p1 cursor-pointer"
                         [ngClass]="model.id === currentGiftcard ? 'col-11' : 'col-13'">
                        {{model.data.name}}
                    </div>
                </div>
                }
            </div>
        </sticky-free>
        }
    </div>
</div>
@if (currentGiftcard) {
<giftcards-product [modelId]="currentGiftcard"
                   (selectNext)="handleSelectNext()" />
}
}
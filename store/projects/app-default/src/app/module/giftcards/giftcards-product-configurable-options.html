<div class="w-12">
    @for (attribute of attributes; track attribute.code) {
    @if (attribute.code === 'card_design' && attribute.options?.length >= 1) {
    <h3 class="flex flex-middle m-b-2-x m-b-2-l m-b">
        <span class="p1 flex flex-middle flex-justify-center m-r-2 bg-col-23 b-radius-max giftcard-bullet">1</span>
        <span class="p3">{{'Select a design' | translate}}</span>
    </h3>

    <div class="w-12 m-b-3 overflow-hidden giftcard-image"
         *ifSize="'SM'">
        <div class="ratio-3-2">
            @if (request.am_giftcard_image && product.data.image) {
            <img [dfImage]="product.data.image"
                 [ratio]="'ratio-3-2'"
                 fill
                 alt="Gift card design" />
            }

            @if (!request.am_giftcard_image) {
            <div class="fill bg-col-5 overflow-hidden giftcard-image">
                <div class="vc-outer">
                    <div class="vc-inner-middle center">
                        <p class="p3">{{'Select a design to start' | translate}}</p>
                    </div>
                </div>
            </div>
            }
        </div>
    </div>

    <div class="m-l--4-s m-r--4-s">
        <div class="p-l-4-s overflow-y-auto-s"
             [grid]="app.client.isS ? undefined : 4"
             [gridColumnGap]="app.client.isS ? undefined : 2">
            @for (option of attribute.options; track option.value) {
            @if (option.product) {
            <div class="overflow-hidden m-r-1-s giftcard-option-image">
                <label class="block pos-relative cursor-pointer"
                       (click)="pick(attribute, option)">
                    <div class="ratio-1-1">
                        <img [dfImage]="option.product.data.image"
                             [ratio]="'ratio-1-1'"
                             alt="Gift card design"
                             fill>
                    </div>
                    <div class="pos-absolute flex flex-middle flex-justify-center b-a b-radius-max bg-col-6 giftcard-option"
                         [ngClass]="isPicked(attribute, option) ? 'b-col-13' : 'b-col-4'">
                        @if (isPicked(attribute, option)) {
                        <div class="bg-col-11 b-radius-max pos-absolute giftcard-option-checked"></div>
                        }
                    </div>
                </label>
            </div>
            }
            }
        </div>
    </div>
    }
    }
</div>
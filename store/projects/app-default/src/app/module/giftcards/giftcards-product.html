@if (model && model.data) {
<div class="wrap p-b"
     [elementHeight]="'giftcards-product'">
    <form name="e-gift"
          method="POST"
          novalidate
          #form="ngForm">
        @if (client.sizeId > 1) {
        <div class="c-1"></div>

        <!-- gift card main image XLM -->
        <div class="c-5 giftcard-image-block">
            <sticky-free [stickyId]="'sticky-giftcards-product'"
                         [stickyOffset]="'page-header-wrapper'"
                         [stickyOff]="client.isCms"
                         [order]="2">
                <ng-container *ngTemplateOutlet="giftCardImage; context: {$implicit: model, request: request}" />
            </sticky-free>
        </div>
        }

        <div class="c-5 c-12-m c-12-s p-l-5-x p-l-5-l m-t-3-x m-t-3-l">
            @if (!model.inStock) {
            <h1 class="m-t m-t-8-s h1">
                {{'Gift cards out of stock' | translate}}<br>
                {{'E-gift cards are available' | translate}}
            </h1>

            <div class="m-t-3 m-t-6-s p1">
                {{'Our gift cards are temporarily out of stock, we are expecting more very soon. In the meantime, you
                can still purchase E-Gift Cards online.' | translate}}
            </div>

            <button class="m-t-3 m-t-6-s button-2"
                    (click)="handleSelectNext()">
                <span>{{'Shop E-Gift Cards' | translate}}</span>
            </button>
            } @else {
            <!-- options -->
            <giftcards-product-configurable-options class="m-t"
                                                    [product]="model"
                                                    [request]="request"
                                                    (init)="handleInitChange($event.data)"
                                                    (selectionChange)="handleSelectionChange($event.data)"
                                                    (modelChange)="handleDataChange()" />

            <!-- amount selector-->
            <div #secondStep>
                <giftcards-amount-selector class="block m-t-9"
                                           [product]="model"
                                           [noDefault]="true"
                                           [class.giftcard-disabled]="!firstStepChecked"
                                           [request]="request"
                                           (update)="onRequestChange(); goToThirdStep()" />
            </div>

            @if (isVirtual) {
            <!-- send by email to -->
            <div class="m-t-9"
                 [class.giftcard-disabled]="!secondStepChecked"
                 #thirdStep>
                <h3 class="flex flex-middle m-b--1">
                    <span
                          class="p1 flex flex-middle flex-justify-center m-r-2 bg-col-23 b-radius-max giftcard-bullet">3</span>
                    <span class="p3">{{'Add a message' | translate}}</span>
                </h3>

                @if (secondStepChecked) {
                <!-- recipient name -->
                <input-wrap class="block">
                    <input class="input"
                           type="text"
                           name="recipient_name"
                           required
                           [muiInput]="'Recipient\'s name' | translate"
                           [(ngModel)]="request.am_giftcard_recipient_name"
                           (blur)="goToFourthStep()" />
                </input-wrap>

                <!-- message -->
                <input-wrap class="block">
                    <!-- message textarea -->
                    <textarea class="input textarea"
                              name="recipient_message"
                              maxlength="200"
                              [muiInput]="'Message' | translate"
                              [(ngModel)]="request.am_giftcard_message"
                              (blur)="goToFourthStep()"></textarea>
                </input-wrap>

                <!-- sender name -->
                <input-wrap class="block">
                    <input class="input"
                           type="text"
                           name="sender_name"
                           required
                           [muiInput]="'Your name' | translate"
                           [(ngModel)]="request.am_giftcard_sender_name"
                           (blur)="goToFourthStep()" />
                </input-wrap>
                }
            </div>

            <div class="m-t-9"
                 [class.giftcard-disabled]="!thirdStepChecked"
                 #fourthStep>
                <h3 class="flex flex-middle m-b-2">
                    <span
                          class="p1 flex flex-middle flex-justify-center m-r-2 bg-col-23 b-radius-max giftcard-bullet">4</span>
                    <span class="p3">{{'Select recipient' | translate}}</span>
                </h3>

                @if (thirdStepChecked) {
                <span class="p1">{{'Who would you like to send the E-gift card to?' | translate}}</span>
                <div [grid]="app.client.isS ? 1 : 2"
                     [gridColumnGap]="app.client.isS ? 0 : 2">
                    <button class="b-a b-col-5 p-l-3 p-r-3 flex flex-middle giftcard-send-option"
                            [ngClass]="{'bg-col-34': formType === 'recipient'}"
                            type="button"
                            (click)="formType = 'recipient'">
                        <div class="b-a b-col-13 bg-col-w b-radius-max m-r-2 pos-relative giftcard-send-option-bullet">
                            @if (formType === 'recipient') {
                            <div class="bg-col-11 b-radius-max pos-absolute giftcard-send-option-bullet-checked"></div>
                            }
                        </div>

                        <span class="p1">{{'Recipient' | translate}}</span>
                    </button>

                    <button class="b-a b-col-5 p-l-3 p-r-3 flex flex-middle giftcard-send-option"
                            [ngClass]="{'bg-col-34': formType === 'myself'}"
                            type="button"
                            (click)="formType = 'myself'">
                        <div class="b-a b-col-13 bg-col-w b-radius-max m-r-2 pos-relative giftcard-send-option-bullet">
                            @if (formType === 'myself') {
                            <div class="bg-col-11 b-radius-max pos-absolute giftcard-send-option-bullet-checked"></div>
                            }
                        </div>
                        <span class="p1">{{'Myself' | translate}}</span>
                    </button>
                </div>

                @switch (formType) {
                @case ('recipient') {
                <p class="m-t-6 m-b-3 p3">{{'When would you like to send it?' | translate}}</p>

                <!-- buttons -->
                <div class="flex m-t-2 m-t-4-m m-t-4-s">
                    <button class="p-l-3 p-r-3 p-t-1 p-b-1 m-r-1 b-a b-col-5 b-radius-max"
                            [ngClass]="{'bg-col-34': !isCalendarOpened}"
                            type="button"
                            (click)="toggleCalendar(!isCalendarOpened); openCalendarModal();">
                        <span class="p1">
                            @if (request.am_giftcard_date_delivery) {
                            {{request.am_giftcard_date_delivery | date: 'E'}},
                            {{request.am_giftcard_date_delivery | date: 'd' | ordinal}}
                            {{request.am_giftcard_date_delivery | date: 'MMM'}}
                            } @else {
                            <span class="fw-bold">{{'Today' | translate}}</span>
                            @if (today) {
                            {{today | date: 'E'}},
                            {{today | date: 'd' | ordinal}}
                            {{today | date: 'MMM'}}
                            }
                            }
                        </span>
                    </button>

                    <button class="p-l-3 p-r-3 p-t-1 p-b-1 b-a b-col-5 b-radius-max"
                            [ngClass]="{'bg-col-34': isCalendarOpened}"
                            type="button"
                            (click)="toggleCalendar(); openCalendarModal();">
                        @if (!request.am_giftcard_date_delivery) {
                        <span class="p1">{{'Select a date' | translate}}</span>
                        } @else {
                        <span class="p1">{{'Edit date' | translate}}</span>
                        }
                    </button>
                </div>

                <!-- date delivery -->
                @if (!client.isS && isCalendarOpened) {
                <calendar class="w-9 w-12-s m-t-3 m-b-1 p-b-2 b-a b-col-5 pos-relative giftcard-calendar"
                          name="delivery_date"
                          [options]="calendarOptions"
                          [(ngModel)]="deliveryDate" />
                }

                <p class="p3 m-t-5 m-b--2">{{'Enter the recipient\'s email address' | translate}}</p>

                <!-- recipient email -->
                <input-wrap class="block m-b--1"
                            [messages]="{
                                validateEqual: ('Email address is not the same' | translate),
                                email: ('Valid email is required' | translate),
                                required: ('Valid email is required' | translate),
                                LOQATE_INVALID_EMAIL: ('Valid email is required' | translate)
                            }"
                            [canShowValidate]="recipientEmailRef?.value?.length > 1 && elementBlur['am_giftcard_recipient_email']">
                    <input class="input"
                           type="email"
                           name="recipient_email"
                           required
                           validatePatternAsync="email"
                           [loqateEmail]="true"
                           [muiInput]="'Email address' | translate"
                           [muiInputValidateOnSubmit]="false"
                           [(ngModel)]="request.am_giftcard_recipient_email"
                           [ngModelOptions]="{updateOn: 'blur'}"
                           (keyup)="handleKeyUp($event, 'am_giftcard_recipient_email', request.am_giftcard_recipient_email)"
                           (blur)="handleKeyUp(undefined, 'am_giftcard_recipient_email', request.am_giftcard_recipient_email)"
                           #recipientEmailRef>
                </input-wrap>

                <!-- confirm recipient email -->
                <input-wrap [messages]="{
                                validateEqual: ('Email address is not the same' | translate),
                                email: ('Valid email is required' | translate),
                                required: ('Valid email is required' | translate)
                            }"
                            [canShowValidate]="recipientEmailConfirmationRef?.value?.length > 1 && elementBlur['recipient_email_confirmation']">
                    <input class="input"
                           type="email"
                           name="recipient_email_confirmation"
                           required
                           matchCheck="recipient_email"
                           validatePatternAsync="email"
                           [muiInput]="'Confirm email address' | translate"
                           [muiInputValidateOnSubmit]="false"
                           [(ngModel)]="recipient_email_confirmation"
                           [ngModelOptions]="{updateOn: 'blur'}"
                           (keyup)="handleKeyUp($event, 'recipient_email_confirmation', recipient_email_confirmation)"
                           (blur)="handleKeyUp(undefined, 'recipient_email_confirmation', recipient_email_confirmation)"
                           #recipientEmailConfirmationRef>
                </input-wrap>
                }
                @case ('myself') {
                <p class="p3 m-t-5 m-b--2">{{'My email address' | translate}}</p>

                <!-- my email -->
                <input-wrap class="block m-b--1"
                            [messages]="{
                                validateEqual: ('Email address is not the same' | translate),
                                email: ('Valid email is required' | translate),
                                required: ('Valid email is required' | translate),
                                LOQATE_INVALID_EMAIL: ('Valid email is required' | translate)
                            }"
                            [canShowValidate]="recipientEmailRef?.value?.length > 1 && elementBlur['am_giftcard_recipient_email']">
                    <input class="input"
                           type="email"
                           name="recipient_email"
                           required
                           validatePatternAsync="email"
                           [loqateEmail]="true"
                           [muiInput]="'Email address' | translate"
                           [muiInputValidateOnSubmit]="false"
                           [(ngModel)]="request.am_giftcard_recipient_email"
                           [ngModelOptions]="{updateOn: 'blur'}"
                           (keyup)="handleKeyUp($event, 'am_giftcard_recipient_email', request.am_giftcard_recipient_email)"
                           (blur)="handleKeyUp(undefined, 'am_giftcard_recipient_email', request.am_giftcard_recipient_email)"
                           #recipientEmailRef>
                </input-wrap>

                <!-- confirm recipient email -->
                <input-wrap [messages]="{
                                validateEqual: ('Email address is not the same' | translate),
                                email: ('Valid email is required' | translate),
                                required: ('Valid email is required' | translate)
                            }"
                            [canShowValidate]="recipientEmailConfirmationRef?.value?.length > 1 && elementBlur['recipient_email_confirmation']">
                    <input class="input"
                           type="email"
                           name="email_confirmation"
                           required
                           matchCheck="recipient_email"
                           validatePatternAsync="email"
                           [muiInput]="'Confirm email address' | translate"
                           [muiInputValidateOnSubmit]="false"
                           [(ngModel)]="recipient_email_confirmation"
                           [ngModelOptions]="{updateOn: 'blur'}"
                           (keyup)="handleKeyUp($event, 'recipient_email_confirmation', recipient_email_confirmation)"
                           (blur)="handleKeyUp(undefined, 'recipient_email_confirmation', recipient_email_confirmation)"
                           #recipientEmailConfirmationRef>
                </input-wrap>
                }
                }
                }
            </div>
            }

            <!-- add to basket -->
            <giftcards-add-to-basket class="block m-t-10 form-size-1"
                                     [product]="model"
                                     [form]="form"
                                     [request]="request"
                                     [disabled]="!isValid" />
            }
        </div>
    </form>
    <div id="giftcard-main-img"></div>
</div>
}

<!-- giftCard image template -->
<ng-template #giftCardImage
             let-model
             let-request="request">
    <div class="w-12 overflow-hidden giftcard-image">
        <div class="ratio-3-2">

            <!-- image -->
            @if (request.am_giftcard_image && model.data.image) {
            <img [dfImage]="model.data.image"
                 [ratio]="'3-2'"
                 fill />
            }

            <!-- placeholder -->
            @if (!request.am_giftcard_image) {
            <div class="fill bg-col-5 overflow-hidden giftcard-image">
                <div class="vc-outer">
                    <div class="vc-inner-middle center">
                        <p class="p3">{{'Select a design to start' | translate}}</p>
                    </div>
                </div>
            </div>
            }
        </div>
    </div>
</ng-template>

<!-- calendar modal template -->
<ng-template #calendarModal>
    <calendar class="w-12-s bg-col-w p-a pos-relative giftcard-calendar"
              name="delivery_date"
              [options]="calendarOptions"
              [(ngModel)]="request.am_giftcard_date_delivery"
              (changeDate)="closeCalendarModal()" />
</ng-template>
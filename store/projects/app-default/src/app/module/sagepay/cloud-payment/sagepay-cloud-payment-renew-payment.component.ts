import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SagepayCloudPaymentRenewPaymentComponentDef } from '@df/module-sagepay';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ActionComponent } from '../../../ui/api/action.component';
import { ResultComponent } from '../../../ui/api/result.component';

@Component({
    selector: 'sagepay-cloud-payment-renew-payment',
    templateUrl: './sagepay-cloud-payment-renew-payment.html',
    standalone: true,
    imports: [ActionComponent, CommonModule, ResultComponent, RouterModule, TranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SagepayCloudPaymentRenewPaymentComponent extends SagepayCloudPaymentRenewPaymentComponentDef {}

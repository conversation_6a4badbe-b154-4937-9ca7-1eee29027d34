@if (isSelected()) {
<div>
    <input-wrap class="mui-input-no-padding">
        <input class="input"
               type="text"
               pattern="[0-9 ]*"
               inputmode="numeric"
               name="ccNumber"
               data-cs-mask
               required
               autocomplete="cc-number"
               [muiInput]="'Card number' | translate"
               [(ngModel)]="ccNumber" />
    </input-wrap>
    <input-wrap>
        <input type="text"
               class="input"
               name="ccName"
               required
               data-cs-mask
               autocomplete="cc-name"
               [muiInput]="'Card holder name' | translate"
               [(ngModel)]="data.holderName">
    </input-wrap>
    @if (cardNotMatched) {
    <input-wrap>
        <select class="select"
                name="ccType"
                required
                data-cs-mask
                [muiInput]="'Select your card type' | translate"
                [(ngModel)]="data.type"
                (change)="updateSelectedCardType()">
            @for (type of cardTypes; track $index) {
            <option [value]="type.value">{{type.label}}</option>
            }
        </select>
    </input-wrap>
    }
    <input-wrap class="w-8 p-r-1 m-b-2 m-b-3-s"
                [messages]="{
                    pattern: ('Please enter a date in the format MM/YY' | translate),
                    minlength: ('Please enter a date in the format MM/YY' | translate)
                }">
        <input class="input"
               type="text"
               inputmode="numeric"
               name="ccDate"
               required
               autocomplete="cc-exp"
               minlength="5"
               maxlength="5"
               placeholder="MM/YY"
               [pattern]="datePattern"
               data-cs-mask
               [muiInput]="'Expiry date' | translate"
               [(ngModel)]="ccDate" />
    </input-wrap>
    <input-wrap class="w-4 m-b-2 m-b-3-s">
        <input class="input"
               placeholder="CVV"
               type="text"
               pattern="[0-9]*"
               inputmode="numeric"
               name="cvc"
               data-cs-mask
               required
               minlength="3"
               maxlength="4"
               autocomplete="cc-csc"
               [muiInput]="'CVV'"
               [(ngModel)]="data.cvc" />
    </input-wrap>
    @if (customer.isLoggedIn) {
    <checkbox class="block m-b-2-s"
              name="store_cc"
              [(ngModel)]="data.store_cc">
        <span class="col-12 p2 fs-3-l">{{'Save securely for future orders' | translate}}</span>
    </checkbox>
    }
</div>
}

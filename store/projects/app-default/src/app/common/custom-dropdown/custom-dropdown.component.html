<input-wrap class="pos-relative z-8 _table"
            [offClickOff]="!customTableSelectState?.value"
            [offClickFilter]="'._custom-table-list'"
            (offClick)="closeCustomTableSelect()">

    <div class="pos-relative m-t m-b">
        <select class="input"
                name="table"
                autocomplete="xyz"
                [muiInput]="dropdownLabel"
                [(ngModel)]="selectedCustomTable"
                [toggleClass]="'_is-open'"
                [toggleClassBy]="customTableToggleId"
                (ngModelChange)="onCustomTableModelChange()">
            @for (table of tables; track $index) {
            <option [value]="table.title">{{table.title}}</option>
            }
        </select>

        <div class="fill z-8 cursor-pointer"
             (click)="toggleCustomTableSelect()"></div>

        <div class="pos-absolute right-0 top-100 left-0 z-9">
            <div class="hide-up-container">
                <div class="ng-hide ng-hide-animate hide-up"
                     [toggleClass]="'!ng-hide'"
                     [toggleClassBy]="customTableToggleId">

                    <div class="bg-col-w b-r b-b b-l b-col-21 _table-list">
                        <ul class="pe-auto p-r-2 p-b-2 p-l-2">
                            @for (table of tables; track $index) {
                            <li class="block p-a-2 p-a-4-s b-radius-7 cursor-pointer"
                                [class._is-active]="selectedCustomTable === table.title"
                                (click)="selectCustomTable(table.title)">
                                {{table.title}}
                            </li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</input-wrap>

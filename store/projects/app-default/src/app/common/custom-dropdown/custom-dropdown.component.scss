@use '~@tomandco/atomic/scss/atom/color';

:host ::ng-deep {
    ._table {
        .input.disabled,
        .input.is-disabled,
        .input:disabled,
        .input[disabled],
        .mui-input.is-disabled select ~ label,
        .mui-input select[disabled] ~ label {
            color: color.get(12) !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        &:hover .input:not(._is-open) {
            border-color: color.get(12);
        }

        &-list {
            border-bottom-left-radius: 7px;
            border-bottom-right-radius: 7px;
        }

        ul {
            overflow: hidden;
            max-height: 200px;
            overflow-y: auto;

            li {
                &._is-active {
                    background-color: color.get(4);
                }

                &:hover {
                    background-color: color.get(34);
                }
            }
        }
    }

    .input._is-open {
        border-color: color.get(21);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom: 0px;
        background-image: url('/assets/images/arrow-up.svg');
    }
}

<ng-form #form="ngForm">
    <!-- SWITCH: logged/unlogged user -->
    @if (isLoggedIn) {
    <p class="h2 m-t-1-s">{{'You’re logged in' | translate}}</p>

    <p class="m-t-1 m-t-3-s m-t-3-m"
       [sizeClass]="'XL: p1, SM: p2'">{{'Confirm your email to continue' | translate}}</p>
    } @else {
    <login-form-content-text [checked]="isChecked()"
                             [isLoggedIn]="isLoggedIn"
                             [shouldRegister]="shouldRegister()"
                             [shouldFillRegisterData]="shouldFillRegisterData"
                             [isGuestCheckout]="isGuestCheckout"
                             [logoutSuccess]="logoutSuccess"
                             [checkoutVariant]="true"
                             [variant]="'checkout'" />
    }

    <!-- email address -->
    @if (isLoggedIn) {
    <input-wrap class="block fake-disabled">
        <input class="input"
               type="email"
               name="email"
               data-cs-mask
               disabled
               email
               [attr.placeholder]="'Email address' | translate"
               [muiInput]="'Email address' | translate"
               [(ngModel)]="email">
    </input-wrap>
    } @else {
    <input-wrap class="pos-relative block async-validation"
                [class.has-validation]="canShowEmailValidate(emailInput?.value)"
                [style.margin-top.px]="app.client.isS || app.client.isM ? -9 : null"
                [canShowValidate]="canShowEmailValidate(emailInput?.value)"
                [messages]="{
                    email: ('Valid email is required' | translate),
                    required: ('Valid email is required' | translate),
                    LOQATE_INVALID_EMAIL: ('Valid email is required' | translate)
                }">
        <input class="input"
               type="email"
               name="email"
               data-cs-mask
               required
               [attr.placeholder]="'Email address' | translate"
               validatePatternAsync="email"
               [loqateEmail]="shouldFillRegisterData"
               [muiInput]="'Email address' | translate"
               [muiInputValidateOnSubmit]="!shouldFillRegisterData"
               [(ngModel)]="email"
               [ngModelOptions]="{updateOn: 'blur'}"
               (blur)="handleEmailBlur()"
               (keydown)="handleKeyDown($event)"
               #emailInput>
        @if (checkStatus.busy || form.pending) {
        <span class="pos-absolute top-3 right-3 bottom-0 flex flex-middle flex-justify-center pe-none">
            <i class="icon-loading"
               aria-hidden="true"></i>
        </span>
        }
    </input-wrap>
    }

    <!-- logout success-->
    @if (logoutSuccess) {
    <div class="m-t-2 col-13 p2">
        <!-- logout message start -->
        {{'Don\'t worry your items in your bag have been kept' | translate}}
    </div>
    }

    <!-- logout -->
    @if (isLoggedIn) {
    <div class="m-t-2"
         [sizeClass]="'XL: p1, SM: p2'">
        <!-- logout message start -->
        {{'Not you?' | translate}}
        <action class="button-inline no-fs"
                [style.margin]="'0 0.2em'"
                [status]="logoutStatus"
                (click)="logout()">
            <!-- logout button text -->
            {{'Click here' | translate}}
        </action>
        <!-- logout message end -->
        &thinsp;{{'to sign out.' | translate}}&thinsp;
        <span class="fs-3">{{'Items in your shopping bag will be kept.' | translate}}</span>
    </div>
    }

    <!-- guest -->
    @if (isGuestCheckout) {
    <div class="m-t-2"
         [sizeClass]="'XL: p1, SM: p2'">
        {{'You\'re checking out as a guest.' | translate}}
        <button class="link fw-bold"
                [style.margin]="'0 0.2em'"
                type="button"
                (click)="leaveGuestCheckout()">
            {{'Click here' | translate}}
        </button>
        {{'if you would like to sign in to your OB account' | translate}}.
    </div>
    }

    <!-- newsletter block for logged in customers -->
    @if (isLoggedIn && showSubscriptionOptIn && email) {
    <checkout-newsletter class="w-12 m-t-2 m-t-6-s"
                         [data]="data"
                         [source]="'checkout_start_softoptin'"
                         (dataChange)="dataChangeHandle($event)" />
    } @else {
    <!-- if should login : login form -->
    @if (shouldLogin() && !shouldFillRegisterData) {
    <!-- password input -->
    <input-wrap>
        <view-password>
            <input class="input"
                   type="password"
                   name="password"
                   data-cs-mask
                   [attr.placeholder]="'Enter password'"
                   required
                   [muiInput]="'Password' | translate"
                   [(ngModel)]="data.password"
                   (keyup.enter)="login(onLoginSuccess)">
        </view-password>
    </input-wrap>

    <!-- reset my password -->
    <action class="button-inline cursor-pointer block m-t-2"
            sl-button="reset"
            [status]="passwordStatus"
            (click)="resetPassword()">{{'Forgot your password?' | translate}}</action>

    <result class="block m-t-1 p1"
            sl-result="reset"
            [status]="passwordStatus"
            [success]="'Password reset email has been sent to your email address' | translate"></result>

    @if (showSubscriptionOptIn && email) {
    <checkout-newsletter class="w-12 m-t-2 m-t-6-s"
                         [data]="data"
                         [source]="'checkout_start_softoptin'"
                         (dataChange)="dataChangeHandle($event)" />
    }

    <!-- action button -->
    <action class="w-12 button m-t"
            sl-button="login"
            [status]="loginStatus"
            (click)="login(onLoginSuccess)">{{'Sign in' | translate}}</action>

    <result sl-result="login"
            class="block m-t-1 c1"
            [status]="loginStatus"></result>

    <!-- continue as guest button -->
    @if (canUseGuestCheckout) {
    <action class="w-12 button-1 m-t"
            [class.is-busy]="checkStatus.busy || form.pending"
            (click)="continueAsGuestToDelivery(true)">{{'Continue with guest checkout' | translate}}</action>
    }
    }

    <!-- if should register: create account form -->
    @if (shouldFillRegisterData) {
    <!-- first name -->
    <input-wrap class="block async-validation"
                [class.has-validation]="firstNameRef?.value?.length > 1 || form.submitAttempt"
                [canShowValidate]="firstNameRef?.value?.length > 1 || form.submitAttempt">
        <input class="input"
               type="text"
               name="firstname"
               [muiInput]="'First name' | translate"
               maxlength="50"
               data-cs-mask
               required
               [(ngModel)]="shippingAddressData.firstname"
               #firstNameRef>
    </input-wrap>

    <!-- last name -->
    <input-wrap class="block async-validation"
                [class.has-validation]="lastNameRef?.value?.length > 1 || form.submitAttempt"
                [canShowValidate]="lastNameRef?.value?.length > 1 || form.submitAttempt">
        <input class="input"
               type="text"
               name="lastname"
               [muiInput]="'Surname' | translate"
               maxlength="50"
               data-cs-mask
               required
               [(ngModel)]="shippingAddressData.lastname"
               #lastNameRef>
    </input-wrap>

    <!-- newsletter -->
    @if (showSubscriptionOptIn && email) {
    <checkout-newsletter class="w-12 m-t-2 m-t-6-s"
                         [data]="data"
                         [source]="'checkout_start_softoptin'"
                         (dataChange)="dataChangeHandle($event)" />
    }

    <!-- action button -->
    @if (canUseGuestCheckout) {
    <action class="w-12 button-1"
            [ngClass]="showSubscriptionOptIn ? 'm-t-2' : 'm-t'"
            [class.is-busy]="checkStatus.busy || form.pending"
            (click)="continueAsGuestToDelivery()">
        {{'Continue with guest checkout' | translate}}
    </action>
    }
    }
    }

    @if (!shouldLogin() && !shouldRegister()) {
    <!-- action button -->
    @if (!shouldFillRegisterData) {
    <action class="w-12 button m-t-2 m-t-4-m m-t-4-s"
            [status]="checkStatus"
            (click)="continue()">{{'Continue' | translate}}</action>
    }

    <!-- Continue with guest checkout button -->
    @if (!isGuestCheckout && !isLoggedIn && !shouldFillRegisterData) {
    <div class="m-t m-t-11-m m-t-11-s">
        <p class="h2">{{'New to Oliver Bonas?' | translate}}</p>

        @if (canUseGuestCheckout) {
        <action class="w-12 button-1 m-t-2 m-t-3-m m-t-3-s"
                [class.is-busy]="checkStatus.busy || form.pending"
                (click)="continueAsGuestToDelivery(true)">{{'Continue with guest checkout' | translate}}</action>
        }
    </div>
    }
    }
</ng-form>

<ng-form #stepForm="ngForm">
    <!-- dummy field for step validation for step -->
    @if (!isValid) {
    <input type="hidden"
           name="dummy"
           required
           [(ngModel)]="dummy">
    }
</ng-form>

<ng-template #newsletterCheckbox>
    @if (showSubscriptionOptIn) {
    <cms-newsletter-block [newsletter]="data.newsletter"
                          (newsletterChange)="dataChangeHandle($event)" />
    }
</ng-template>

import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { LoqateEmailValidateDirective } from '@df/module-loqate/email/loqate-email.validator';
import { Login } from '@df/session/login/login.interfaces';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { LoginFormContentTextComponent } from '../../login/form/content/login-form-content-text.component';
import { ActionComponent } from '../../ui/api/action.component';
import { ResultComponent } from '../../ui/api/result.component';
import { InputWrapBodyDirective } from '../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../ui/form/input-wrap.component';
import { MuiInputDirective } from '../../ui/form/mui-input.directive';
import { ValidatePatternAsyncDirective } from '../../ui/form/validate/validate-pattern-acync.directive';
import { ViewPasswordComponent } from '../../ui/form/view-password.component';
import { CheckoutNewsletterComponent } from '../newsletter/checkout-newsletter.component';
import { CheckoutAccountAbstract } from './checkout-account.abstract';

declare module '@df/session/login/login.interfaces' {
    export namespace Login {
        export interface ILoginFormData {
            newsletter?: boolean;
        }
    }
}

@Component({
    selector: 'checkout-account',
    templateUrl: './checkout-account.component.html',
    styleUrl: './checkout-account.component.scss',
    standalone: true,
    imports: [
        ActionComponent,
        CheckoutNewsletterComponent,
        CommonModule,
        FormsModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        LoginFormContentTextComponent,
        LoqateEmailValidateDirective,
        MuiInputDirective,
        ResultComponent,
        SizeClassDirective,
        TranslatePipe,
        ValidatePatternAsyncDirective,
        ViewPasswordComponent
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CheckoutAccountComponent extends CheckoutAccountAbstract {}

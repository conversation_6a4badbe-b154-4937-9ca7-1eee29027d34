<ng-form #form="ngForm">
    <ng-container *ngTemplateOutlet="text, context:
                        isLoggedIn ?
                        {title: 'You’re logged in', description: 'Confirm your email to continue' } :
                        guestFormVisible() ?
                        {title: 'You’re new here, welcome!', description: 'Please enter your details. You can create an account later.' } :
                        loginFormVisible() ?
                        {title: 'Welcome back!', description: 'Enter your password to sign in or continue as a guest.' } :
                        {title: 'Sign in or checkout as a guest'}" />
    @if(!isLoggedIn && !loginFormVisible() && !guestFormVisible()){
    <button class="w-12 m-t-1 m-t-3-s"
            (click)="showLoginForm()">
        <ng-container
                      *ngTemplateOutlet="button, context: {title: 'Sign in', description: 'Checkout faster with saved details', textColor: 'col-w', bgColor: 'bg-col-1'}" />
    </button>
    <button class="w-12 m-t-2 m-t-3-s"
            (click)="showGuestForm()">
        <ng-container
                      *ngTemplateOutlet="button, context: {title: 'Continue with guest checkout', description: 'You can create an account later', textColor: 'col-1', bgColor: 'bg-col-t', borderColor: 'b-col-1'}" />
    </button>
    <p class="c1 col-11 m-l-2 m-t-2">{{'If you need any help please contact customers services' | translate}}</p>
    }
    <!-- email address -->
    @if (isLoggedIn) {
    <input-wrap class="block fake-disabled">
        <input class="input"
               type="email"
               name="email"
               data-cs-mask
               disabled
               email
               [attr.placeholder]="'Email address' | translate"
               [muiInput]="'Email address' | translate"
               [(ngModel)]="email">
    </input-wrap>
    } @else {
    @if(loginFormVisible() || guestFormVisible()){
    <input-wrap class="pos-relative block async-validation"
                [canShowValidate]="form.submitAttempt"
                [style.margin-top.px]="app.client.isS || app.client.isM ? -9 : null"
                [class.has-validation]="form.submitAttempt"
                [messages]="{ email: ('Valid email is required' | translate),
                              required: ('Valid email is required' | translate),
                              LOQATE_INVALID_EMAIL: ('Valid email is required' | translate)
                }">
        <input class="input"
               type="email"
               name="email"
               data-cs-mask
               required
               [attr.placeholder]="'Email address' | translate"
               validatePatternAsync="email"
               [loqateEmail]="true"
               [muiInput]="'Email address' | translate"
               [muiInputValidateOnSubmit]="true"
               [(ngModel)]="email"
               [ngModelOptions]="{updateOn: 'blur'}"
               (blur)="handleEmailBlur()"
               (keydown)="handleKeyDown($event)"
               #emailInput>
        @if (checkStatus.busy || form.pending) {
        <span class="pos-absolute top-3 right-3 bottom-0 flex flex-middle flex-justify-center pe-none">
            <i class="icon-loading"
               aria-hidden="true"></i>
        </span>
        }
    </input-wrap>
    }
    }
    <!-- logout success-->
    @if (logoutSuccess) {
    <div class="m-t-2 col-13 p2">
        <!-- logout message start -->
        {{'Don\'t worry your items in your bag have been kept' | translate}}
    </div>
    }
    <!-- logout -->
    @if (isLoggedIn) {
    <div class="m-t-2"
         [sizeClass]="'XL: p1, SM: p2'">
        <!-- logout message start -->
        {{'Not you?' | translate}}
        <action class="button-inline no-fs"
                [style.margin]="'0 0.2em'"
                [status]="logoutStatus"
                (click)="logout()">
            <!-- logout button text -->
            {{'Click here' | translate}}
        </action>
        <!-- logout message end -->
        &thinsp;{{'to sign out.' | translate}}&thinsp;
        <span class="fs-3">{{'Items in your shopping bag will be kept.' | translate}}</span>
    </div>
    }
    <!-- newsletter block for logged in customers -->
    @if (isLoggedIn && showSubscriptionOptIn && email) {
    <checkout-newsletter class="w-12 m-t-2 m-t-6-s"
                         [data]="data"
                         [source]="'checkout_start_softoptin'"
                         (dataChange)="dataChangeHandle($event)" />
    } @else {
    @if (!isLoggedIn && loginFormVisible()) {
    <!-- password input -->
    <input-wrap>
        <view-password>
            <input class="input"
                   type="password"
                   name="password"
                   data-cs-mask
                   [attr.placeholder]="'Enter password'"
                   required
                   [muiInput]="'Password' | translate"
                   [(ngModel)]="data.password"
                   (keyup.enter)="login(onLoginSuccess)">
        </view-password>
    </input-wrap>
    <!-- reset my password -->
    <action class="button-inline cursor-pointer block m-t-2"
            sl-button="reset"
            [status]="passwordStatus"
            (click)="resetPassword()">{{'Forgot your password?' | translate}}</action>
    <result class="block m-t-1 p1"
            sl-result="reset"
            [status]="passwordStatus"
            [success]="'Password reset email has been sent to your email address' | translate"></result>
    @if (showSubscriptionOptIn && email) {
    <checkout-newsletter class="w-12 m-t-2 m-t-6-s"
                         [data]="data"
                         [source]="'checkout_start_softoptin'"
                         (dataChange)="dataChangeHandle($event)" />
    }
    <!-- action button -->
    <action class="w-12 button m-t-3"
            sl-button="login"
            [status]="loginStatus"
            (click)="login(onLoginSuccess)">{{'Sign in' | translate}}</action>
    <result sl-result="login"
            class="block m-t-1 c1"
            [status]="loginStatus"></result>
    <!-- continue as guest button -->
    @if (canUseGuestCheckout) {
    <action class="w-12 button-1 m-t-1"
            [class.is-busy]="checkStatus.busy || form.pending"
            (click)="showGuestForm()">{{'Continue with guest checkout' | translate}}</action>
    }
    }
    <!-- if should register: create account form -->
    @if (guestFormVisible()) {
    <!-- first name -->
    <input-wrap class="block async-validation"
                [class.has-validation]="firstNameRef?.value?.length > 1 || form.submitAttempt"
                [canShowValidate]="firstNameRef?.value?.length > 1 || form.submitAttempt">
        <input class="input"
               type="text"
               name="firstname"
               [muiInput]="'First name' | translate"
               maxlength="50"
               data-cs-mask
               required
               [(ngModel)]="shippingAddressData.firstname"
               #firstNameRef>
    </input-wrap>
    <!-- last name -->
    <input-wrap class="block async-validation"
                [class.has-validation]="lastNameRef?.value?.length > 1 || form.submitAttempt"
                [canShowValidate]="lastNameRef?.value?.length > 1 || form.submitAttempt">
        <input class="input"
               type="text"
               name="lastname"
               [muiInput]="'Surname' | translate"
               maxlength="50"
               data-cs-mask
               required
               [(ngModel)]="shippingAddressData.lastname"
               #lastNameRef>
    </input-wrap>
    <!-- newsletter -->
    @if (showSubscriptionOptIn && email) {
    <checkout-newsletter class="w-12 m-t-2 m-t-6-s"
                         [data]="data"
                         [source]="'checkout_start_softoptin'"
                         (dataChange)="dataChangeHandle($event)" />
    }
    <!-- action button -->
    @if (canUseGuestCheckout) {
    <action class="w-12 button m-t-3"
            [class.is-busy]="checkStatus.busy || form.pending"
            (click)="continueAsGuestToDelivery()">
        {{'Continue with guest checkout' | translate}}
    </action>
    }
    }
    }
    @if (isLoggedIn) {
    <!-- action button -->
    <action class="w-12 button m-t-2 m-t-4-m m-t-4-s"
            [status]="checkStatus"
            (click)="continue()">{{'Continue' | translate}}</action>
    }
</ng-form>
<ng-form #stepForm="ngForm">
    <!-- dummy field for step validation for step -->
    @if (!isValid) {
    <input type="hidden"
           name="dummy"
           required
           [(ngModel)]="dummy">
    }
</ng-form>
<ng-template #newsletterCheckbox>
    @if (showSubscriptionOptIn) {
    <cms-newsletter-block [newsletter]="data.newsletter"
                          (newsletterChange)="dataChangeHandle($event)" />
    }
</ng-template>
<ng-template #button
             let-title="title"
             let-description="description"
             let-textColor="textColor"
             let-bgColor="bgColor"
             let-borderColor="borderColor">
    <div [ngClass]="['flex flex-justify-between flex-middle left p-t p-b-3 p-r-9 p-l-6 b-a', textColor || '', bgColor || '', borderColor || '' ]"
         [style.borderRadius.px]="5">
        <div>
            <p class="p1">{{title| translate}}</p>
            <p class="p2">{{description | translate}}</p>
        </div>
        <i class="icon-arrow-forward"></i>
    </div>
</ng-template>
<ng-template #text
             let-title="title"
             let-description="description">
    @if(title){
    <p [sizeClass]="'S:h2, !S:s1'">{{title | translate}}</p>
    }
    @if(description){
    <p class="p2">{{description | translate}}</p>
    }
</ng-template>
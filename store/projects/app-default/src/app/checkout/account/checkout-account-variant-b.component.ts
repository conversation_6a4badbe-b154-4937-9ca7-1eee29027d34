import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnChanges, OnInit, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { LoqateEmailValidateDirective } from '@df/module-loqate/email/loqate-email.validator';
import { Login } from '@df/session/login/login.interfaces';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { Subscription } from 'rxjs/internal/Subscription';
import { ActionComponent } from '../../ui/api/action.component';
import { InputWrapBodyDirective } from '../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../ui/form/input-wrap.component';
import { MuiInputDirective } from '../../ui/form/mui-input.directive';
import { ValidatePatternAsyncDirective } from '../../ui/form/validate/validate-pattern-acync.directive';
import { ViewPasswordComponent } from '../../ui/form/view-password.component';
import { CheckoutNewsletterComponent } from '../newsletter/checkout-newsletter.component';
import { CheckoutAccountAbstract } from './checkout-account.abstract';

@Component({
    selector: 'checkout-account-variant-b',
    templateUrl: './checkout-account-variant-b.component.html',
    styleUrl: './checkout-account.component.scss',
    standalone: true,
    imports: [
        ActionComponent,
        CheckoutNewsletterComponent,
        CommonModule,
        FormsModule,
        InputWrapBodyDirective,
        InputWrapComponent,
        LoqateEmailValidateDirective,
        MuiInputDirective,
        SizeClassDirective,
        TranslatePipe,
        ValidatePatternAsyncDirective,
        ViewPasswordComponent
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CheckoutAccountVariantBComponent extends CheckoutAccountAbstract implements OnInit, OnChanges {
    guestFormVisible = signal(false);
    loginFormVisible = signal(false);

    emailFieldStatusSubscription?: Subscription;

    override get isValid(): boolean {
        if (this.customer.model.isLoggedIn) {
            return true;
        }

        if (this.loginFormVisible()) {
            return false;
        }

        return !!this.form?.controls?.['email']?.valid;
    }

    protected override onLogoutSuccess(): void {
        super.onLogoutSuccess();
        this.loginFormVisible.set(false);
    }

    override setCheckResult(checkResult?: Login.IResponseAccountExists) {
        super.setCheckResult(checkResult);
        if (checkResult) {
            if (this.hasAccount()) {
                this.showLoginForm();
            } else {
                this.showGuestForm();
            }
        }
    }

    override subscribeToEmailValidation() {}

    override onEmailChange(newEmail: string | undefined, oldEmail: string | undefined) {
        super.onEmailChange(newEmail, oldEmail);
        this.submitAttempt = false;
        this.emailFieldStatusSubscription?.unsubscribe();
        this.emailFieldStatusSubscription = this.form?.controls['email']?.statusChanges.subscribe(status => {
            if (status === 'VALID') {
                this.check();
            }
            this.submitAttempt = false;
        });
    }

    showGuestForm(): void {
        this.logoutSuccess = false;
        this.loginFormVisible.set(false);
        this.guestFormVisible.set(true);
        this.submitAttempt = false;
        this.detectChanges();
    }

    showLoginForm(): void {
        this.logoutSuccess = false;
        this.guestFormVisible.set(false);
        this.loginFormVisible.set(true);
        this.submitAttempt = false;
        this.detectChanges();
    }

    set submitAttempt(value: boolean) {
        if (this.form) {
            this.form['submitAttempt'] = value;
            this.form.ngSubmit.emit();
        }
    }
}

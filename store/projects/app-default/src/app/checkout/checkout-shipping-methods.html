<ng-form #stepForm="ngForm">
    <!-- shipping methods -->
    @if (noShippingNeeded) {
    <!-- virtual order -->
    <p class="fw-bold">{{'No shipping information required' | translate}}</p>
    } @else {
    <!-- regular order -->
    <div class="delivery-options">
        <!-- no methods found notice -->
        @if (!methods.length) {
        <p class="fw-bold">{{'No shipping methods found for this delivery address.' | translate}}</p>
        }

        <!-- methods list -->
        <radio-group name="shippingMethod"
                     [(ngModel)]="data.method"
                     (ngModelChange)="handleModelChange()">
            @for (method of methods; track method.code) {
            <div class="w-12 b-radius-7 p-a-3 b-a"
                 [class.m-b]="!$last"
                 [ngClass]="data.method === method.code ? 'bg-col-34 b-col-21' : 'bg-col-w b-col-4'">

                <radio class="block w-12 p-l-6 radio-full-width-label"
                       [value]="method.code">
                    <div class="flex flex-justify-between p1">
                        <span>{{method.title}}</span>
                        <price class="fw-regular"
                               [value]="method.price" />
                    </div>
                    @if (method.lead_time) {
                    <p class="w-12 p2 col-13"
                       [innerHTML]="method.lead_time"></p>
                    }
                    @if (method.description) {
                    <p class="w-12 p2 col-13"
                       [innerHTML]="method.description"></p>
                    }
                </radio>
            </div>
            }
        </radio-group>

        <input-wrap>
            <input type="hidden"
                   name="shippingMethod"
                   required
                   [(ngModel)]="data.method">
        </input-wrap>

        @if (displayParcelInformation) {
        <p class="m-t p1 fw-bold">{{'Your delivery will be split into' | translate}} {{parcelNumber}}
            >{{'parcels' | translate}}</p>
        }

        @if (displayQuestions) {
        <checkout-delivery-questions class="block m-t-8 m-b" />
        }
    </div>
    }
</ng-form>

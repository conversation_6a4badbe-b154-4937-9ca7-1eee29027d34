<ng-form #stepForm="ngForm">
    <!-- shipping methods -->
    @if (noPaymentNeeded) {
    <!-- virtual order -->
    <p class="label m-t-0">{{'No payment information required' | translate}}</p>
    } @else {

    <!-- no methods found notice -->
    @if (!methods?.length) {
    <p class="label m-t-0">{{'No payment methods found.' | translate}}</p>
    }

    <radio-group name="paymentMethod"
                 [(ngModel)]="data.method"
                 (ngModelChange)="handleModelChange()">
        @if (!methodChanged && defaultMethod && [defaultMethod.code, 'paypal_express'].indexOf($any(data).method) !==
        -1) {
        <div class="w-12 m-b b-radius-7 b-a b-col-21">
            <div class="p-a-2 p-r-4-m p-r-4-s p-l-4-m p-l-4-s flex flex-middle flex-justify-between">
                <span class="p1">{{'Default payment' | translate}}</span>

                <button class="lh-1"
                        type="button"
                        (click)="changeHandle()">
                    <span [sizeClass]="'SM: fs-3 fw-bold, XL: s2'">{{'Change' | translate}}</span>
                </button>
            </div>

            <div class="w-12 p-a-3 p-l-5-s b-radius-7"
                 [ngClass]="{'bg-col-w b-col-4': data.method !== defaultMethod.code, 'bg-col-34 b-col-21': data.method === defaultMethod.code, 'b-b b-t': data.method === defaultMethod.code && methodHasMore(defaultMethod)}">

                <radio class="block w-12 p-l-6 p-l-9-s radio-full-width-label"
                       [value]="defaultMethod.code">
                    <ng-container *ngTemplateOutlet="paymentMethod, context: { $implicit: defaultMethod }" />
                </radio>
            </div>

            <ng-container *ngTemplateOutlet="paymentMethodMore, context: { $implicit: defaultMethod }" />
        </div>

        @if (defaultMethod.isExpired) {
        <p class="p-b-2 p-b-3-s p2 col-2">
            {{creditCardExpiredMessage || ('This card has expired. Remove and another payment.' | translate)}}
        </p>
        }

        @for (method of methods | applePayFilter : ['braintree_applepay'] : 3; track method.code) {
        @if (method.code === 'paypal_express') {
        <div class="w-12 b-radius-7 b-a"
             [ngClass]="[!$last ? 'm-b-2 m-b-3-s': '', data.method === method.code ? 'b-col-21' : 'b-col-4']"
             [style.padding-bottom.px]="1">
            <div class="w-12 p-a-3 p-l-5-s b-radius-7"
                 [ngClass]="{'bg-col-w b-col-4': data.method !== method.code, 'bg-col-34 b-col-21': data.method === method.code, 'b-b': data.method === method.code && methodHasMore(method)}">
                <radio class="block w-12 p-l-6 p-l-9-s radio-full-width-label"
                       [value]="method.code">
                    <ng-container *ngTemplateOutlet="paymentMethod, context: { $implicit: method }">
                    </ng-container>
                </radio>
            </div>
            <ng-container *ngTemplateOutlet="paymentMethodMore, context: { $implicit: method }" />
        </div>
        }
        }
        } @else {
        @for (method of methods | applePayFilter : ['braintree_applepay'] : 3; track method.code) {
        <div class="w-12 b-radius-7 b-a"
             [ngClass]="[!$last ? 'm-b' : '', data.method === method.code ? 'b-col-21' : 'b-col-4']"
             [style.padding-bottom.px]="1">

            <div class="w-12 p-a-3 p-l-5-s b-radius-7"
                 [ngClass]="{'bg-col-w b-col-4': data.method !== method.code, 'bg-col-34 b-col-21': data.method === method.code, 'b-b': data.method === method.code && methodHasMore(method)}">

                <radio class="block w-12 p-l-6 p-l-9-s radio-full-width-label"
                       [value]="method.code">
                    <ng-container *ngTemplateOutlet="paymentMethod, context: { $implicit: method }" />
                </radio>
            </div>

            <ng-container *ngTemplateOutlet="paymentMethodMore, context: { $implicit: method }" />
        </div>
        @if ($any(method).isExpired) {
        <p class="p-b-2 p-b-3-s p2 col-2">
            {{creditCardExpiredMessage || ('This card has expired. Remove and another payment.' | translate)}}
        </p>
        }
        }
        }
    </radio-group>

    <input-wrap>
        <input type="hidden"
               [(ngModel)]="data.method"
               name="paymentMethod"
               required>
    </input-wrap>
    <result [status]="saveStatus" />
    }
</ng-form>

<ng-template #paymentMethod
             let-method>
    <div class="flex flex-justify-between flex-middle pos-relative">
        <span class="p1">
            @if (!!method.isCard) {
            {{getCardTitle(method.type)}} Ending **** {{method.data.last_four}}
            } @else {
            {{method.title}}
            }
        </span>

        <span class="pos-absolute right-0">
            @switch (method.code) {
            @case ('braintree_applepay') {
            <span class="payment-method-logo payment-method-logo--apple-pay"></span>
            }
            @case ('paypal_express') {
            <span class="payment-method-logo payment-method-logo--paypal"></span>
            }
            @case ('sagepaysuitepi') {
            <span class="flex">
                <span class="payment-method-logo payment-method-logo--visa"
                      style="margin-right: 0.25em;"></span>
                <span class="payment-method-logo payment-method-logo--amex"
                      style="margin-right: 0.25em;"></span>
                <span class="payment-method-logo payment-method-logo--mastercard"
                      style="margin-right: 0.25em;"></span>
            </span>
            }
            }

            <span class="payment-method-logo payment-method-logo--klarna"
                  *ifKlarnaPayment="method.code"></span>
            @if (method.isCard && method.type) {
            <span class="payment-method-logo"
                  [ngClass]="'payment-method-logo--' + method.type"></span>
            }
        </span>
    </div>
</ng-template>

<ng-template #paymentMethodMore
             let-method>
    @if (method && methodHasMore(method)) {
    <div class="p-a-3"
         [ngClass]="{'visually-hidden': data.method !== method.code, 'p-t-0': method.code === 'sagepaysuitepi' || method.isCard }">
        @if (method.code === 'sagepaysuitepi') {
        <sagepay-pi-form class="w-12" />
        }

        @if (method.isCard && method.data.method === 'sagepaysuitepi') {
        <sagepay-pi-saved-card class="w-12"
                               [card]="method" />
        }

        @if (method.code === 'braintree_applepay') {
        <i class="icon-info p-r-2"
           aria-hidden="true"></i>
        <span class="c1">{{'Hit \'Apple Pay\' and confirm your purchase using Touch/Face ID on your Apple device' |
            translate}}

        </span>
        }

        <ng-container *ifKlarnaPayment="method.code">
            <klarna-payment class="block"
                            [methodCode]="method.code" />
        </ng-container>
    </div>
    }
</ng-template>

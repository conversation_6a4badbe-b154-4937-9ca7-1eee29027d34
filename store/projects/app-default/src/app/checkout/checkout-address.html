<form class="w-12"
      autocomplete="off"
      method="POST"
      #stepForm="ngForm">
    @if (shouldShowList) {
    <!-- billing to shipping -->
    @if (shouldShowToShipping) {
    <div class="flex flex-justify-between p-a-2 p-a-4-m p-a-4-s b-radius-7 bg-col-5"
         [class.m-b]="!checkoutFormComponent.isBillingToShipping">
        @if (!checkoutFormComponent.isBillingToShipping) {
        <checkbox class="w-12"
                  name="to_shipping"
                  [(ngModel)]="checkoutFormComponent.isBillingToShipping"
                  (ngModelChange)="onAddressStatusChange($event)">
            <span class="p1 fs-4-l">{{'Same as delivery address' | translate}}</span>
        </checkbox>
        } @else {
        <span class="p1 fs-4-l">{{'Same as delivery address' | translate}}</span>
        <button type="button"
                (click)="changeBillingToShipping()">
            <span [sizeClass]="'SM: fs-3 fw-bold, XL: s2'">{{'Change' | translate}}</span>
        </button>
        }
    </div>
    }

    <!-- address book entries and add new  -->
    @if (shouldShowNotToShippingPart) {
    <!-- customer addresses -->
    <radio-group name="customer_address_id"
                 [(ngModel)]="data.customer_address_id"
                 (ngModelChange)="onAddressStatusChange($event)">
        @for (item of addressBook.addresses; track item.id) {
        <div class="w-12 b-radius-7 p-a-3 b-a delivery-options"
             [class.m-b]="!$last || shouldShowAddNew"
             [ngClass]="data.customer_address_id === item.id ? 'bg-col-34 b-col-21' : (((item.is_shipping && !isBilling) || (item.is_billing && isBilling)) ? 'bg-col-33 b-col-4' : 'bg-col-w b-col-4')">

            <radio class="block w-12 p-l-6 radio-full-width-label"
                   [value]="item.id">
                <p class="p1"
                   data-cs-mask>{{item.oneLine}}</p>
                @if ((item.is_shipping && !isBilling) || (item.is_billing && isBilling)) {
                <p class="p2 m-t-1 col-13">{{'Default address' | translate}}
                </p>
                }
            </radio>
        </div>
        }

        @if (shouldShowAddNew) {
        <div class="w-12 b-radius-7 p-a-3 b-a"
             [ngClass]="data.customer_address_id === 0 ? 'bg-col-34 b-col-21' : 'bg-col-w b-col-4'">
            <radio [value]="0"
                   class="block w-12 p-l-6 radio-full-width-label">
                <p class="p1">{{'Add new address' | translate}}</p>
            </radio>
        </div>
        }
    </radio-group>
    }
    }

    <!-- new address form -->
    @if (shouldShowForm) {
    <address-fieldset [form]="stepForm"
                      [data]="data"
                      [submitSubject]="checkoutStepComponent.submittedSubject"
                      [isBilling]="isBilling"
                      [expandable]="true"
                      [phoneFlags]="true"
                      (inputsBlur)="onInputsBlur()"
                      (addressStatusChange)="onAddressStatusChange($event)"
                      (searcherSelectionApplied)="onSearcherSelectionApplied()" />
    }

    <!-- error notice if no address is selected -->
    @if (shouldShowNotToShippingPart && !shouldShowForm) {
    <input-wrap>
        <input type="hidden"
               [(ngModel)]="data.customer_address_id"
               name="customerAddressId"
               required>
    </input-wrap>
    }
</form>
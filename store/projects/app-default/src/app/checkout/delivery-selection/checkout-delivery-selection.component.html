<ng-form #stepForm="ngForm">
    <radio-group name="selection"
                 [value]="deliveryType"
                 [ngModel]="deliveryType">
        <div class="b-radius-7 p-a-3 b-a b-col-4 shipping-selection pos-relative"
             [class.is-disabled]="!hasNonCollectionMethods">
            <i class="svg-icon--delivery pos-absolute right-3 top-3 right-4-s top-4-s fs-4-l p1"
               aria-hidden="true"></i>
            <radio class="block w-12 p-l-6 radio-full-width-label"
                   [value]="'shipping'"
                   (click)="selectShipping()">
                <p class="p1">{{'Delivery' | translate}}</p>
                @if (methodsLabel) {
                <p class="w-12 p2 col-13">
                    {{'Choose from a selection of available delivery options' | translate}}
                </p>
                }
            </radio>
        </div>

        <div class="b-radius-7 p-a-3 b-a b-col-4 shipping-selection pos-relative m-t"
             [class.none]="!hasCollectionMethods">
            <i class="svg-icon--click-and-collect pos-absolute right-3 top-3 right-4-s top-4-s fs-4-l p1"></i>
            <radio class="block w-12 p-l-6 radio-full-width-label"
                   [value]="'collection'"
                   (click)="selectCollection()">
                <p class="p1">{{'Click & Collect' | translate}}</p>
                <p class="w-12 p2 col-13">
                    {{'Collect your order from a store near you' | translate}}
                </p>
            </radio>
        </div>
    </radio-group>

    @if (showDeliveryError && (hasCollectionMethods || hasNonCollectionMethods)) {
    <div class="pos-relative b-radius-7 b-a b-col-4 col-13 bg-col-33 pe-none"
         [style.padding]="'12px 16px 12px 16px'"
         [style.marginTop.px]="20">
        <p class="s1">{{'Click & Collect / Parcel shop is unavailable' | translate}}</p>
        <p class="w-12 p2">
            {{'Due to items arriving from different locations' | translate}}
        </p>
    </div>
    }
</ng-form>

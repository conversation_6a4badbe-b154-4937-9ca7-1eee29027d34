import { CommonModule } from '@angular/common';
import { After<PERSON>iewInit, Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup, FormsModule, NgForm } from '@angular/forms';
import { IRequestCallback, IRequestCallbackFunction } from '@df/api/request/request.service';
import { SessionStorageService } from '@df/browser/storage/session-storage.service';
import { CheckoutFormComponentDef } from '@df/checkout/checkout-form.component.def';
import { Checkout } from '@df/checkout/checkout.interfaces';
import { MultisiteService } from '@df/core/service/multisite';
import { Status } from '@df/core/status';
import { getElementHeight } from '@df/dom/fn/get-element-height';
import { IScrollToOptions } from '@df/dom/scroll/scroll-to.service';
import { AbModule } from '@df/module-ab/ab.module';
import { AbService } from '@df/module-ab/ab.service';
import { ApplePayService } from '@df/module-apple-pay/apple-pay.service';
import { Braintree } from '@df/module-braintree/braintree.namespace';
import { Giftcards } from '@df/module-giftcards/giftcards';
import { ImageDirective } from '@df/module-image/image.directive';
import { injectTimeoutService } from '@df/ng/timeout.service';
import { ValuesPipe } from '@df/ui/common/values.pipe';
import { PriceComponent } from '@df/ui/price/price.component';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import debounce from 'lodash-es/debounce';
import find from 'lodash-es/find';
import flatten from 'lodash-es/flatten';
import omit from 'lodash-es/omit';
import { merge } from 'rxjs/internal/observable/merge';
import { Subject } from 'rxjs/internal/Subject';
import { BasketDiscountButtonsComponent } from '../basket/basket-discount-buttons.component';
import { CouponRemoveComponent } from '../basket/coupon-remove.component';
import { StoreDecorator } from '../catalog/model/store/store.decorator';
import { BasketOosItemsWarningComponent } from '../catalog/ui/basket/basket-oos-items-warning.component';
import { StickyFreeComponent } from '../common/sticky-free/sticky-free.component';
import { BasketDecorator } from '../customer/api/basket/basket.decorator';
import { BraintreeApplePayComponent } from '../module/braintree/apple-pay/braintree-apple-pay.component';
import { ExponeaService } from '../module/exponea/exponea.service';
import { GiftcardsAddedGiftcardComponent } from '../module/giftcards/giftcards-added-giftcard.component';
import { SAGEPAY_PI_SAVED_CARD_STATUS_DISPLAY_SUBMITTED } from '../module/sagepay/pi/sagepay.service.decorator';
import { IfLoggedInDirective } from '../module/session/if-logged-in.directive';
import { IfNotLoggedInDirective } from '../module/session/if-not-logged-in.directive';
import { StoreCreditRemoveComponent } from '../module/store-credit/remove/store-credit-remove.component';
import { ActionComponent } from '../ui/api/action.component';
import { OverlayComponent } from '../ui/common/overlay/overlay.component';
import { StoreCreditService } from './../module/store-credit/store-credit.service';
import { CheckoutAccountStatusComponent } from './account-status/checkout-account-status.component';
import { CheckoutAccountVariantBComponent } from './account/checkout-account-variant-b.component';
import { CheckoutAccountComponent } from './account/checkout-account.component';
import { CheckoutAddressComponent } from './checkout-address.component';
import { DeliveryAddressCmsBlockComponent, ReceiveOrderCmsBlockComponent } from './checkout-cmsblocks';
import { CheckoutConfirmationComponent } from './checkout-confirmation.component';
import { CheckoutDeliveryMethodStatusComponent } from './checkout-delivery-method-status.component';
import { CheckoutDeliveryStatusComponent } from './checkout-delivery-status.component';
import { CheckoutMinibasketModalOpenDirective } from './checkout-minibasket-modal-open.directive';
import { CheckoutMinibasketComponent } from './checkout-minibasket.component';
import { CheckoutPaymentAddressStatusComponent } from './checkout-payment-address-status.component';
import { CheckoutPaymentMethodsComponent } from './checkout-payment-methods.component';
import { CheckoutPaymentStatusComponent } from './checkout-payment-status.component';
import { CheckoutResultContentBlockComponent } from './checkout-result-content-block.component';
import { CheckoutResultComponent } from './checkout-result.component';
import { CheckoutServiceDecorator } from './checkout-service.service.decorator';
import { CheckoutShippingMethodsComponent } from './checkout-shipping-methods.component';
import { CheckoutStepComponent } from './checkout-step.component';
import { CheckoutSummaryStepComponent } from './checkout-summary-step.component';
import { CheckoutCollectionComponent } from './collection/checkout-collection.component';
import { MetapackCollectionPoint } from './collection/metapack/metapack-collection-point';
import { CheckoutDeliverySelectionComponent } from './delivery-selection/checkout-delivery-selection.component';

declare module '@tomandco/nexus-api-types' {
    namespace Nexus.Lib.General {
        interface Reviews {
            product_global_summary: IReview;
            merchant_global_summary: IReview;
        }
    }

    interface IReview {
        rating_summary: number;
        reviews_count: number;
    }
}

declare module '@df/checkout/checkout.interfaces' {
    namespace Checkout {
        interface IData {
            contains_furniture?: boolean;
        }

        export enum EVENTS {
            LogoutSuccess = 'checkout.logout.success'
        }
    }
}

export enum CheckoutCollectionMethods {
    storePickup = 'storepickup_storepickup',
    metapackCollection = 'metapackcollection_metapackcollection'
}

export const CHECKOUT_STEPS_NAMES: Record<string, Checkout.EStepName | string> = {
    account: Checkout.EStepName.account,
    deliverySelection: 'deliverySelection',
    deliveryAddress: 'deliveryAddress',
    deliveryMethod: 'deliveryMethod',
    collection: 'collection',
    payment: Checkout.EStepName.payment,
    summary: 'summary'
};

export enum CheckoutDeliveryType {
    collection = 'collection',
    shipping = 'shipping'
}

export const ACCOUNT_STEP_EXPERIMENT_ID = 'checkoutAccountStep';

@Component({
    selector: 'checkout-form',
    templateUrl: './checkout-form.html',
    styleUrl: './checkout-form.scss',
    standalone: true,
    imports: [
        AbModule,
        ActionComponent,
        BasketDiscountButtonsComponent,
        BasketOosItemsWarningComponent,
        BraintreeApplePayComponent,
        CheckoutAccountComponent,
        CheckoutAccountStatusComponent,
        CheckoutAccountVariantBComponent,
        CheckoutAddressComponent,
        CheckoutCollectionComponent,
        CheckoutConfirmationComponent,
        CheckoutDeliveryMethodStatusComponent,
        CheckoutDeliverySelectionComponent,
        CheckoutDeliveryStatusComponent,
        CheckoutMinibasketComponent,
        CheckoutMinibasketModalOpenDirective,
        CheckoutPaymentAddressStatusComponent,
        CheckoutPaymentMethodsComponent,
        CheckoutPaymentStatusComponent,
        CheckoutResultComponent,
        CheckoutResultContentBlockComponent,
        CheckoutShippingMethodsComponent,
        CheckoutSummaryStepComponent,
        CheckoutStepComponent,
        CouponRemoveComponent,
        CommonModule,
        DeliveryAddressCmsBlockComponent,
        FormsModule,
        GiftcardsAddedGiftcardComponent,
        IfLoggedInDirective,
        IfNotLoggedInDirective,
        ImageDirective,
        OverlayComponent,
        PriceComponent,
        ReceiveOrderCmsBlockComponent,
        StickyFreeComponent,
        StoreCreditRemoveComponent,
        TranslatePipe,
        ValuesPipe
    ],
    providers: [
        {
            provide: CheckoutFormComponentDef,
            useExisting: CheckoutFormComponent
        }
    ]
})
export class CheckoutFormComponent extends CheckoutFormComponentDef implements OnInit, AfterViewInit, OnDestroy {
    /**
     * If store pickup method is available
     * @type {boolean}
     */
    storePickupAvailable?: boolean;

    /**
     * If metapack collection method is available
     * @type {boolean}
     */
    collectionPointsAvailable?: boolean;

    headerHeight!: number;
    stepNames = CHECKOUT_STEPS_NAMES;
    checkoutForMinibasket!: CheckoutFormComponent;
    changePaymentAddress = true;
    removeStatus = new Status();
    showSubscriptionOptIn = false;
    subscriptionChange$ = new Subject<boolean>();
    applePayAvailable = false;

    readonly accountExperimentId = ACCOUNT_STEP_EXPERIMENT_ID;

    /**
     * Selected collection method store name
     * @type {string}
     */
    protected storeName?: string;

    private _deliveryType!: CheckoutDeliveryType;

    protected sessionStorageService = inject(SessionStorageService);
    protected storeCredit = inject(StoreCreditService);
    protected exponeaService = inject(ExponeaService);
    protected multisiteService = inject(MultisiteService);
    protected applePayService = inject(ApplePayService);
    private readonly abService = inject(AbService);
    protected readonly disposableTimeoutService = injectTimeoutService();

    override ngOnInit() {
        super.ngOnInit();

        this.applePayAvailable = this.applePayService.isAvailable && this.applePayService.isSupportedVersion(3);

        this.triggers['custom'].fields = ['custom.gift.message'];

        this.checkoutForMinibasket = this;

        this._deliveryType = this.sessionStorageService.get('deliveryType');
        this.subscriptions.add(this.app.events.on(Checkout.EVENTS.ShippingMethodsUpdate).subscribe(() => this.onShippingMethodsUpdate()));
        this.subscriptions.add(
            merge(this.app.events.on(Checkout.EVENTS.TotalsUpdate), this.basket.basketUpdateSubject).subscribe(() => this.detectChanges())
        );
        this.subscriptions.add(this.customer.model.obs.data.subscribe(() => this.detectChanges()));

        this.onShippingMethodsUpdate();

        if (this.deliveryType === CheckoutDeliveryType.shipping && this.selfCollect) {
            this.selectShipping();
        } else if (this.deliveryType === CheckoutDeliveryType.collection && !this.selfCollect) {
            this.selectCollection();
        }

        this.subscriptions.add(this.app.client.obs.resize.subscribe(() => this.updatePageHeaderHeight()));
        [
            this.saveShippingAddressStatus,
            this.saveShippingMethodStatus,
            this.saveBillingAddressStatus,
            this.savePaymentMethodStatus
        ].forEach(status => {
            this.subscriptions.add(
                status.subscribe(() => {
                    if (!status.busy) {
                        setTimeout(() => this.detectChanges(), 100);
                    }
                })
            );
        });
    }

    override ngAfterViewInit() {
        this.updatePageHeaderHeight();

        // hidden in task OBR-4092
        // @link https://tomandco.atlassian.net/browse/OBR-4092

        // if (this.preselectModeAvailable()) {
        //     if (!this.deliveryTypeSelected) {
        //         this.selectDeliveryType(CheckoutDeliveryType.shipping, false);
        //     }

        //     const shippingMethodStep = this.getStepByName(CHECKOUT_STEPS_NAMES.deliveryMethod);

        //     if (this.isShipping && shippingMethodStep) {
        //         this.goToStep(shippingMethodStep);

        //         return;
        //     }
        // }

        super.setInitStep();
    }

    override ngOnDestroy() {
        super.ngOnDestroy();
        this.passStepsToService(undefined);
    }

    override scrollToStepSection() {
        if (!this.client.isTouchy) {
            if (!this.basket.hasOOSProducts) {
                super.scrollToStepSection();
            } else {
                this.disposableTimeoutService.setTimeout(() => this.scrollToService.scrollTo(0), 100);
            }
        }
    }

    override get hasNonCollectionMethods(): boolean {
        return super.hasNonCollectionMethods || this.data.address.shipping.country_id !== this.app.multisite.current.country;
    }

    get isLoggedIn(): boolean {
        return this.customer.model.isLoggedIn;
    }

    updatePageHeaderHeight(): void {
        this.headerHeight = getElementHeight('header.header') || 0;
        this.detectChanges();
    }

    override _onStepsChange() {
        super._onStepsChange();
        if (this.currentStep?.name === 'account') {
            this.runAccountExperiment();
        }
        this.passStepsToService(this.visibleStepsSequence);
    }

    /**
     * update steps in service
     * @param {Checkout.IStep[]} steps
     */
    passStepsToService(steps: Checkout.IStep[] | undefined): void {
        (<CheckoutServiceDecorator>this.checkout).steps = steps;
    }

    /////////////////////////////////////////////////
    /////
    ///// Delivery type
    /////
    /////////////////////////////////////////////////

    /**
     * Returns if we should display delivery selection -> both types available
     */
    get displayDeliverySelection(): boolean {
        return this.hasCollectionMethods || this.hasNonCollectionMethods;
    }

    get noStatusForDeliverySelection(): boolean {
        return (
            this.displayDeliverySelectionStep ||
            this.displayDeliveryAddressStep ||
            this.displayCollectionStep ||
            (!this.data?.address?.shipping?.id &&
                this.shippingMethodCode !== CheckoutCollectionMethods.metapackCollection &&
                this.shippingMethodCode !== CheckoutCollectionMethods.storePickup)
        );
    }

    get displayStatusTitleForDeliverySelection(): boolean {
        return (this.isShipping || this.isCollection) && !this.displayDeliverySelectionStep && !this.displayAccountStep;
    }

    get displayDeliverySelectionSection(): boolean {
        return this.displayDeliverySelection || this.noStatusForDeliverySelection;
    }

    /**
     * Checks if delivery is selected
     * @returns {boolean}
     */
    get isShipping(): boolean {
        return this.deliveryType === CheckoutDeliveryType.shipping;
    }

    /**
     * Checks if click and collect is selected
     * @returns {boolean}
     */
    get isCollection(): boolean {
        return this.deliveryType === CheckoutDeliveryType.collection;
    }

    get deliveryTypeSelected(): CheckoutDeliveryType | undefined {
        return this.deliveryType;
    }

    /**
     * Sets delivery as selected delivery type
     */
    selectShipping(currentStep = false): void {
        this.selectDeliveryType(CheckoutDeliveryType.shipping, currentStep);
    }

    preferShipping(): void {
        this.selectShipping();

        this.goToStep(CHECKOUT_STEPS_NAMES['deliveryAddress']);
    }

    /**
     * Sets click and collect as selected delivery type
     */
    selectCollection(currentStep = false): void {
        this.selectDeliveryType(CheckoutDeliveryType.collection, currentStep);
    }

    preferCollection(): void {
        this.selectCollection();

        this.goToStep(CHECKOUT_STEPS_NAMES['collection']);
    }

    /**
     * Returns selected delivery type
     */
    protected set deliveryType(type: CheckoutDeliveryType) {
        this._deliveryType = type;
        this.sessionStorageService.set('deliveryType', type);
    }

    /**
     * Sets selected delivery type
     */
    protected get deliveryType(): CheckoutDeliveryType {
        return this._deliveryType;
    }

    /**
     * Select delivery type
     * @param {CheckoutDeliveryType} type
     */
    selectDeliveryType(type: CheckoutDeliveryType | undefined, nextStep = true): void {
        this.deliveryType = type;

        if (type === CheckoutDeliveryType.collection) {
            this.clearShippingAdditionalData();
            this.selfCollect = true;
        } else {
            if (this.selfCollect) {
                this.clearShippingAdditionalData();
            }

            this.selfCollect = false;
        }

        if (nextStep) {
            setTimeout(() => {
                this.goToNextStep();
                if (type === CheckoutDeliveryType.shipping) {
                    const currentStep = this.currentStep;
                    if (currentStep && !this.client.isTouchy) {
                        setTimeout(() => {
                            this.scrollToService.scrollTo(currentStep.domElement, { offset: this.headerHeight + 20 });
                        }, 100);
                    }
                }
                this.detectChanges();
            }, 100);
        }

        this.detectChanges();
    }

    /**
     * Updated delivery type
     * if click and collect selected but no method is available
     * we have to unselect it
     */
    updateDeliveryType(): void {
        if (!this.hasCollectionMethods) {
            if (this.selfCollect) {
                this.selfCollect = false;
                this.clearShippingAdditionalData();
            }

            this.selectShipping();
        } else if (!this.hasNonCollectionMethods) {
            if (!this.selfCollect) {
                this.selfCollect = true;
                this.clearShippingAdditionalData();
            }

            this.selectCollection();
        }

        this.detectChanges();
    }

    onDeliveryStepChange = () => {
        this.selectDeliveryType(undefined, false);
    };

    /////////////////////////////////////////////////
    /////
    ///// Shipping methods
    /////
    /////////////////////////////////////////////////

    /**
     * On shipping methods update callback
     */
    protected onShippingMethodsUpdate(): void {
        if (
            this.currentStepName &&
            this.currentStepName !== this.stepNames['deliveryAddress'] &&
            this.currentStepName !== this.stepNames['account']
        ) {
            this.updateDeliveryType();
        }

        const storePickup = this.shippingMethodByCode(CheckoutCollectionMethods.storePickup);
        this.storePickupAvailable = !!storePickup;

        const collectionPoints = this.shippingMethodByCode(CheckoutCollectionMethods.metapackCollection);
        this.collectionPointsAvailable = !!collectionPoints;
    }

    handleSelectedCollectionData(data: StoreDecorator | MetapackCollectionPoint): void {
        this.storeName = data.data?.store_name;
    }

    override _saveShippingMethod(
        force?: boolean,
        onSuccess?: IRequestCallback,
        onError?: IRequestCallback,
        status?: Status,
        data?: Checkout.IQuoteDataShipping
    ) {
        if (data?.additionalData?.['store_name'] && data.method && this.isCollectionMethod(data.method)) {
            data.additionalData['store_name'] = this.storeName;
        }

        return super._saveShippingMethod(force, onSuccess, onError, status, data);
    }

    protected override getSaveShippingMethodData(data: Checkout.IQuoteDataShipping): Checkout.ISaveShippingMethodData {
        return omit(super.getSaveShippingMethodData(data), 'additionalData.store_name'); // we only have methodDetails for internal processing, we need to take from event to include patches
    }

    /////////////////////////////////////////////////
    /////
    ///// Others
    /////
    /////////////////////////////////////////////////

    /**
     * Clears shipping additional data
     */
    clearShippingAdditionalData(): void {
        if (this.data.shipping.additionalData) {
            this.data.shipping.additionalData = {
                store_id: null
            };
        }
    }

    getAdditionalShippingData(): Checkout.IQuoteDataShippingAdditionalData | undefined {
        return this.data.shipping.additionalData;
    }

    /**
     * we do set init step in after view init
     */
    override setInitStep() {}

    /**
     * Check if we can display preselect mode -> all data filled except delivery method
     * @returns {boolean}
     */
    preselectModeAvailable(): boolean {
        if (
            !this.data ||
            !this.customer.model.isLoggedIn ||
            !this.customer.model.cards?.length ||
            this.deliveryType === CheckoutDeliveryType.collection ||
            !this.nonCollectionShippingMethods?.length ||
            this.isVirtual
        ) {
            return false;
        }

        const shippingAddressId = this.shippingAddress?.customer_address_id;

        if (!shippingAddressId) {
            return false;
        }

        if (!this.isBillingToShipping) {
            const billingAddressId = this.billingAddress?.customer_address_id;

            if (!billingAddressId) {
                return false;
            }
        }

        const paymentMethod = this.data.payment?.method;

        if (!paymentMethod || !find(this.customer.model.cards, card => card.code === paymentMethod)) {
            return false;
        }

        return true;
    }

    /**
     * Checks if delivery method step is valid
     * @returns {boolean}
     */
    get deliveryMethodStepValid(): boolean {
        return !!this.getStepByName(CHECKOUT_STEPS_NAMES['deliveryMethod'])?.valid;
    }

    /**
     * Checks if delivery step is valid
     * @returns {boolean}
     */
    get deliveryStepValid(): boolean {
        if (this.displayDeliverySelection) {
            return !!this.getStepByName(CHECKOUT_STEPS_NAMES['deliverySelection'])?.valid;
        } else {
            if (this.isShipping) {
                return !!this.getStepByName(CHECKOUT_STEPS_NAMES['deliveryAddress'])?.valid;
            }
        }

        return false;
    }

    get displayAccountStep(): boolean {
        return [CHECKOUT_STEPS_NAMES['account']].indexOf(this.currentStep?.name) !== -1;
    }

    /**
     * Returns if we should display payment selection -> both types available
     */
    get displayPaymentStep(): boolean {
        return [CHECKOUT_STEPS_NAMES['payment']].indexOf(this.currentStep?.name) !== -1;
    }

    get displayDeliveryAddressStep(): boolean {
        return [CHECKOUT_STEPS_NAMES['deliveryAddress']].indexOf(this.currentStep?.name) !== -1;
    }

    get displayCollectionStep(): boolean {
        return [CHECKOUT_STEPS_NAMES['collection']].indexOf(this.currentStep?.name) !== -1;
    }

    get displayDeliveryMethodStep(): boolean {
        return [CHECKOUT_STEPS_NAMES['deliveryMethod']].indexOf(this.currentStep?.name) !== -1;
    }

    get displayDeliverySelectionStep(): boolean {
        return [CHECKOUT_STEPS_NAMES['deliverySelection']].indexOf(this.currentStep?.name) !== -1;
    }

    get displayPaymentSelectionStep(): boolean {
        return [CHECKOUT_STEPS_NAMES['payment']].indexOf(this.currentStep?.name) !== -1;
    }

    get displayDeliveryStatus(): boolean {
        return (
            [
                CHECKOUT_STEPS_NAMES['account'],
                CHECKOUT_STEPS_NAMES['deliverySelection'],
                CHECKOUT_STEPS_NAMES['deliveryAddress'],
                CHECKOUT_STEPS_NAMES['collection']
            ].indexOf(this.currentStep?.name) === -1
        );
    }

    get displayPaymentStatus(): boolean {
        return (
            [
                CHECKOUT_STEPS_NAMES['account'],
                CHECKOUT_STEPS_NAMES['deliverySelection'],
                CHECKOUT_STEPS_NAMES['deliveryAddress'],
                CHECKOUT_STEPS_NAMES['collection'],
                CHECKOUT_STEPS_NAMES['deliveryMethod']
            ].indexOf(this.currentStep?.name) === -1
        );
    }

    /**
     * Returns if payment step is valid to display status and summary
     * if only one field - security code for saved card - is invalid we treat payment step as finished for presentation purposes
     * @returns {boolean}
     */
    isPaymentStepValidForDisplay(): boolean {
        const step = this.getStepByName(CHECKOUT_STEPS_NAMES['payment']);
        if (step) {
            if (this.paymentCard) {
                const code = this.paymentCard.code;
                const errors = flatten(step.forms.map(form => this.getErrorsFromForm(form))).filter(i => {
                    if (!i || (i === 'address' && (this.isBillingToShipping || this.data?.address?.billing?.customer_address_id))) {
                        return undefined;
                    } else {
                        return i;
                    }
                });
                if (errors.length === 1 && errors[0] === code) {
                    return true;
                }
                return !errors?.length;
            } else {
                return step.valid;
            }
        }
        return true;
    }

    /**
     * Returns fields ids with error
     * @param {NgForm | FormGroup} form
     * @returns {string[]}
     */
    protected getErrorsFromForm(form: NgForm | FormGroup): string[] {
        const controls = form.controls as Record<string, FormControl | FormGroup>;
        const controlsKeys = Object.keys(controls);
        if (controlsKeys.length) {
            return controlsKeys
                .map(i => {
                    const control = controls[i];
                    return control && !control.valid ? i : undefined;
                })
                .filter(i => !!i);
        }
        return [];
    }

    /**
     * On check step failure callback
     * @param {Checkout.IStep} firstInvalid
     * @param {boolean} updateForms
     */
    protected override onCheckStepFailure(firstInvalid: Checkout.IStep, updateForms: boolean): void {
        if (firstInvalid.name === CHECKOUT_STEPS_NAMES['payment'] && this.isPaymentStepValidForDisplay()) {
            if (updateForms) {
                firstInvalid.markSubmitted(firstInvalid.isCurrent);
                if (!firstInvalid.isCurrent) {
                    this.app.events.broadcast(SAGEPAY_PI_SAVED_CARD_STATUS_DISPLAY_SUBMITTED);
                }
            }
            return;
        }
        super.onCheckStepFailure(firstInvalid, updateForms);
    }

    /**
     * On submit success callback
     * we clear delivery type flag
     */
    override redirectToSuccess(): void {
        this.sessionStorageService.remove('deliveryType');
        super.redirectToSuccess();
    }

    get blockSubmitButtons(): boolean {
        return this.saveShippingAddressStatus.busy || this.saveShippingMethodStatus.busy;
    }

    get storeCreditEnabled(): boolean {
        return this.storeCredit.enabled;
    }

    goToPayment(): void {
        const paymentStep = this.getStepByName(CHECKOUT_STEPS_NAMES['payment']);

        if (paymentStep) {
            this.goToStep(paymentStep);
        }
    }

    get displayPaymentAddressStatus(): boolean {
        return !this.changePaymentAddress;
    }

    get displayGoToPayment(): boolean {
        if (this.currentStepName === CHECKOUT_STEPS_NAMES['deliveryMethod']) {
            return this.deliveryMethodStepValid;
        }
        return false;
    }

    get displaySummaryStep(): boolean {
        const stepName = this.currentStep?.name;

        if (!this.currentStep?.isConfirmation) {
            if (
                [
                    CHECKOUT_STEPS_NAMES['deliveryAddress'],
                    CHECKOUT_STEPS_NAMES['collection'],
                    CHECKOUT_STEPS_NAMES['deliverySelection'],
                    CHECKOUT_STEPS_NAMES['deliveryMethod'],
                    CHECKOUT_STEPS_NAMES['account']
                ].indexOf(stepName) !== -1
            ) {
                return false;
            }

            if ([CHECKOUT_STEPS_NAMES['payment']].indexOf(stepName) !== -1) {
                return true;
            }

            if (this.isCheckoutValid) {
                return true;
            } else {
                const invalidSteps = Object.values(this.steps).filter(i => !i.valid);

                if (invalidSteps.length === 1 && invalidSteps[0].name === CHECKOUT_STEPS_NAMES['payment']) {
                    return this.isPaymentStepValidForDisplay();
                }
            }
        }

        return false;
    }

    trackByGiftcard(index: number, giftcard: Giftcards.IAddedGiftcardData) {
        return giftcard.code;
    }

    protected override getToCurrentStepScrollOptions(): IScrollToOptions {
        return {
            offset: this.headerHeight + 20
        };
    }

    override get basket(): BasketDecorator {
        return <BasketDecorator>this.app.customer.basket;
    }

    setNewsletter(value: boolean, source: string) {
        this.data.newsletter = value;
        this.data.source = source;

        // const email = this.email;

        // OBR-3737 - send only one event
        // if (email) {
        //     this.exponeaService.identifyCustomer(email);
        //     this.exponeaService.onNewsletterConsentChange(value, email, source);
        // }

        this.subscriptionChange$.next(value);

        this.detectChanges();
    }

    dataChangeHandle(): void {
        this.detectChanges();
    }

    showSubscriptionOptInHandle(): void {
        this.detectChanges();
    }

    override submitWithData(
        data: Checkout.ISubmissionData,
        onSuccess?: IRequestCallbackFunction,
        onError?: IRequestCallbackFunction,
        force = false
    ) {
        const expressPaymentMethods = [Braintree.Methods.ApplePay, Braintree.Methods.GooglePay] as string[];

        if (expressPaymentMethods.indexOf(`${data.paymentMethod?.method}`) !== -1) {
            // identify customer for express payment methods
            if (!this.customer.model.isLoggedIn && data?.email) {
                this.exponeaService.identifyCustomer(data.email);
            }
        }

        return super.submitWithData(data, onSuccess, onError, force);
    }

    private runAccountExperiment = debounce(
        () => {
            if (this.app.isPlatformBrowser && window['convert']?.data?.experiments) {
                const experiment = this.abService.getExpById(this.accountExperimentId);
                if (experiment?.key) {
                    window[`runExperiment${experiment.key}`] = 1;
                    window['_conv_q'] = window['_conv_q'] || [];
                    window['_conv_q'].push(['executeExperiment', `${experiment.key}`]);
                    this.app.logger.log('executeExperiment', experiment.key);
                }
            }
        },
        300,
        { leading: true, trailing: false }
    );
}

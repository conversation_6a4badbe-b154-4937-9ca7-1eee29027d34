<div class="flex flex-middle">
    <div class="p1 w-1">
        @if (isCardPayment && sagepayCardType) {
        <span class="payment-method-logo"
              [ngClass]="'payment-method-logo--' + sagepayCardType"
              style="width: 100%;"></span>
        }
        @if (!isCardPayment && paymentMethod) {
        @switch (paymentMethod.code) {
        @case ('braintree_applepay') {
        <span class="payment-method-logo payment-method-logo--apple-pay"
              style="width: 100%;"></span>
        }
        @case ('paypal_express') {
        <span class="payment-method-logo payment-method-logo--paypal"
              style="width: 100%;"></span>
        }
        }
        <span class="payment-method-logo payment-method-logo--klarna"
              style="width: 100%;"
              *ifKlarnaPayment="$any(paymentMethod.code)"></span>
        }
    </div>
    <div class="m-l-2 m-l-4-s m-l-4-m">
        <p [sizeClass]="'SM:fs-4, XL:p1'">
            {{title}}
        </p>
    </div>
</div>
@if (card) {
<div class="flex">
    <div class="w-1"></div>
    <div class="w-11 m-l-2 m-l-4-s m-l-4-m">
        @if (card.data.last_four) {
        <div class="p1 col-13">
            Ending **** <span data-cs-mask>{{card.data.last_four}}</span>
        </div>
        }
        @if (card.isCcvRequired) {
        <sagepay-pi-saved-card-status [card]="card" />
        }
    </div>
</div>
}
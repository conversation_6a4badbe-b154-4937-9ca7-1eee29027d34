@if (!steps?.account?.isCurrent && (client.isS || client.isM)) {
<div class="pos-relative z-3">
    <!-- not logged in / out of stock products alert -->
    <ng-container *ifNotLoggedIn>
        @if (basket.hasOOSProducts && steps?.deliverySelection?.isCurrent) {
        <basket-oos-items-warning [status]="removeStatus" />
        }
    </ng-container>

    <!-- logged in / out of stock products alert -->
    <ng-container *ifLoggedIn>
        @if (basket.hasOOSProducts && steps?.payment?.isCurrent) {
        <basket-oos-items-warning [status]="removeStatus" />
        }
    </ng-container>

    <!-- bag summary -->
    <div class="wrap m-t-5">
        <p class="fs-3">{{'Bag summary' | translate}}</p>

        <div class="m-t-2 bg-col-5 m-t-4 p-a flex flex-justify-between flex-middle b-radius-7">
            <p>
                <i class="svg-icon--delivery"
                   aria-hidden="true"></i>
                <span class="inline-block m-l fs-4">{{'Subtotal:' | translate}}</span>

                <price class="price fs-4 fw-bold m-l-2"
                       [value]="basket.subtotal"
                       [precision]="2" />

                @if (basket?.qty) {
                <span class="fs-4 col-13 m-l-1"
                      [ngPlural]="basket?.qty">
                    <ng-template ngPluralCase="=1">{{'(' + basket?.qty + ' Item' + ')'}}</ng-template>
                    <ng-template ngPluralCase="other">{{'(' + basket?.qty + ' Items' + ')'}}</ng-template>
                </span>
                }
            </p>

            <p class="fs-3 fw-bold p-4 cursor-pointer"
               checkoutMinibasketModalOpen
               [modalData]="checkoutForMinibasket">
                <span class="p-a-2">{{'View bag' | translate}}</span>
                <i class="icon-chevron-down"
                   aria-hidden="true"></i>
            </p>
        </div>
    </div>
</div>
}

<form class="pos-relative z-1 block bg-col-5-x bg-col-5-l p-t-7 p-r-1-l"
      method="POST"
      style="min-height:600px;"
      name="checkout"
      #form="ngForm">
    <div class="wrap flex flex-justify-center center">
        <div class="c-5-set c-12-set-m c-12-set-s">
            <div class="bg-col-w p-l-10 p-r-10 p-t-9 p-b-9 p-a-0-s p-a-0-m left">
                <checkout-step [name]="stepNames.account"
                               [stepTitle]="'Email' | translate"
                               [statusTitle]="'Email' | translate"
                               [displayStatusTitle]="!displayAccountStep"
                               [order]="10"
                               [showLogout]="isLoggedIn">

                    <ng-container ngProjectAs="[step-body]">
                        <checkout-account *abIf="'('+ accountExperimentId + ') A'"
                                          class="w-12"
                                          [(showSubscriptionOptIn)]="showSubscriptionOptIn"
                                          (showSubscriptionOptInChange)="showSubscriptionOptInHandle()" />
                        <checkout-account-variant-b *abIf="'(' + accountExperimentId + ') B'"
                                                    class="w-12"
                                                    [(showSubscriptionOptIn)]="showSubscriptionOptIn"
                                                    (showSubscriptionOptInChange)="showSubscriptionOptInHandle()" />
                    </ng-container>

                    <ng-container ngProjectAs="[step-status]">
                        <checkout-account-status class="w-12" />
                    </ng-container>
                </checkout-step>

                @if (!isVirtual) {
                @if (displayDeliverySelectionSection) {
                <checkout-step class="block m-t"
                               [name]="stepNames.deliverySelection"
                               [statusTitle]="'Delivery'"
                               [stepTitle]="'Delivery'"
                               [stepGroup]="'delivery'"
                               [noStatus]="noStatusForDeliverySelection"
                               [displayStatusTitle]="displayStatusTitleForDeliverySelection"
                               [order]="20"
                               [onStepChange]="onDeliveryStepChange"
                               [changeStep]="isCollection ? 'collection' : 'deliveryAddress'">
                    <ng-container ngProjectAs="[step-body]">
                        <receive-order-cmsblock />

                        <checkout-delivery-selection class="w-12" />
                    </ng-container>

                    <ng-container ngProjectAs="[step-status]">
                        <checkout-delivery-status class="w-12" />
                    </ng-container>

                    <ng-container ngProjectAs="[step-save-status]">
                        @if (saveShippingAddressStatus.busy) {
                        <span class="fs-1 col-2 m-l">{{'saving...' | translate}}</span>
                        }
                    </ng-container>

                    <ng-container ngProjectAs="[step-title]">
                        @if (isShipping && hasCollectionMethods) {
                        <button class="inline-flex flex-middle link"
                                type="button"
                                (click)="preferCollection()"><i class="m-r-1 fs-6 lh-0 svg-icon--click-and-collect"></i>
                            <span class="underline p2">{{'Prefer to collect' | translate}}</span></button>
                        }

                        @if (isCollection) {
                        <button class="inline-flex flex-middle link"
                                type="button"
                                (click)="preferShipping()">
                            <i class="m-r-1 m-t-1 lh-0 icon-delivery"
                               [style.min-width.px]="30"
                               [style.font-size.em]="0.95"></i>
                            <span class="underline p2">{{'Prefer to deliver' | translate}}</span>
                        </button>
                        }
                    </ng-container>
                </checkout-step>
                }
                @if (isShipping) {
                <checkout-step [ngClass]="{'block m-t': !displayDeliverySelection}"
                               [name]="stepNames.deliveryAddress"
                               [statusTitle]="displayDeliverySelection ? '' : 'Delivery'"
                               [stepTitle]="'Delivery'"
                               [stepGroup]="'delivery'"
                               [noStatus]="displayDeliverySelection"
                               [displayStatusTitle]="displayDeliveryStatus"
                               [refreshStatuses]="[saveShippingAddressStatus]"
                               [order]="30">

                    <ng-container ngProjectAs="[step-body]">
                        <delivery-address-cmsblock />

                        <!-- if store pickup is not an option -->
                        <checkout-address class="w-12 m-t-2 m-t-6-s"
                                          [data]="data.address.shipping" />

                        <button class="w-12 button m-t"
                                id="continue-to-payment"
                                type="button"
                                [action]="saveShippingAddressStatus"
                                (click)="goToNextStep()">
                            <span>
                                {{'Continue' | translate}}
                            </span>
                        </button>
                    </ng-container>

                    <ng-container ngProjectAs="[step-status]">
                        <checkout-delivery-status class="w-12" />
                    </ng-container>

                    <ng-container ngProjectAs="[step-title]">
                        @if (isShipping && hasCollectionMethods) {
                        <button class="inline-flex flex-middle link"
                                type="button"
                                (click)="selectCollection(true)"><i
                               class="m-r-1 fs-6 lh-0 svg-icon--click-and-collect"></i>
                            <span class="underline p2">{{'Prefer to collect' | translate}}</span></button>
                        }

                        @if (isCollection) {
                        <button class="inline-flex flex-middle link"
                                type="button"
                                (click)="selectShipping(true)">
                            <i class="m-r-1 m-t-1 lh-0 icon-delivery"
                               [style.min-width.px]="30"
                               [style.font-size.em]="0.95"></i>
                            <span class="underline p2">{{'Prefer to deliver' | translate}}</span>
                        </button>
                        }
                    </ng-container>
                </checkout-step>

                <checkout-step [stepTitle]="'Delivery' | translate"
                               [name]="stepNames.deliveryMethod"
                               [refreshStatuses]="[saveShippingAddressStatus]"
                               [blockStatuses]="[saveShippingAddressStatus]"
                               [stepGroup]="'delivery'"
                               [noStatus]="noStatusForDeliverySelection"
                               [order]="40">
                    <ng-container ngProjectAs="[step-body]">
                        <!-- delivery method -->
                        <h3 class="m-t">
                            <span class="p1">{{'Delivery method' | translate}}</span>
                            <span class="fs-1 col-2 m-l">
                                @if (saveShippingAddressStatus.busy || saveShippingMethodStatus.busy) {
                                {{'updating...' | translate}}
                                }
                                @if (saveShippingMethodStatus.busy) {
                                {{'saving...' | translate}}
                                }
                            </span>
                        </h3>

                        <checkout-shipping-methods class="w-12 m-t-2 m-t-6-s" />

                        @if (!displaySummaryStep && ((!displayPaymentSelectionStep && !paymentMethod) ||
                        displayGoToPayment) && deliveryMethodStepValid) {
                        <button class="button w-12 m-t-3"
                                [class.inactive]="!steps.delivery?.valid"
                                type="button"
                                [action]="saveShippingMethodStatus"
                                (click)="goToNextStep()">
                            <span>
                                {{'Continue to payment' | translate}}
                            </span>
                        </button>
                        } @else {
                        <button class="button w-12 m-t-3"
                                [class.disabled]="nonCollectionShippingMethods.length === 0"
                                type="button"
                                (click)="goToNextStep()">
                            <span>
                                {{'Select delivery' | translate}}
                            </span>
                        </button>
                        }
                    </ng-container>
                    <ng-container ngProjectAs="[step-status]">
                        <checkout-delivery-method-status class="w-12" />
                    </ng-container>
                </checkout-step>
                }

                @if (isCollection) {
                <checkout-step [ngClass]="{'block m-t': displayPaymentSelectionStep}"
                               [name]="stepNames.collection"
                               [statusTitle]="displayDeliverySelection ? '' : 'Delivery'"
                               [stepTitle]="'Delivery'"
                               [stepGroup]="'delivery'"
                               [noStatus]="true"
                               [displayStatusTitle]="displayDeliveryStatus"
                               [refreshStatuses]="[saveShippingMethodStatus]"
                               [order]="50">

                    <ng-container ngProjectAs="[step-body]">
                        <checkout-collection class="w-12 m-t-2 p-a-2 p-a-4-m p-a-4-s b-radius-7 bg-col-5"
                                             (selectedCollectionData)="handleSelectedCollectionData($event)" />
                    </ng-container>

                    <ng-container ngProjectAs="[step-status]">
                        <checkout-delivery-status class="block" />
                    </ng-container>
                </checkout-step>
                }
                }

                <checkout-step class="block m-t"
                               [refreshStatuses]="[saveShippingAddressStatus, saveShippingMethodStatus]"
                               [blockStatuses]="[saveShippingAddressStatus, saveShippingMethodStatus]"
                               [name]="stepNames.payment"
                               [noStatus]="!displayPaymentStatus"
                               [displayStatusTitle]="displayPaymentStatus && !isShipping && !isCollection && !displayPaymentSelectionStep"
                               [statusTitle]="'Payment' | translate"
                               [stepTitle]="'Payment' | translate"
                               [order]="60">
                    <ng-container ngProjectAs="[step-body]">
                        <h3 class="m-t">
                            <span class="p1">{{'Payment' | translate}}</span>

                            <span class="fs-1 col-2 m-l">
                                @if (saveBillingAddressStatus.busy || savePaymentMethodStatus.busy) {
                                {{'updating...' | translate}}
                                }
                                @if (savePaymentMethodStatus.busy) {
                                {{'saving...' | translate}}
                                }
                            </span>
                        </h3>

                        <checkout-payment-methods class="w-12 m-t-2" />

                        <!-- regular addresses -->
                        @if (displayPaymentSelectionStep) {
                        <h3 class="m-t p1">{{'Billing address' | translate}}</h3>

                        <checkout-address class="w-12 m-t-2"
                                          [class.ng-hide]="displayPaymentAddressStatus"
                                          [data]="data.address.billing"
                                          [isBilling]="true" />

                        <checkout-payment-address-status [class.ng-hide]="!displayPaymentAddressStatus" />
                        }
                    </ng-container>

                    <ng-container ngProjectAs="[step-status]">
                        <checkout-payment-status class="w-12" />
                    </ng-container>
                </checkout-step>

                <checkout-summary-step [name]="stepNames.summary">
                    <div class="m-t m-b">
                        <p class="p1 m-b-2">
                            {{'Apply a promotional code% or gift card'|translate:(storeCreditEnabled ? ',credit':'')}}
                        </p>
                        <basket-discount-buttons class="w-12"
                                                 context="checkout" />
                    </div>

                    <!-- totals -->
                    <ul class="m-t"
                        [ngClass]="showSubscriptionOptIn ? 'm-b-2' : 'm-b'">
                        <li class="flex flex-justify-between m-b-1 p1 col-12">
                            <div>{{'Subtotal' | translate}}</div>
                            <price [value]="basket.totals?.subtotal"
                                   [precision]="2" />
                        </li>
                        @if (shippingMethod) {
                        <li class="flex flex-justify-between m-b-1 p1 col-12">
                            <div>{{shippingMethod?.title}}</div>
                            <price [value]="basket.totals?.shipping"
                                   [precision]="2" />
                        </li>
                        }

                        @if (basket.data?.totals?.credits) {
                        <li class="m-b-1 p1 col-12">
                            <store-credit-remove class="block"
                                                 [isCheckout]="true" />
                        </li>
                        }

                        @for (giftcard of basket.data.giftcards | values; track trackByGiftcard) {
                        <li class="m-b-1 p1 col-12">
                            <giftcards-added-giftcard class="block"
                                                      [giftcard]="giftcard"
                                                      [useCheckoutPath]="true" />
                        </li>
                        }
                        @if (basket.discount) {
                        <li class="m-b-1 p1 col-12">
                            <coupon-remove class="block" />
                        </li>
                        }

                        <li class="flex flex-justify-between b-t b-col-4 p-t-2 m-t-2 fs-6 fw-bold s1 col-1">
                            <div>{{'Order total' | translate}}</div>
                            <price [value]="basket.totals?.grand_total || 0"
                                   [precision]="2" />
                        </li>
                    </ul>
                    <div [ngClass]="{'pe-none': blockSubmitButtons}">
                        <!-- place order -->
                        <button class="button w-12"
                                [ngClass]="{'visually-hidden': paymentMethodCode === 'braintree_applepay', 'busy': saveShippingMethodStatus.busy}"
                                [class.is-disabled]="basket.hasOOSProducts"
                                [action]="status"
                                (click)="submit()">{{'Place order' | translate}}</button>

                        @if (!!paymentMethodByCode('braintree_applepay') && applePayAvailable) {
                        <braintree-apple-pay class="w-12"
                                             [ngClass]="{'visually-hidden': paymentMethodCode !== 'braintree_applepay'}" />
                        }

                        <checkout-result-content-block class="block">
                            <checkout-result class="m-t-1 block"
                                             [status]="status"
                                             success="Data saved... redirecting..." />
                        </checkout-result-content-block>
                    </div>
                </checkout-summary-step>

                <!-- verification (iframe)  -->
                <checkout-confirmation />
            </div>
            <div id="checkout-sticky-cutout"></div>
            @if (client.sizeId > 1) {
            <div class="m-t-7 m-b-7">
                <p class="p1"><span class="fw-bold m-r-1">{{(app.data.reviews?.merchant_global_summary?.rating_summary /
                        20) | number : '1.1-1'}}</span>
                    <span class="fw-bold">{{'out of 5 rating' | translate}}</span>&nbsp;<span class="col-13">
                        {{'based on' | translate}}</span>&nbsp;
                    <span class="col-13">{{app.data.reviews?.merchant_global_summary?.reviews_count}}</span>&nbsp;
                    <span class="col-13">{{'reviews' | translate}}</span>
                </p>
                <!-- logo-->
                <img class="m-t-2"
                     style="width: 4.9em; height: 1.75em"
                     [image]="'/assets/images/trustpilot-logo.png'">
            </div>
            }
        </div>

        <!-- side product summary -->
        @if (checkoutForMinibasket && client.sizeId > 1) {
        <aside class="c-3-set m-r-0">
            <sticky-free class="basket-sticky"
                         [stickyId]="'checkout-view'"
                         [stickyOffset]="'page-header-checkout'"
                         [stickyOff]="app.client.isCms"
                         [order]="2">
                <checkout-minibasket [checkoutFormComponent]="checkoutForMinibasket" />
            </sticky-free>
        </aside>
        }
    </div>
</form>

<!-- overlay -->
@if (client.sizeId > 1) {
<div class="pos-fixed fill bg-col-5 pe-none"
     style="z-index:-10"></div>
} @else {
<div class="pos-relative z-2">
    <ng-container *ifNotLoggedIn>
        <overlay [visible]="basket.hasOOSProducts && steps?.deliverySelection?.isCurrent" />
    </ng-container>

    <ng-container *ifLoggedIn>
        <overlay [visible]="basket.hasOOSProducts && steps?.payment?.isCurrent" />
    </ng-container>
</div>
}
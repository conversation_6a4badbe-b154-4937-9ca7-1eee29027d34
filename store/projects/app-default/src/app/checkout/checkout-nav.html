@if (steps?.length) {
<ul class="flex flex-middle w-12">
    @for (step of steps; track step.name) {
    @if (!step.isConfirmation) {
    <li class="flex flex-middle"
        [ngStyle]="{'width': getStepWidth(!$first && !$last)}">
        @if (!$first) {
        <span class="checkout-step-line flex-grow"
              style="height: 1px;"
              [ngClass]="currentStepIndex >= $index ? 'bg-col-21' :'bg-col-4'"></span>
        }
        <button class="checkout-step-bullet pos-relative z-1 b-radius-max"
                [ngClass]="currentStepIndex >= $index ? 'bg-col-21' :'bg-col-4'"
                style="height: 9px; width: 9px;"
                type="button"
                (click)="step.goTo()">
            <span class="pos-absolute top-100 p-t-1 p-t-2-s p-t-3-m fs-4"
                  [ngStyle]="{'transform': $first ? 'translateX(-21%)' : $last ? 'translateX(-88%)' : 'translateX(-50%)'}"
                  [ngClass]="currentStepIndex >= $index ? 'col-21' : 'col-13'">
                {{step.stepTitle}}
            </span>
        </button>
        @if (!$last) {
        <span class="checkout-step-line flex-grow"
              style="height: 1px;"
              [ngClass]="currentStepIndex > $index ? 'bg-col-21' :'bg-col-4'"></span>
        }
    </li>
    }
    }
</ul>
}
<div class="flex flex-middle flex-justify-center bg-col-5">
    <div class="bg-col-w m-t-3 m-b-3 p-t-5 p-l-8 p-r-8 _custom-width">
        <!-- page title -->
        <div class="w-5 w-12-m w-12-s">
            <h1 class="h2"
                data-cs-mask
                sl-header>{{'Thanks for your order' | translate}} {{customer.model.data.firstname ||
                data.billing.firstname}}!</h1>
            <div
                 class="bg-col-5 m-t-3 m-b-5-m m-b-5-s p-t-3 p-t-4-m p-t-4-s p-b-3 p-b-4-m p-b-4-s p-l-5 p-l-7-m p-l-7-s p-r-5 p-r-7-m p-r-7-s b-radius-20 _box-width">
                <p class="h2">{{'Order processing' | translate}}</p>
                <p class="p1 m-t-1">{{'Order number' | translate}}: <span class="s2">#{{data.number}}</span></p>
                <p class="p1 m-t-1">{{'We\'ve got your order and you\'ll receive a confirmation email shortly and
                    notified with your tracking details when its on its way.' | translate}}</p>
                <div class="flex m-t-3">
                    <div class="_custom-gray-bar bg-col-12"></div>
                    <div class="_custom-bar bg-col-w"></div>
                </div>
            </div>

            @if (canRegister) {
            <div class="bg-col-34 m-t-3 p-t-3 p-b-3 p-l-5 p-r-5 b-radius-20 _box-width"
                 *ifSize="'XL'">

                @if (!showForm) {
                <p class="s1 no-wrap">{{'You\'re new here' | translate}}</p>
                <p class="p2">{{'Create an account and enjoy early access to sales and new collections, quicker
                    checkout, and more.' |
                    translate}}</p>
                <button class="button w-12 m-t-2"
                        type="button"
                        sl-button="account.create"
                        [action]="status"
                        (click)="openForm()">
                    <span>
                        {{'Create an account' | translate}}
                    </span>
                </button>
                }
                @else {
                <checkout-success-content-block />

                <form name="create_account_order-conf"
                      method="POST"
                      #form="ngForm">
                    <input-wrap class="block">
                        <input class="input"
                               type="password"
                               name="password"
                               data-cs-mask
                               [muiInput]="'Create a password'"
                               placeholder="Enter password"
                               [(ngModel)]="registrationData.password"
                               required
                               sl-input="password" />
                    </input-wrap>

                    @if (showSubscriptionOptIn) {
                    <checkout-success-newsletter [email]="data.customer.email"
                                                 [source]="'checkout_success'" />
                    }


                    <div class="pos-relative m-t-3">
                        <button class="button w-12"
                                type="button"
                                sl-button="account.create"
                                [action]="status"
                                (click)="register()">
                            <span>
                                {{'Create an account' | translate}}
                            </span>
                        </button>
                        <result class="block m-t-2"
                                [status]="status" />
                    </div>
                </form>
                }
            </div>
            }
            @if (isRegistered) {
            <div class="bg-col-34 m-t-3 p-t-3 p-b-3 p-l-5 p-r-5 b-radius-20 _box-width"
                 *ifSize="'XL'">

                <p class="s1">{{'You\'re in!' | translate}}</p>
                <p class="p2">
                    {{'To say thanks for creating an account, we\'ve credited your account with £5 to spend on your next
                    order and more account benefits.' | translate}}</p>

                <a class="w-12 button m-t-3"
                   [routerLink]="'/account'"><span>{{'View my account' | translate}}</span></a>
            </div>
            }

            <div class="bg-col-5 m-t-3 m-t-5-m m-t-5-s p-t-3 p-t-4-m p-t-4-s p-b-3 p-b-4-m p-b-4-s p-l-5 p-l-7-m p-l-7-s p-r-5 p-r-7-m p-r-7-s b-radius-20 _box-width"
                 *ifSize="'XL'">
                <p class="h3 m-b-1">{{'Need any help with your order?' | translate}}</p>
                <p class="p2">
                    {{'Our Customer Service team is here to help! For any questions or support contact us' | translate}}
                    <href [hrefClass]="'underline'"
                          [link]="'https://oliver-bonas.elevio.help'">here</href>.
                </p>
            </div>
            @if (!canRegister && !showForm) {
            <a class="w-12 button m-t-2 _box-width"
               [routerLink]="'/account'"
               *ifSize="'XL'"><span>{{'View my account' | translate}}</span></a>
            }
        </div>
        <div class="w-7 w-12-m w-12-s">

            @if (data.sccp_payment_reference) {
            <sagepay-cloud-payment-renew-payment class="block"
                                                 [successData]="data" />
            }

            @if (data?.shipping) {
            <div class="w-6">
                <p class="s1 m-b-1">{{'Shipping address' | translate}}</p>
                <p class="s2"
                   data-cs-mask>{{data.shipping.firstname}} {{data.shipping?.lastname}}</p>
                <p class="p2 m-r-8"
                   data-cs-mask>{{data.shipping?.street_1}} {{data.shipping?.street_2}}</p>
                <p class="p2"
                   data-cs-mask>{{data.shipping.postcode}} {{data.shipping.city}}</p>
                <p class="p2"
                   data-cs-mask>{{data.shipping.region}}</p>
                <p class="p2"
                   data-cs-mask>{{data.shipping.country_id}}</p>
                <p class="p2 m-t"
                   data-cs-mask>{{'Phone' | translate}} {{data.shipping.telephone}}</p>
            </div>
            }

            <div class="w-6">
                @if (data.shipping_method.description) {
                <p class="s1 m-b-1">{{'Shipping Method' | translate}}</p>
                <p class="p2">{{data.shipping_method.description}}</p>
                }

                <p class="s1 m-t m-b-1">{{'Payment Method' | translate}}</p>
                <p class="c1">{{paymentMethod}}</p>
            </div>

            <div>
                <p class="s1 m-t m-t-5-m m-t-5-s p-b-2">{{'Your purchases' | translate}}</p>
                @for (item of data.items; track $index) {
                <div class="p2 flex flex-middle flex-justify-between p-t-2 p-t-3-m p-t-3-s p-b-2 b-t b-col-4">
                    <p class="w-7-m w-7-s">{{item.qty}} x {{item.name}}</p>
                    <div> {{'Price' | translate}}:
                        <price class="price"
                               [value]="item.price * item.qty" />
                    </div>
                </div>

                @if (item.giftcard && item.giftcard.recipient_email) {
                <div class="bg-col-33 b-radius-5 p-l-2 p-l-3-m p-l-3-s p-r-2 p-r-3-m p-r-3-s p-t-1 p-t-2-m p-t-2-s p-b-1 p-b-2-m p-b-2-s m-b-2"
                     [style.margin-top.px]="(client.isX || client.isL) ? -3 : 0">
                    @if (item.giftcard.recipient_name) {
                    <p class="p2"><span class="s2">{{ 'Recipient Name' | translate }}:</span> {{
                        item.giftcard.recipient_name }}</p>
                    }
                    @if (item.giftcard.recipient_email) {
                    <p class="p2"><span class="s2">{{ 'Recipient Email' | translate }}:</span> {{
                        item.giftcard.recipient_email }}</p>
                    }
                    @if (item.giftcard.date_delivery) {
                    <p class="p2"><span class="s2">{{ 'Date of Delivery' | translate }}:</span> {{
                        item.giftcard.date_delivery }}</p>
                    }
                    @if (item.giftcard.message) {
                    <p class="p2"><span class="s2">{{ 'Gift Card Message' | translate }}:</span> "{{
                        item.giftcard.message }}"</p>
                    }
                </div>
                }
                }
                <div class="p2 flex flex-middle flex-justify-between p-t-2 p-b-2 b-t b-col-4">
                    <p class="s2">{{'Subtotal' | translate}}</p>
                    <price class="price"
                           [value]="data.totals.subtotal" />
                </div>
                @if (data.totals?.delivery) {
                <div class="p2 flex flex-middle flex-justify-between p-b-2">
                    <p>{{data.shipping_method.description}}</p>
                    <price class="price"
                           [value]="data.totals.delivery" />
                </div>
                }
                @if (data.totals.discount) {
                <div class="p2 flex flex-middle flex-justify-between p-b-2">
                    <p>{{'Discount' | translate}}</p>
                    <price class="price"
                           [value]="data.totals.discount" />
                </div>
                }
                <div class="s1 flex flex-middle flex-justify-between p-t-2 p-b-2 b-t b-col-4">
                    <p>{{'Order Total' | translate}}</p>
                    <price class="price"
                           [value]="data.total" />
                </div>
            </div>

            @if (canRegister) {
            <div class="bg-col-34 m-t-3 p-t-3 p-t-4-m p-t-4-s p-b-3 p-b-4-m p-b-4-s p-l-5 p-l-7-m p-l-7-s p-r-5 p-r-7-m p-r-7-s b-radius-20 _box-width"
                 *ifSize="'SM'">
                @if (!showForm) {
                <p class="s1">{{'You\'re new here, get £5 off your next order' | translate}}</p>
                <p class="p2">{{'Create an account now to track your order, save your details and much more.' |
                    translate}}</p>
                <button class="button w-12 m-t-2"
                        type="button"
                        sl-button="account.create"
                        [action]="status"
                        (click)="openForm()">
                    <span>
                        {{'Create an account' | translate}}
                    </span>
                </button>
                }
                @else {
                <checkout-success-content-block />

                <form name="create_account_order-conf"
                      method="POST"
                      #form="ngForm">
                    <input-wrap class="block">
                        <input class="input"
                               type="password"
                               name="password"
                               data-cs-mask
                               [muiInput]="'Create a password'"
                               placeholder="Enter password"
                               [(ngModel)]="registrationData.password"
                               required
                               sl-input="password" />
                    </input-wrap>

                    @if (showSubscriptionOptIn) {
                    <checkout-success-newsletter [email]="data.customer.email"
                                                 [source]="'checkout_success'"
                                                 [status]="status" />
                    }

                    <div class="pos-relative m-t-3">
                        <button class="button w-12"
                                type="button"
                                sl-button="account.create"
                                [action]="status"
                                (click)="register()">
                            <span>
                                {{'Create an account' | translate}}
                            </span>
                        </button>
                        <result class="block m-t-2"
                                [status]="status" />
                    </div>
                </form>
                }
            </div>
            }
            @if (isRegistered) {
            <div class="bg-col-34 m-t-3 p-t p-b p-l-7 p-r-7 b-radius-20 _box-width"
                 *ifSize="'SM'">

                <p class="s1">{{'You\'re in!' | translate}}</p>
                <p class="p2">
                    {{'To say thanks for creating an account, we\'ve credited your account with £5 to spend on your next
                    order and more account benefits.' | translate}}</p>

                <a class="w-12 button m-t-3"
                   [routerLink]="'/account'"><span>{{'View my account' | translate}}</span></a>
            </div>
            }
            <div class="bg-col-5 m-t-3 p-t-3 p-t-4-m p-t-4-s p-b-3 p-b-4-m p-b-4-s p-l-5 p-l-7-m p-l-7-s p-r-5 p-r-7-m p-r-7-s b-radius-20 _box-width"
                 *ifSize="'SM'">
                <p class="h3 m-b-1">{{'Need any help with your order?' | translate}}</p>
                <p class="p2">
                    {{'Our Customer Service team is here to help! For any questions or support contact us' |
                    translate}}
                    <href [hrefClass]="'underline'"
                          [link]="'https://oliver-bonas.elevio.help'">here</href>.
                </p>

            </div>
            @if (!canRegister && !showForm) {
            <a class="w-12 button m-t _box-width"
               [routerLink]="'/account'"
               *ifSize="'SM'"><span>{{'View my account' | translate}}</span></a>
            }
        </div>


        <!-- cms outlet block -->
        <icms-outlet-block class="block m-t-6"
                           [name]="'Success page components'"
                           [variant]="'success-page'" />
    </div>
</div>
<ng-form #stepForm="ngForm"
         class="block">
    <h3>
        <span class="h2">{{'Find a collection point' | translate}}</span>

        <span class="fs-1 m-l-1">
            @if (saveStatus.busy && !initStatus.busy) {
            <span class="col-2">{{'saving...' | translate}}</span>
            }
            @if (initStatus.busy) {
            {{'loading...' | translate}}
            }
        </span>
    </h3>
    @if (!initStatus.busy) {
    <div class="m-t-3">
        @if (noResults) {
        <p class="p1">{{'Search a postcode, town or address' | translate}}</p>
        <ng-container *ngTemplateOutlet="searchButtons" />
        } @else {
        <div class="flex flex-middle flex-justify-between">
            <span class="p1">
                @if (currentResultsAreGeolocation) {
                {{'Showing pick-up locations' | translate}}&thinsp;
                <ng-container *ifSize="'S'"><br></ng-container>{{'around my location' | translate}}
                }
                @if (currentResultsAddress) {
                {{'Showing pick-up locations near' | translate}}&nbsp;<span
                      class="uppercase">{{currentResultsAddress}}</span>
                }
            </span>

            <button class="fs-3-s fs-3-m fw-bold-s fw-bold-m"
                    type="button"
                    [sizeClass]="'XL: s2'"
                    (click)="displayInput = !displayInput">
                <span *ifSize="'!S'">Change location</span>
                <span *ifSize="'S'">Change</span>
            </button>
        </div>
        <div class="hide-up-container">
            <div class="ng-hide-animate hide-up ng-hide"
                 [ngClass]="{'ng-hide': !displayInput}">
                <ng-container *ngTemplateOutlet="searchButtons" />
            </div>
        </div>
        }
    </div>
    }
    @if (displayedAll?.length) {
    <div class="m-t-6">
        <div class="flex flex-middle">
            <span class="m-r-2 p1">
                {{'Display as' | translate}}&nbsp;:
            </span>
            <radio-group name="checkout_collection_display_type"
                         [(ngModel)]="displayType">
                <radio [value]="'list'"
                       class="m-r">
                    <span class="p1">{{'List view' | translate}}</span>

                </radio>
                <radio [value]="'map'"><span class="p1">{{'Map view' | translate}}</span></radio>
            </radio-group>
        </div>
        @if (isListView) {
        @if (displayedStores?.length) {
        <checkout-collection-list class="m-t block"
                                  [list]="$any(displayedStores)"
                                  [model]="checkoutFormComponent.data.shipping.additionalData.store_id">
            {{'Store collection' | translate}}
        </checkout-collection-list>
        @if (storesLimit<stores.length) {
                        <div
                        class="center p-t-2 p-b-2 p-t-4-s p-b-4-s">
            <button type="button"
                    [sizeClass]="'SM: fs-3 fw-bold, XL: s2'"
                    (click)="showMore()">Show more</button>
    </div>
    }
    }
    @if (displayedCollectionPoints?.length) {
    <checkout-collection-list class="m-t block"
                              [list]="displayedCollectionPoints"
                              [model]="checkoutFormComponent.data.shipping.additionalData.booking_code">
        {{'Pickup points' | translate}}
    </checkout-collection-list>
    @if (collectionPointsLimit<collectionPoints.length) {
                              <div
                              class="center p-t-2 p-b-2 p-t-4-s p-b-4-s">
        <button type="button"
                [sizeClass]="'SM: fs-3 fw-bold, XL: s2'"
                (click)="showMoreCollectionPoints()">Show more</button>
        </div>
        }
        }
        }
        @if (isMapview) {
        <div class="m-t">
            <div class="ratio-4-3">
                <checkout-collection-google-map class="fill"
                                                [options]="mapOptions"
                                                [fitBounds]="true"
                                                [stores]="$any(displayedAll)" />
            </div>
            @if (currentMapModel) {
            <div class="m-t flex flex-column-s flex-justify-between-x flex-justify-between-l flex-justify-between-m">
                <div>
                    <div class="m-b-2 flex flex-middle flex-justify-between-s">
                        <span class="s1">{{currentMapModel.data.store_name}}</span>
                        @if (currentMapModel.distance) {
                        <span class="m-l-2 p2 fs-3-l">{{currentMapModel.distance | number: '1.0-1'}}mi</span>
                        }
                    </div>
                    @if (currentMapModel.data.address) {
                    <p class="p2 fs-3-l">{{currentMapModel.data.address}}</p>
                    }
                    @if ($any(currentMapModel).secondLine) {
                    <p class="p2 fs-3-l">{{$any(currentMapModel).secondLine}}</p>
                    }
                    @if (currentMapModel?.data?.phone) {
                    <p class="p2 fs-3-l">{{currentMapModel.data.phone}}</p>
                    }

                    @if ($any(currentMapModel).collectionDate) {
                    <div class="m-t-2 p2 fs-3-l">
                        {{'Get it by' | translate}}&nbsp;
                        <span>{{$any(currentMapModel).collectionDate | date: 'EEE'}}</span>&nbsp;
                        <span>{{$any(currentMapModel).collectionDate | date: 'd' | ordinal}}</span>&nbsp;
                        <span>{{$any(currentMapModel).collectionDate | date: 'MMM'}}</span>
                    </div>
                    }
                </div>
                <div class="m-t-4-s">
                    <action [class.is-disabled]="saveStatus?.busy"
                            class="button p-r-6-x p-l-6-x p-r-6-l p-l-6-l p-r-6-m p-l-6-m w-12-s"
                            (click)="selectModel($any(currentMapModel))">
                        <span>{{currentMapModel.type === 'store' ? 'Select store' : 'Select pickup point'}}</span>
                    </action>
                </div>
            </div>
            }
        </div>
        }
        </div>
        }

        <input-wrap class="visually-hidden">
            <input type="checkbox"
                   [(ngModel)]="valid"
                   name="collection"
                   required />
        </input-wrap>
        @if (displayContinue) {
        <action class="w-12 button m-t-8"
                [status]="saveStatus"
                (click)="checkoutFormComponent.goToNextStep()">
            {{'Continue to Payment' | translate}}
        </action>
        }
        <result class="block"
                [status]="saveStatus" />
</ng-form>


<ng-template #searchButtons>
    <input-wrap>
        <input class="input"
               type="text"
               name="location"
               data-cs-mask
               [muiInput]="'Enter a postcode, town or address'"
               (keyup.enter)="searchByLocation()"
               [(ngModel)]="searchAddress">
    </input-wrap>
    <action class="w-12 button m-t"
            [status]="loadStatus"
            (click)="searchByLocation()">
        {{'Find a collection point' | translate}}
    </action>
    <result [status]="initStatus"
            class="block p-t-1"></result>
    <result [status]="loadStatus"
            class="block p-t-1"></result>
    @if (geolocationAvailable) {
    <action class="w-12 m-t-2"
            [status]="geoStatus"
            (click)="searchByGeolocation()">
        <span class="flex flex-middle">
            <i class="svg-icon--geolocation m-r-1 fs-6 ng-hide-animate hide-fade"
               [ngClass]="{'ng-hide': geoStatus.busy}"></i>
            <span>{{'Use my current location' | translate}}</span>
        </span>
    </action>
    <result [status]="geoStatus"
            class="block p-t-1" />
    }
</ng-template>
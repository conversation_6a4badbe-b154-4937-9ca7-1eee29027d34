import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { IRequestCallbackFunction } from '@df/api/request/request.service';
import { SessionStorageService } from '@df/browser/storage/session-storage.service';
import { Catalog } from '@df/catalog/catalog.interfaces';
import { Store } from '@df/catalog/store/store';
import { StoreResource } from '@df/catalog/store/store.resource';
import { StoreService } from '@df/catalog/store/store.service';
import { CheckoutStorePickupComponentDef } from '@df/checkout/checkout-store-pickup.component.def';
import { Checkout } from '@df/checkout/checkout.interfaces';
import { App } from '@df/core/app';
import { Status } from '@df/core/status';
import { TimeoutService } from '@df/ng/timeout.service';
import { NOTICES } from '@df/ui/api/notice/notice.const';
import { IfSizeDirective } from '@df/ui/common/if-size.directive';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { RadioGroupDirective } from '@df/ui/form/radio/radio.component.def';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import sortBy from 'lodash-es/sortBy';
import take from 'lodash-es/take';
import uniq from 'lodash-es/uniq';
import { ActionComponent } from '../../ui/api/action.component';
import { ResultComponent } from '../../ui/api/result.component';
import { InputWrapBodyDirective } from '../../ui/form/input-wrap-body.directive';
import { InputWrapComponent } from '../../ui/form/input-wrap.component';
import { MuiInputDirective } from '../../ui/form/mui-input.directive';
import { RadioComponent } from '../../ui/form/radio';
import { CheckoutCollectionMethods } from '../checkout-form.component';
import { StoreDecorator } from './../../catalog/model/store/store.decorator';
import { CheckoutFormComponent } from './../checkout-form.component';
import { CheckoutStepComponent } from './../checkout-step.component';
import { CheckoutCollectionGoogleMapComponent } from './checkout-collection-google-map.component';
import { CheckoutCollectionListComponent } from './checkout-collection-list.component';
import { MetapackCollectionPoint } from './metapack/metapack-collection-point';
import { Metapack } from './metapack/metapack.namespace';
import { MetapackService } from './metapack/metapack.service';

export interface ICheckoutStorePickupSearchResponse {
    stores: StoreDecorator[];
    collectionPoints: MetapackCollectionPoint[];
}

interface GeolocationCoordinates {
    readonly accuracy: number;
    readonly altitude: number | null;
    readonly altitudeAccuracy: number | null;
    readonly heading: number | null;
    readonly latitude: number;
    readonly longitude: number;
    readonly speed: number | null;
}

export enum DisplayType {
    list = 'list',
    map = 'map'
}

@Component({
    selector: 'checkout-collection',
    templateUrl: './checkout-collection.html',
    standalone: true,
    imports: [
        ActionComponent,
        CheckoutCollectionGoogleMapComponent,
        CheckoutCollectionListComponent,
        CommonModule,
        FormsModule,
        IfSizeDirective,
        InputWrapBodyDirective,
        InputWrapComponent,
        MuiInputDirective,
        RadioComponent,
        RadioGroupDirective,
        ResultComponent,
        SizeClassDirective,
        TranslatePipe
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CheckoutCollectionComponent extends CheckoutStorePickupComponentDef implements OnInit, OnDestroy {
    @Output()
    selectedCollectionData = new EventEmitter<StoreDecorator | MetapackCollectionPoint>();

    /**
     * Current display type
     */
    displayType = DisplayType.list;

    /**
     * Collection points display limit
     * @type {number}
     */
    collectionPointsLimit = 4;
    /**
     * Stores display limit
     * @type {number}
     */
    storesLimit = 4;

    /**
     * Show limit step
     * @type {number}
     */
    private readonly limitStep = 4;

    private readonly storesInitLimit = 3;

    private readonly collectionPointsInitLimit = 4;

    /**
     * Current results address
     * @type {string}
     */
    currentResultsAddress?: string;

    /**
     * If current results are from geolocation
     * @type {boolean}
     */
    currentResultsAreGeolocation?: boolean;

    /**
     * If search input should be displayed
     * @type {boolean}
     */
    displayInput = false;

    /**
     * Collection points models
     * @type {MetapackCollectionPoint[]}
     */
    collectionPoints?: MetapackCollectionPoint[];

    /**
     * Collection points models displayed
     */
    displayedCollectionPoints: MetapackCollectionPoint[] = [];

    /**
     * Store models displayed
     */
    displayedStores: Store[] = [];

    /**
     * Al models - stores and metapack collection points
     * @type {(Store | MetapackCollectionPoint)[]}
     */
    displayedAll?: (Store | MetapackCollectionPoint)[];

    /**
     * Init status
     */
    initStatus = new Status();

    /**
     * Country code used for search
     * @type {string}
     */
    countryCode!: string;

    searchAddress?: string;

    mapOptions = {
        zoom: 14,
        fullscreenControl: false,
        mapTypeControl: false
    };

    private _currentMapModel?: Store | MetapackCollectionPoint;

    protected override _stepForm?: NgForm;
    @ViewChild('stepForm', { static: false })
    override set stepForm(form: NgForm) {
        this.checkoutStepComponent.removeForm(this._stepForm);
        this.checkoutStepComponent.addForm(form);
        this._stepForm = form;
    }

    private onStepChange: () => void;

    constructor(
        protected override checkoutFormComponent: CheckoutFormComponent,
        protected override checkoutStepComponent: CheckoutStepComponent,
        changeDetectorRef: ChangeDetectorRef,
        storeService: StoreService,
        app: App,
        storeResource: StoreResource,
        private metapack: MetapackService,
        private sessionStorageService: SessionStorageService,
        private timeoutService: TimeoutService
    ) {
        super(checkoutFormComponent, checkoutStepComponent, changeDetectorRef, storeService, app, storeResource);
    }

    override ngOnInit(): void {
        this.onStepChange = this.timeoutService.debounce(() => this._onStepChange(), 100);
        this.countryCode = this.app.multisite.current?.storePickupCountry || 'GB';
        this.subscriptions.add(
            this.loadStatus.subscribe(() => {
                this.initStatus.reset();
                this.detectChanges();
            })
        );
        this.subscriptions.add(this.saveStatus.subscribe(() => this.detectChanges()));
        this.subscriptions.add(
            this.geoStatus.subscribe(() => {
                this.initStatus.reset();
                this.detectChanges();
            })
        );
        this.subscriptions.add(this.initStatus.subscribe(() => this.detectChanges()));
        this.subscriptions.add(this.app.events.on(Checkout.EVENTS.StepChange).subscribe(() => this.onStepChange()));
        this.onStepChange();
    }

    override ngOnDestroy(): void {
        super.ngOnDestroy();
        this.checkoutStepComponent.removeForm(this._stepForm);
    }

    get leadTime(): string | undefined {
        return this.checkoutFormComponent?.shippingMethod?.lead_time;
    }

    /**
     * Checks if collection is current step
     * @returns {boolean}
     */
    private isCollectionStepActive(): boolean {
        return this.checkoutFormComponent.currentStep === this.checkoutStepComponent;
    }

    private _onStepChange(): void {
        if (this.isCollectionStepActive()) {
            // we don't load anything if we display already loaded stores
            if (!this.stores?.length && !this.collectionPoints?.length) {
                this.loadLastSearch(this.initStatus);
            }
        }
        this.detectChanges();
    }

    private loadLastSearch(status?: Status, noGeo = false): void {
        if (this.lastCollectionSearch) {
            const geo = !!(this.lastCollectionSearch as Catalog.IFindableLocateCoordsOptions).latitude;
            if (!geo || !noGeo) {
                this.search(status || (geo ? this.geoStatus : this.loadStatus), this.lastCollectionSearch);
            }
        }
    }

    /**
     * Last searched find data
     * @returns {Catalog.IFindableLocateOptions | Catalog.IFindableLocateCoordsOptions}
     */
    private get lastCollectionSearch(): Catalog.IFindableLocateOptions | Catalog.IFindableLocateCoordsOptions {
        return this.sessionStorageService.get('lastCollectionSearch');
    }

    /**
     * Last searched find data setter
     * @param {Catalog.IFindableLocateOptions | Catalog.IFindableLocateCoordsOptions} value
     */
    private set lastCollectionSearch(value: Catalog.IFindableLocateOptions | Catalog.IFindableLocateCoordsOptions) {
        this.sessionStorageService.set('lastCollectionSearch', value);
    }

    /////////////////////////////////////////////////
    /////
    ///// Shipping methods
    /////
    /////////////////////////////////////////////////

    /**
     * Checks if storepickup method is available
     * @returns {boolean}
     */
    private get storePickupAvailable(): boolean {
        return !!this.checkoutFormComponent.storePickupAvailable;
    }

    /**
     * Checks if metapack collection method is available
     * @returns {boolean}
     */
    private get collectionPointsAvailable(): boolean {
        return !!this.checkoutFormComponent.collectionPointsAvailable;
    }

    selectModel(model: StoreDecorator | MetapackCollectionPoint): void {
        if (model.type === Metapack.COLLECTION_POINT_TYPE) {
            this.selectCollectionPoint(model as MetapackCollectionPoint);
        } else {
            this.selectStore(model as StoreDecorator);
        }

        this.selectedCollectionData.next(model);
    }

    /**
     * Select store piuckup as shipping method
     * @param {Store} store
     */
    selectStore(store: StoreDecorator): void {
        this.select(CheckoutCollectionMethods.storePickup, { store_id: store.id as number });
    }

    /**
     * Select metapack collection point as shipping method
     * @param {MetapackCollectionPoint} collectionPoint
     */
    selectCollectionPoint(collectionPoint: MetapackCollectionPoint): void {
        this.checkoutFormComponent.isBillingToShipping = false;
        this.select(CheckoutCollectionMethods.metapackCollection, collectionPoint.getSelectionData());
    }

    /**
     * Store selection to checkout data
     * @param {CheckoutClickAndCollectMethods} methodCode
     * @param {ICheckoutQuoteDataShippingAdditionalData} additionalData
     */
    private select(methodCode: CheckoutCollectionMethods, additionalData: Checkout.IQuoteDataShippingAdditionalData): void {
        this.checkoutFormComponent.data.shipping.additionalData = additionalData;
        this.checkoutFormComponent.data.shipping.method = methodCode;
        this.checkoutFormComponent.saveShippingMethod();
        this.checkoutFormComponent.detectChanges();
        this.detectChanges();
    }

    get valid(): boolean {
        const method = this.selectedMethod();
        const additionalData = this.getAdditionalData();

        if (method && additionalData) {
            if (method === CheckoutCollectionMethods.storePickup) {
                return !!additionalData.store_id;
            } else if (method === CheckoutCollectionMethods.metapackCollection) {
                return !!additionalData.booking_code;
            }
        }

        return false;
    }

    set valid(value: boolean) {}

    private selectedMethod(): CheckoutCollectionMethods | undefined {
        const code = this.checkoutFormComponent.shippingMethodCode;
        if (code && (code === CheckoutCollectionMethods.metapackCollection || code === CheckoutCollectionMethods.storePickup)) {
            return code;
        }
        return undefined;
    }

    private getAdditionalData(): Checkout.IQuoteDataShippingAdditionalData | undefined {
        return this.checkoutFormComponent.getAdditionalShippingData();
    }

    /////////////////////////////////////////////////
    /////
    ///// Search
    /////
    /////////////////////////////////////////////////

    /**
     * Search by address/postcode
     */
    searchByLocation(): void {
        // we check if address/postcode is provided
        if (this.searchAddress && !this.loadStatus.busy && !this.geoStatus.busy) {
            // we reset geo status do hide geo status error message
            this.geoStatus.reset();

            const data: Catalog.IFindableLocateOptions = { address: this.searchAddress };
            if (this.maxDistance) {
                data.maxDistance = this.maxDistance;
            }

            this.search(this.loadStatus, data);
        }
    }

    /**
     * Search by browser geolocation
     */
    override searchByGeolocation(status = this.geoStatus, onGeolocationError?: IRequestCallbackFunction, silentError = false): void {
        // first we check if geolocation is available in the browser
        if (this.geolocationAvailable && !this.loadStatus.busy && !this.geoStatus.busy) {
            // we reset address/postcode status to hide error message
            this.loadStatus.reset();
            this.app.request.send(status, () => {
                return new Promise((resolve, reject) => {
                    this.getGeolocation()
                        .then((response: GeolocationCoordinates) => {
                            this.search(undefined, { latitude: response.latitude, longitude: response.longitude })
                                .then(resolve)
                                .catch(reject);
                        })
                        .catch(e => {
                            if (onGeolocationError) {
                                onGeolocationError();
                            }

                            if (silentError) {
                                resolve(undefined);
                            } else {
                                reject(e);
                            }
                        });
                });
            });
        }
    }

    /**
     * Search stores/collection points by address/postcode or geolocation coordinates
     * @param {Status} status
     * @param {IStoreLocateOptions | IStoreLocateCoordsOptions} data
     */
    private search(
        status: Status | undefined,
        data: Catalog.IFindableLocateOptions | Catalog.IFindableLocateCoordsOptions
    ): Promise<ICheckoutStorePickupSearchResponse> {
        return this.app.request.send(
            status,
            () =>
                new Promise((resolve, reject) => {
                    let stores: StoreDecorator[] | undefined;
                    let storesError: string | undefined;
                    let collectionPoints: MetapackCollectionPoint[] | undefined;
                    let collectionPointsError: string | undefined;

                    const finish = () => {
                        // we check if we have error or data for stores and collection points
                        if ((stores || storesError) && (collectionPoints || collectionPointsError)) {
                            // search fails if both searches fail or no stores/collection points are found
                            if (
                                (storesError && collectionPointsError) ||
                                ((!stores || !stores.length) && (!collectionPoints || !collectionPoints.length))
                            ) {
                                reject({ error: storesError || collectionPointsError || NOTICES['LOCATION'] });
                            } else {
                                resolve({
                                    data: {
                                        stores: stores || [],
                                        collectionPoints: collectionPoints || []
                                    }
                                });
                            }
                        }
                    };

                    const onStoresSuccess = (response: StoreDecorator[]) => {
                        stores = response;
                        finish();
                    };

                    const onStoresError = error => {
                        storesError = !error ? NOTICES['LOCATION'] : error.error || NOTICES['LOCATION'];
                        finish();
                    };

                    const onCollectionPointsSuccess = (response: MetapackCollectionPoint[]) => {
                        collectionPoints = response;
                        finish();
                    };

                    const onCollectionPointsError = error => {
                        collectionPointsError = !error ? NOTICES['LOCATION'] : error.error || NOTICES['LOCATION'];
                        finish();
                    };

                    this.lastCollectionSearch = { ...data };

                    // if address/postcode find data we use search by postcode
                    if ((data as Catalog.IFindableLocateOptions).address) {
                        const locateOptions = data as Catalog.IFindableLocateOptions;
                        locateOptions.country = this.countryCode;

                        // we assume that at least one method is available
                        // if store pickup not available we return empty array
                        if (this.storePickupAvailable) {
                            this.storeService.locate(undefined, locateOptions, onStoresSuccess, onStoresError);
                        } else {
                            onStoresSuccess([]);
                        }
                        // if collection points not available we return empty array
                        if (this.collectionPointsAvailable) {
                            this.metapack.findByAddress(
                                undefined,
                                {
                                    address: locateOptions.address,
                                    country: locateOptions.country
                                },
                                onCollectionPointsSuccess,
                                onCollectionPointsError
                            );
                        } else {
                            onCollectionPointsSuccess([]);
                        }
                    } else {
                        // if geolocation coordinates find data we use search by geolocation
                        const geoLocateOptions = data as Catalog.IFindableLocateCoordsOptions;
                        if (geoLocateOptions.latitude && geoLocateOptions.longitude) {
                            if (this.storePickupAvailable) {
                                this.storeService.locate(undefined, geoLocateOptions, onStoresSuccess, onStoresError);
                            } else {
                                onStoresSuccess([]);
                            }

                            if (this.collectionPointsAvailable) {
                                this.metapack.findByGeolocation(
                                    undefined,
                                    { latitude: geoLocateOptions.latitude, longitude: geoLocateOptions.longitude },
                                    onCollectionPointsSuccess,
                                    onCollectionPointsError
                                );
                            } else {
                                onCollectionPointsSuccess([]);
                            }
                        } else {
                            collectionPointsError = NOTICES['LOCATION'];
                            storesError = NOTICES['LOCATION'];
                            finish();
                        }
                    }
                }),
            (response?: ICheckoutStorePickupSearchResponse) => {
                if (response) {
                    this.onLoad(response.stores, response.collectionPoints, data);
                }
            },
            () => {
                this.onError();
            }
        );
    }

    /**
     * On load callback
     * @param {Store[]} stores
     * @param {MetapackCollectionPoint[]} collectionPoints
     */
    private onLoad(
        stores: StoreDecorator[],
        collectionPoints: MetapackCollectionPoint[],
        searchData?: Catalog.IFindableLocateOptions | Catalog.IFindableLocateCoordsOptions
    ): void {
        const storesModels = this.sortByDistance(
            uniq(stores).filter((store: StoreDecorator) => store.data && store.data.allow_for_checkout)
        );
        const collectionPointsModels = this.sortByDistance(collectionPoints);
        this.stores = storesModels;
        this.collectionPoints = collectionPointsModels;
        if (searchData) {
            const asGeo = searchData as Catalog.IFindableLocateCoordsOptions;
            if (asGeo.latitude && asGeo.longitude) {
                this.currentResultsAddress = undefined;
                this.currentResultsAreGeolocation = true;
            } else {
                this.currentResultsAreGeolocation = false;
                this.currentResultsAddress = (searchData as Catalog.IFindableLocateOptions).address;
            }
        } else {
            this.currentResultsAddress = undefined;
            this.currentResultsAreGeolocation = false;
        }
        this.resetLimits();
        if (this.currentMapModel && !this.displayedAll?.find(i => i.id === this.currentMapModel.id)) {
            this.currentMapModel = undefined;
        }
        this.detectChanges();
    }

    /**
     * On error callback
     * we reset stores and collections points
     */
    private onError(): void {
        this.onLoad([], []);
    }

    /////////////////////////////////////////////////
    /////
    ///// Others
    /////
    /////////////////////////////////////////////////

    /**
     * Returns if there is any store to display
     */
    get noResults(): boolean {
        return !(this.stores?.length || this.collectionPoints?.length);
    }

    /**
     * Checks if geolocation is available in the browser
     * @returns {boolean}
     */
    override get geolocationAvailable(): boolean {
        if (navigator && navigator.geolocation && navigator.geolocation.getCurrentPosition) {
            return true;
        }
        return false;
    }

    get displayContinue(): boolean {
        return !!(this.valid && !this.checkoutFormComponent.displaySummaryStep);
    }

    /**
     * Sorts stores/collection points by distance
     * @param {Store[] | MetapackCollectionPoint[]} storeLikeArray
     * @returns {Store[] | MetapackCollectionPoint[]}
     */
    private sortByDistance<T extends StoreDecorator | MetapackCollectionPoint>(storeLikeArray: T[]): T[] {
        return sortBy(storeLikeArray, storeLike => {
            return storeLike.distance;
        });
    }

    /**
     * Resets show limits to initial
     */
    private resetLimits(): void {
        this.storesLimit = this.storesInitLimit;
        this.collectionPointsLimit = this.collectionPointsInitLimit;
        this.applyStoresLimit();
        this.applyCollectionPointsLimit();
        this.applyAllLimit();
    }

    /**
     * Applies stores limit
     */
    private applyStoresLimit(): void {
        this.displayedStores = this.stores && this.stores.length ? take(this.stores as Store[], this.storesLimit) : [];
    }

    /**
     * Applies collection points limit
     */
    private applyCollectionPointsLimit(): void {
        this.displayedCollectionPoints =
            this.collectionPoints && this.collectionPoints.length ? take(this.collectionPoints, this.collectionPointsLimit) : [];
    }

    /**
     * Applies all models limit
     */
    private applyAllLimit(): void {
        this.displayedAll = [...this.displayedStores, ...this.displayedCollectionPoints];
    }

    /**
     * Increases display limit for collection points
     */
    showMoreCollectionPoints(): void {
        this.collectionPointsLimit += this.limitStep;
        this.applyCollectionPointsLimit();
        this.applyAllLimit();
    }

    /**
     * Increases display limit for stores
     */
    showMore() {
        this.storesLimit += this.limitStep;
        this.applyStoresLimit();
        this.applyAllLimit();
    }

    get isListView(): boolean {
        return this.displayType === DisplayType.list;
    }

    get isMapview(): boolean {
        return this.displayType === DisplayType.map;
    }

    get currentMapModel(): Store | MetapackCollectionPoint {
        return this._currentMapModel;
    }

    set currentMapModel(model: Store | MetapackCollectionPoint) {
        this._currentMapModel = model;

        this.detectChanges();
    }
}

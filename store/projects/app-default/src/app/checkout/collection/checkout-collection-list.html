<div class="flex flex-justify-between flex-middle m-b-2">
    <span class="s2">
        <ng-content />
    </span>
</div>
<div>
    <radio-group name="collection_model"
                 [(ngModel)]="model">
        @for (collectionModel of list; track collectionModel.id) {
        <div class="w-12"
             [ngClass]="{'m-b-2 m-b-3-s': !$last}">

            <div class="w-12 p-a-3 p-l-5-s b-a b-radius-7"
                 [ngClass]="collectionModel.id === model ? 'bg-col-34 b-col-21' : 'bg-col-w b-col-4'">

                <radio class="block w-12 p-l-6 p-l-9-s radio-full-width-label"
                       [value]="collectionModel.id"
                       (click)="select(collectionModel)">
                    <div class="flex flex-justify-between pos-relative">
                        <span class="p1">{{collectionModel.data.store_name}}</span>
                        @if (collectionModel.distance) {
                        <span class="p1">{{collectionModel.distance | number: '1.0-1'}}mi</span>
                        }
                        @if (collectionModel.id === model) {
                        <div class="p-t-3 col-13 pos-absolute top-100">
                            @if (collectionModel.data.address) {
                            <p class="p1 fs-3-l">{{collectionModel.data.address}}</p>
                            }
                            @if (collectionModel.secondLine) {
                            <p class="p1 fs-3-l">{{collectionModel.secondLine}}</p>
                            }
                            @if (collectionModel?.data?.phone) {
                            <p class="p1 fs-3-l">{{collectionModel.data.phone}}</p>
                            }
                            @if (leadTime && collectionModel?.data?.type === 'metapack_collection') {
                            <div class="m-t-2 p1"
                                 [innerHTML]="leadTime"></div>
                            } @else if (collectionModel.collectionDate) {
                            <div class="m-t-2 p1">
                                {{'Get it by' | translate}}&nbsp;
                                <span>{{collectionModel.collectionDate | date: 'EEE'}}</span>&nbsp;
                                <span>{{collectionModel.collectionDate | date: 'd' | ordinal}}</span>&nbsp;
                                <span>{{collectionModel.collectionDate | date: 'MMM'}}</span>
                            </div>
                            }
                        </div>
                        }
                    </div>
                </radio>
                @if (collectionModel.id === model) {
                <div class="m-t-3 col-t">
                    @if (collectionModel.data.address) {
                    <p class="p1 fs-3-l">{{collectionModel.data.address}}</p>
                    }
                    @if (collectionModel.secondLine) {
                    <p class="p1 fs-3-l">{{collectionModel.secondLine}}</p>
                    }
                    @if (collectionModel?.data?.phone) {
                    <p class="p1 fs-3-l">{{collectionModel.data.phone}}</p>
                    }
                    @if (leadTime && collectionModel?.data?.type === 'metapack_collection') {
                    <div class="m-t-2 p1"
                         [innerHTML]="leadTime">
                    </div>
                    } @else if (collectionModel.collectionDate) {
                    <div class="m-t-2 p1">{{'Get it by' | translate}}&nbsp;
                        <span>{{collectionModel.collectionDate | date: 'EEE'}}</span>&nbsp;
                        <span>{{collectionModel.collectionDate | date: 'd' | ordinal}}</span>&nbsp;
                        <span>{{collectionModel.collectionDate | date: 'MMM'}}</span>
                    </div>
                    }
                </div>
                }
            </div>
        </div>
        }
    </radio-group>
</div>

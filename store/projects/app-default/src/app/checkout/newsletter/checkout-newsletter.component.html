<div class="col-12 c1">
    <span class="text"
          [translateHtml]="'We\'d love to update you with newness, sales, offers and inspiration.'"></span>&nbsp;<button
            class="inline underline link-1"
            type="button"
            (click)="showSubscriptionOptOutHandle()">
        <span [translateHtml]="'Click here'"></span></button>&nbsp;<span class="text"
          [translateHtml]="'and tick if you\'d prefer not to hear from us, you can unsubscribe at any time.'"></span>&nbsp;<a
       class="underline link-1"
       [routerLink]="'/about-us/privacy-policy'"
       target="_blank">
        <span [translateHtml]="'Privacy policy'"></span></a>
    <div class="hide-up-container">
        <div class="p-b-2 ng-hide ng-hide-animate hide-up"
             [class.ng-hide]="!showSubscriptionOptOut">
            <checkbox class="w-12 m-t-2"
                      name="newsletter"
                      sl-input="newsletter"
                      [checkedValue]="false"
                      [notCheckedValue]="true"
                      [ngModel]="newsletter"
                      (ngModelChange)="ngModelChangeHandle($event)">
                <span class="col-13 c1">{{'I don\'t want to sign up' | translate}}</span>
            </checkbox>
        </div>
    </div>
</div>
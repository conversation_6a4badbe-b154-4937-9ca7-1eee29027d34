<ng-form #stepForm="ngForm">
    <div class="pos-relative flex flex-justify-right flex-middle z-2 p-t p-b p-t-5-s p-b-5-s p-r-6 p-l-6"
         style="box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);">
        <i class="block icon-close col-11 fs-4 cursor-pointer right"
           aria-hidden="true"
           (click)="hide()"></i>
    </div>
    <div class="w-12 pos-relative overflow-y-auto scrollbar-none shipping-methods"
         style="height: 100%;padding-bottom:4rem;">
        <div class="w-12 left p-a p-l-6 p-r-6"
             (offClick)="hide()"
             [offClickOff]="!isVisible">
            <div class="h2 p-b">{{'Delivery' | translate}}</div>
            <div class="w-12 m-r-0 m-b pos-relative">
                <input-wrap>
                    <select class="select b-a b-radius-basket b-col-4"
                            name="country"
                            [(ngModel)]="country"
                            required>
                        @for (country of countries | orderBy: ['name']; track country.code) {
                        <option [value]="country.code">{{country.name}}</option>
                        }
                    </select>
                </input-wrap>
            </div>
            <result class="block"
                    [status]="checkoutFormComponent.saveShippingAddressStatus" />
            <div class="pos-relative">
                <!-- no methods found notice -->
                @if (!allowedShippingMethods?.length) {
                <p class="fw-bold">{{'No shipping methods found for this delivery address.' | translate}}</p>
                } @else {
                <!-- methods list -->
                <radio-group name="method"
                             class="block w-12"
                             [(ngModel)]="shippingMethod">
                    @for (method of allowedShippingMethods; track method.code) {
                    <div class="c-12-set m-b-0">
                        <button class="block w-12 m-b-3"
                                [ngClass]="{ 'pe-none' : saveShippingAddressStatus.busy || saveShippingMethodStatus.busy}"
                                type="button"
                                (click)="shippingMethod = method.code">
                            <div class="w-12 flex block p-a-3 b-a b-radius-basket"
                                 [ngClass]="{'bg-col-33 b-col-4' : saveShippingAddressStatus.busy || saveShippingMethodStatus.busy, 'bg-col-34 b-col-21' : shippingMethod === method.code}">
                                <radio [value]="method.code"
                                       class="inline-block fs-6 basket-shipping-radio" />
                                <div class="w-12 p2 p-l-2">
                                    <div class="w-12 flex flex-justify-between">
                                        <span>{{method?.title}}</span>
                                        <price class="fw-regular"
                                               [value]="method.price" />
                                    </div>
                                    <div class="left p-t-2 p2 col-12">{{method.lead_time}}</div>
                                </div>
                            </div>
                        </button>
                    </div>
                    }
                </radio-group>
                }

                @if (showDeliveryError && allowedShippingMethods?.length) {
                <div class="pe-none pos-relative b-radius-7 b-a b-col-4 col-13 bg-col-33"
                     [style.padding]="'12px 16px 12px 16px'">
                    <p class="s1">{{'Click & Collect / Parcel shop is unavailable' | translate}}</p>
                    <p class="w-12 p2">
                        {{'Due to items arriving from different locations' | translate}}
                    </p>
                </div>
                }

                <!-- checkout only methods -->
                @if (checkoutOnlyShippingMethods?.length) {
                <div class="p2 col-12 p-t p-b-2">{{'Also available at the checkout' | translate}}</div>
                }
                @for (method of checkoutOnlyShippingMethods; track method.code) {
                <div class="block c-12 m-b-3">
                    <div class="w-12 flex block p-a-3 b-a b-col-4 b-radius-basket bg-col-33">
                        <div class="w-12 p2 p-l-2">
                            <div class="w-12 flex flex-justify-between">
                                <span>{{method?.title}}</span>
                                <price class="fw-regular"
                                       [value]="method.price" />
                            </div>
                            <div class="left p-t-2 p2 col-12">{{method.lead_time}}</div>
                        </div>
                    </div>
                </div>
                }
                <result class="block"
                        [status]="checkoutFormComponent.saveShippingAddressStatus" />
                <input-wrap>
                    <input type="hidden"
                           name="shippingMethod"
                           required
                           [(ngModel)]="shippingMethod">
                </input-wrap>
            </div>
            <!-- busy state presentation -->
            @if (saveShippingAddressStatus.busy || saveShippingMethodStatus.busy) {
            <div class="flex flex-column w-12 flex-middle">
                <i class="icon-loading m-b-2 block w-12 center"
                   aria-hidden="true"></i>
                @if (saveShippingAddressStatus.busy && !saveShippingMethodStatus.busy) {
                <span>updating...</span>
                }
                @if (saveShippingMethodStatus.busy) {
                <span>saving...</span>
                }
            </div>
            }
        </div>
    </div>
</ng-form>

<basket-fast-checkout-step name="fastcheckout">
    <div class="m-l-2 m-l-0-s bg-col-w p-a p-l-7 p-r-7 p-l-4-m p-r-4-m p-l-4-s p-r-4-s">
        <!-- totals -->
        <div class="w-12 p-t-2 p-b-3 p-t-0-s p-b-8-s">
            <ul>
                <!-- total !S -->
                @if (!client.isS) {
                <li class="flex p-b-2 flex-justify-between">
                    <span class="s1">{{'Order total' | translate}}</span>&nbsp;
                    <price class="s1 price"
                           [value]="basket.totals?.grand_total || 0" />
                </li>
                }
                <!-- subtotal -->
                <li class="p-b-2 p2 col-12 flex flex-justify-between">
                    {{'Subtotal' | translate}}
                    <price class="price"
                           [value]="basket.subtotal" />
                </li>
                <!-- shipping -->
                @if (shippingMethod?.title) {
                <li class="p-b-2 p2 col-12 flex flex-middle flex-justify-between">
                    <div class="inline-block">
                        {{(shippingMethod?.title || 'No shipping selected') | translate}}
                        <button class="m-l-2 p-l-2-s p-r-2-s b-col-4 b-radius-max b-a button-size-2"
                                [clickEvent]="'fastcheckoutshippingmethods.toggle.request'">
                            <span>{{(shippingMethod?.title ? 'Change' : 'Select') | translate}}</span>
                        </button>
                    </div>
                    <price class="price"
                           [value]="basket.shipping" />
                </li>
                }
                @if (basket.data?.totals?.credits) {
                <li class="p-b-2 p2 col-12">
                    <store-credit-remove class="block" />
                </li>
                }
                @for (giftcard of basket.data.giftcards | values; track giftcard.code) {
                <li class="p-b-2 p2 col-12">
                    <giftcards-added-giftcard class="block"
                                              [giftcard]="giftcard" />
                </li>
                }
                <!-- discount -->
                @if (basket.discount) {
                <li class="p-b-2 p2 col-12">
                    <coupon-remove class="block" />
                </li>
                }
                <!-- total S -->
                @if (client.isS) {
                <li class="flex p-b-2 b-t b-col-4 p-t-2 flex-justify-between">
                    <span class="s1">{{'Order total' | translate}}</span>&nbsp;
                    <price class="s1 price"
                           [value]="basket.totals?.grand_total || 0" />
                </li>
                }
            </ul>
            <!-- klarna notice -->
            @if (basket.isKlarnaSlice) {
            <div class="w-12 left">
                <span class="p2">{{'Pay over 12 months with Klarna Financing.' | translate}}</span>
                <button class="button-inline s2"
                        type="button"
                        [clickEvent]="'basketklarnatray.toggle.request'">
                    <span>{{'Learn More' | translate}}</span>
                </button>
            </div>
            }
        </div>
        <!--addons -->
        <div class="left m-b-5">
            <basket-discount-buttons class="w-12"
                                     [context]="'basket'" />
            <!-- giftbox button -->
            @if (client.isS && canAddGiftbox) {
            <div class="pos-fixed bottom-4 right-4 p-b-10 m-b-16">
                <div [clickEvent]="boxItemsCount ? 'giftboxformtray.toggle.request' : 'giftboxintroductiontray.toggle.request'"
                     class="b-radius-max bg-col-w p-l-2 p-r-2 p-a-1 cursor-pointer flex flex-middle"
                     style="box-shadow:0px 1px 6px rgba(0, 0, 0, 0.15)">
                    <div class="circle-wrapper bg-col-23">
                        <i class="icon-gift fs-4"
                           style="padding-bottom:0.1rem;"
                           aria-hidden="true"></i>
                    </div>
                    <div class="p-l-1 p2">{{'Create a gift box' | translate}}</div>
                </div>
            </div>
            }
        </div>
        @if (client.isS) {
        <div class="m-r--4 m-l--4 p-t m-b-8 b-b b-col-4"></div>
        <!-- upsale S-->
        <div class="p1 left p-t-3"
             id="upsale-carousel">{{'We think you\'ll love these too' | translate}}</div>
        }
        <div class="pos-fixed-s p-a-4-s bg-col-w-s left-0-s right-0-s bottom-0-s basket-fixed-button-block">
            <free-shipping-block [isBasketView]="true" />
            <div class="m-b-3  flex flex-justify-between">
                <span class="col-21 p1 left">
                    <basket-return-message />
                </span>
                @if (client.isS) {
                <price class="s1 price"
                       [value]="basket.totals?.grand_total || 0" />
                }
            </div>
            <div class="w-12 center">

                <!-- go to checkout -->
                <a class="w-12 button"
                   [class.is-disabled]="!canGoNextStep"
                   [routerLink]="'/checkout'">
                    {{'Checkout' | translate}}
                </a>
            </div>
            @if (displayApplePay) {
            <div class="m-t-2">
                <braintree-apple-pay-express-basket class="w-12" />
                <result class="m-t-1 block"
                        [status]="status"
                        [success]="'Data saved... redirecting...' | translate" />
            </div>
            }
        </div>
    </div>
</basket-fast-checkout-step>
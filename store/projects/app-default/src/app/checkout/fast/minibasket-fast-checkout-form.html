<minibasket-fast-checkout-step class="flex flex-column flex-grow"
                               name="fastcheckout">
    <free-shipping-block />
    <!-- out of stock products alert -->
    @if (basket.hasOOSProducts) {
    <basket-oos-items-warning />
    }
    @if (showMoveToWishlistMessage) {
    <div class="p2 center bg-col-23 p-t-1 p-b-1">{{'Item added to wishlist'| translate}}</div>
    }
    <div class="flex-grow pos-relative">
        <div class="fill overflow-y-auto"
             [sizeClass]="'SM:scrollbar-none'"
             #minibasketScrollContainer>
            <!-- please don't remove this div element, its required for scroll functionality -->
            <div class="w-12">
                <div class="w-12 m-t-3 m-t-2">
                    <div class="bg-col-w p-l-6 p-r-6 p-b-1">
                        <ul>
                            <!-- items -->
                            @for (item of basket.displayedBasketItems | orderBy : ['data.added_at'] : ['desc']; track
                            item.id) {
                            <li>
                                <minibasket-item [item]="item" />
                            </li>
                            }
                            <!-- giftbox items -->
                            @for (item of displayedGiftboxBasketItems | orderBy : ['data.added_at'] : ['desc'];
                            track item.id) {
                            @if (item?.product?.data) {
                            <li>
                                <giftbox-minibasket-item [item]="item" />
                            </li>
                            }
                            }
                        </ul>
                    </div>
                </div>
                <div class="w-12">
                    <div class="bg-col-w p-b-4">
                        <!-- totals -->
                        <div class="w-12 p-b-2 p-l-6 p-r-6">
                            <ul>
                                <!-- subtotal -->
                                <li class="p-b-2 p2 col-12 flex flex-justify-between">
                                    {{'Subtotal' | translate}}
                                    <price class="price"
                                           [value]="basket.subtotal" />
                                </li>
                                <!-- shipping -->
                                @if (backendData) {
                                <li class="p-b-2 p2 col-12 flex flex-justify-between">
                                    <div class="inline-block">
                                        {{(shippingMethod?.title || 'No shipping selected') | translate}}
                                        <button class="m-l-2 p-l-2-s p-r-2-s b-col-4 b-radius-max b-a button-size-2"
                                                [clickEvent]="'fastcheckoutshippingmethods.toggle.request'">
                                            <span>{{(shippingMethod?.title ? 'Change' : 'Select') | translate}}</span>
                                        </button>
                                    </div>
                                    @if (shippingMethod?.title) {
                                    <price class="price flex flex-middle"
                                           [value]="shippingPrice" />
                                    }
                                </li>
                                }
                                @if (basket.data?.totals?.credits) {
                                <li class="p-b-2 p2 col-12">
                                    <store-credit-remove class="block" />
                                </li>
                                }
                                @for (giftcard of basket.data.giftcards | values; track giftcard.code) {
                                <li class="p-b-2 p2 col-12">
                                    <giftcards-added-giftcard class="block"
                                                              [giftcard]="giftcard" />
                                </li>
                                }
                                <!-- discount -->
                                @if (basket.discount) {
                                <li class="p-b-2 p2 col-12">
                                    <coupon-remove class="block" />
                                </li>
                                }
                                <!-- total -->
                                <li class="flex p-b-2 b-t col-11 b-col-4 p-t-2 flex-justify-between">
                                    <span class="s1">{{'Order total' | translate}}</span>&nbsp;
                                    <price class="s1 price"
                                           [value]="basket?.totals?.grand_total || 0" />
                                </li>
                            </ul>
                            @if (basket.isKlarnaSlice) {
                            <div class="w-12 left">
                                <span class="p2">{{'Pay over 12 months with Klarna Financing.' | translate}}</span>
                                <button class="button-inline s2"
                                        type="button"
                                        [clickEvent]="'basketklarnatray.toggle.request'">
                                    <span>{{'Learn More' | translate}}</span>
                                </button>
                            </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- giftbox button -->
        @if (canAddGiftbox) {
        <div class="pos-absolute bottom-1 right-4 p-b-2">
            <div [clickEvent]="boxItemsCount ? 'giftboxformtray.toggle.request' : 'giftboxintroductiontray.toggle.request'"
                 class="b-radius-max bg-col-w p-l-2 p-r-2 p-a-1 cursor-pointer flex flex-middle"
                 style="box-shadow:0px 1px 6px rgba(0, 0, 0, 0.15)">
                <div class="circle-wrapper bg-col-23">
                    <i class="icon-gift"
                       [style.padding-top]="'0.1rem'"
                       [style.font-size]="'25px'"
                       aria-hidden="true"></i>
                </div>
                <div class="p-l-1 p2">{{'Create a gift box' | translate}}</div>
            </div>
        </div>
        }

        <!--temp hidden at customer request-->
        <!-- @if(showUpsellCarousel) {
    @if(!client.isS) {
    <icms-outlet-block class="wrap p-t-1"
                       [name]="'Upsell Carousel Block'"
                       [variant]="'upsell-carousel' + showUpsellCarousel"
                       [allowOnly]="['basket-upsell-carousel']" />
    }
    } -->

    </div>
    <div class="pos-relative p-t-10px p-r-6 p-r-4-s p-b-10px p-l-6 p-l-4-s b-t bg-col-33 z-10">
        <div class="m-b-8px flex flex-justify-between s1">
            <span class="left">{{'Order total' | translate}}</span>
            <price class="price"
                   [value]="basket?.totals?.grand_total || 0" />
        </div>

        <div class="w-12 ta-center">
            <!-- go to checkout -->
            <a class="w-12 button"
               [class.is-disabled]="!canGoNextStep"
               [routerLink]="'/checkout'">
                <span cy-miniBasketGoToCheckout>{{'Checkout' | translate}}</span>
            </a>
        </div>
        @if (displayApplePay) {
        <div class="m-t-2">
            <braintree-apple-pay-express-minibasket class="w-12" />
            <result class="m-t-1 block"
                    [status]="status"
                    [success]="'Data saved... redirecting...' | translate" />
        </div>
        }

        <basket-return-message class="w-12 m-t-8px col-21 left p1" />
    </div>
    @if (client.isM) {
    <icms-outlet-block [name]="'Upsell Carousel Block'"
                       [variant]="'upsell-carousel' + showUpsellCarousel"
                       [allowOnly]="'basket-upsell-carousel'" />
    }
</minibasket-fast-checkout-step>
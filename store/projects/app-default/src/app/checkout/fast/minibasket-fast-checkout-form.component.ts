import { Component, Input, OnInit, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { GtmService } from '@df/module-gtm/gtm.service';
import { injectTimeoutService } from '@df/ng/timeout.service';
import { BasketItem } from '@df/session/api/basket/basket-item';
import { ClickEventDirective } from '@df/ui/common/click-event.directive';
import { OrderByPipe } from '@df/ui/common/order-by.pipe';
import { SizeClassDirective } from '@df/ui/common/size-class.directive';
import { ValuesPipe } from '@df/ui/common/values.pipe';
import { PriceComponent } from '@df/ui/price/price.component';
import { TranslatePipe } from '@df/ui/translation/translate.pipe';
import { ICmsClientModule } from '@icms/client/icms-client.module';
import { CouponRemoveComponent } from '../../basket/coupon-remove.component';
import { FreeShippingBlockComponent } from '../../basket/free-shipping-block/free-shipping-block.component';
import { GiftboxBasketItem } from '../../basket/giftbox/giftbox-basket-item';
import { MinibasketItemComponent } from '../../basket/minibasket-item.component';
import { ICmsOutletBlockComponent } from '../../catalog/outlet-block/icms-outlet-block.component';
import { BasketOosItemsWarningComponent } from '../../catalog/ui/basket/basket-oos-items-warning.component';
import { BasketReturnMessageComponent } from '../../catalog/ui/basket/basket-return-message.component';
import { BasketDecorator, IBasketDataFreeShippingMinAmount } from '../../customer/api/basket/basket.decorator';
import { BraintreeApplePayExpressMinibasketComponent } from '../../module/braintree/apple-pay/braintree-apple-pay-express-minibasket.component';
import { GiftcardsAddedGiftcardComponent } from '../../module/giftcards/giftcards-added-giftcard.component';
import { StoreCreditRemoveComponent } from '../../module/store-credit/remove/store-credit-remove.component';
import { ResultComponent } from '../../ui/api/result.component';
import { GiftboxMinibasketItemComponent } from './../../basket/giftbox/giftbox-minibasket-item.component';
import { CheckoutServiceDecorator } from './../checkout-service.service.decorator';
import { LocalFastCheckoutFormComponentDef } from './local-fast-checkout-form.component.def';
import { MinibasketFastCheckoutStepComponent } from './minibasket-fast-checkout-step.component';

@Component({
    selector: 'minibasket-fast-checkout-form',
    templateUrl: './minibasket-fast-checkout-form.html',
    standalone: true,
    imports: [
        BasketOosItemsWarningComponent,
        BasketReturnMessageComponent,
        BraintreeApplePayExpressMinibasketComponent,
        ClickEventDirective,
        CouponRemoveComponent,
        FreeShippingBlockComponent,
        GiftboxMinibasketItemComponent,
        GiftcardsAddedGiftcardComponent,
        ICmsClientModule,
        ICmsOutletBlockComponent,
        MinibasketFastCheckoutStepComponent,
        MinibasketItemComponent,
        OrderByPipe,
        PriceComponent,
        ResultComponent,
        RouterModule,
        SizeClassDirective,
        StoreCreditRemoveComponent,
        TranslatePipe,
        ValuesPipe
    ]
})
export class MinibasketFastCheckoutFormComponent extends LocalFastCheckoutFormComponentDef implements OnInit {
    @Input()
    canGoNextStep = false;

    freeShippingAmounts?: IBasketDataFreeShippingMinAmount[];
    showMoveToWishlistMessage = false;
    displayedGiftboxBasketItems: GiftboxBasketItem[] = [];


    private gtmService = inject(GtmService);
    protected readonly disposableTimeoutService = injectTimeoutService();

    override ngOnInit() {
        super.ngOnInit();

        this.basket.preloadData().then(() => {
            this.detectChanges();
        });

        this.subscriptions.add(this.basket.subscribeDisplayedData(() => this.detectChanges()));
        this.subscriptions.add(this.localCheckout.availableShippingMethodsSubject.subscribe(() => this.updateFreeShippingAmounts()));
        this.basket.basketUpdateSubject.subscribe(async () => {
            this.updateFreeShippingAmounts();
            this.displayedGiftboxBasketItems = await this.giftboxService.getBoxItems();
        });

        this.updateFreeShippingAmounts();
        this.detectChanges();
    }

    showMoveToWishlistNotification(): void {
        this.showMoveToWishlistMessage = true;
        this.detectChanges();
        this.disposableTimeoutService.setTimeout(() => {
            this.showMoveToWishlistMessage = false;
            this.detectChanges();
        }, 3000);
    }

    protected updateFreeShippingAmounts(): void {
        const allowed = this.localCheckout.availableShippingMethods;
        this.freeShippingAmounts = this.basket.getFreeShippingMinAmounts(allowed);

        this.detectChanges();
    }

    get localCheckout(): CheckoutServiceDecorator {
        return this.checkout as CheckoutServiceDecorator;
    }

    /**
     * Customer basket shorthand
     */
    override get basket() {
        return <BasketDecorator>this.app.customer.basket;
    }

    get shippingPrice(): number {
        if (this.saveShippingMethodStatus.busy && this.shippingMethod) {
            return this.shippingMethod.price || 0;
        }
        return this.basket.shipping;
    }

    get showUpsellCarousel(): number | undefined {
        return this.basket.items.find(item => item.product?.data?.upsell_carousel)?.product?.data?.upsell_carousel;
    }

    trackByItemId = (index: number, item: BasketItem) => {
        return item.id;
    };
}

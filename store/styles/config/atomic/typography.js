const atomic = require('../../../git_modules/@tomandco/atomic/config/typography');

/**
 * Font families used to generate atomic classes.
 *
 * WARNING!!! `key` and `value` are required.
 * Other are used in iCMS
 *
 * @example
 * ```css
 *   .font-1 { font-family: "'Lato', sans-serif"; }
 * ```
 */
const fontFamily = [
    // default atomic
    ...atomic.fontFamily,
    // custom local
    { key: '1', value: "'futura-pt', sans-serif", title: 'Futura PT' }
];
exports.fontFamily = fontFamily;

const fontWeight = [
    // default atomic
    // ...atomic.fontWeight,
    // custom local
    { key: 'light', value: '300', title: 'Light' },
    { key: 'regular', value: '400', title: 'Regular' },
    { key: 'bold', value: '500', title: 'Bold' }
];
exports.fontWeight = fontWeight;

const textStyle = [
    { key: 'h1', title: 'Heading 1' },
    { key: 'h1-bold', title: 'Heading 1 - Medium' },
    { key: 'h1-italic', title: 'Heading 1 - Book Oblique' },
    { key: 'h1-bold-italic', title: 'Heading 1 - Medium Oblique' },
    { key: 'h2', title: 'Heading 2' },
    { key: 'h2-bold', title: 'Heading 2 - Medium' },
    { key: 'h2-italic', title: 'Heading 2 - Book Oblique' },
    { key: 'h2-bold-italic', title: 'Heading 2 - Medium Oblique' },
    { key: 'h3', title: 'Heading 3' },
    { key: 'h3-bold', title: 'Heading 3 - Medium' },
    { key: 'h3-italic', title: 'Heading 3 - Book Oblique' },
    { key: 'h3-bold-italic', title: 'Heading 3 - Medium Oblique' },
    { key: 'hero', title: 'Hero' },
    { key: 'hero-bold', title: 'Hero - Medium' },
    { key: 'hero-italic', title: 'Hero - Book Oblique' },
    { key: 'hero-bold-italic', title: 'Hero - Medium Oblique' },
    { key: 'hero2', title: 'Hero 2' },
    { key: 'hero2-bold', title: 'Hero 2 - Medium' },
    { key: 'hero2-italic', title: 'Hero 2 - Book Oblique' },
    { key: 'hero2-bold-italic', title: 'Hero 2 - Medium Oblique' },
    { key: 'hero3', title: 'Hero 3' },
    { key: 'hero3-bold', title: 'Hero 3 - Medium' },
    { key: 'hero3-italic', title: 'Hero 3 - Book Oblique' },
    { key: 'hero3-bold-italic', title: 'Hero 3 - Medium Oblique' },
    { key: 's1', title: 'Sub-heading 1' },
    { key: 's2', title: 'Sub-heading 2' },
    { key: 'p1', title: 'Paragraph 1' },
    { key: 'p2', title: 'Paragraph 2' },
    { key: 'p3', title: 'Paragraph 3' },
    { key: 'c1', title: 'Caption 1' },
    { key: 'o1', title: 'Overline 1' },
    { key: 'q1', title: 'Quote 1' }
];
exports.textStyle = textStyle;

const atomic = require('../../../git_modules/@tomandco/atomic/config/color');

/**
 * Not all colors are used to generate atomic classes
 *
 * Use named colors in SCSS files by `get` function
 * @example
 * ```css
 *   .class {
 *     color: get('namedColor.abbey');
 *   }
 * ```
 *
 * All colour variable names should come from http://chir.ag/projects/name-that-color/
 *
 */
const namedColor = {
    offBlack: '#333333', // 1 and 11
    redError: '#fa4731', // 2
    greenSuccess: '#10e895', // 3
    mercury: '#e5e5e5', // 4
    concrete: '#f2f2f2', // 5
    darkGrey: '#666666', // 12
    lightGrey: '#999999', // 13
    silverChalice: '#afafaf',
    green: '#72ac96', // 21
    pink: '#efb7c5', // 22
    cosmos: '#FED8D8', // 23
    outline: '#4d90fe',
    concrete90: 'rgba(255, 255, 255, 0.9)',
    gallery: '#eaeaea',
    parisWhite: '#d5e6df',
    aquaHaze: '#f1f7f5',
    alabaster: '#F7F7F7', // 33
    gin: '#ECF4F1', // 34
    chablis: '#FFEFEF', // 35
    moodyBlue: '#6C6AD9' // 36
};
exports.namedColor = namedColor;

/**
 * Thouse colors are used to generate atomic classes.
 *
 * WARNING!!! `key` and `value` are required.
 * Other are used in iCMS
 *
 * @example
 * ```css
 *   .col-1 { color: #454647; }
 * ```
 */
const color = [
    // default colors from atomic
    ...atomic.color,
    // system colours
    { key: '1', value: namedColor.offBlack, title: 'Default', group: 'Base' },
    { key: '2', value: namedColor.redError, title: 'Red Error', group: 'Base' },
    { key: '3', value: namedColor.greenSuccess, title: 'Green Success', group: 'Base' },
    // lines & borders
    { key: '4', value: namedColor.mercury, title: 'Mercury', group: 'Lines & Borders' },
    // backgrounds
    { key: '5', value: namedColor.concrete, title: 'Concrete', group: 'Backgrounds' },
    { key: '6', value: namedColor.concrete90, title: 'Semi transparent concrete', group: 'Backgrounds' },
    { key: '33', value: namedColor.alabaster, title: 'Alabaster', group: 'Backgrounds' },
    { key: '35', value: namedColor.chablis, title: 'Chablis', group: 'Backgrounds' },
    { key: '36', value: namedColor.moodyBlue, title: 'Moody Blue', group: 'Backgrounds' },

    // primary colours
    { key: '11', value: namedColor.offBlack, title: 'Off black', group: 'Primary Colours' },
    { key: '12', value: namedColor.darkGrey, title: 'Dark Grey', group: 'Primary Colours' },
    { key: '13', value: namedColor.lightGrey, title: 'Light Grey', group: 'Primary Colours' },
    { key: '34', value: namedColor.gin, title: 'Gin', group: 'Primary Colours' },
    // accent colours
    { key: '21', value: namedColor.green, title: 'Green', group: 'Accent Colours' },
    { key: '23', value: namedColor.cosmos, title: 'Cosmos', group: 'Accent Colours' },
    // other
    { key: 't', value: 'transparent', title: 'Transparent', group: 'Other' }
];
exports.color = color;

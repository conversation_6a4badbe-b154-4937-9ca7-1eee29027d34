@use 'sass:map';
@use 'sass:math';
@use '~@tomandco/atomic/scss/component';

/* Semi transparent bar with rounded corners */
.semi-transparent-pane {
    $_component-height: map.get(component.$height, size-1);

    height: $_component-height;
    border-radius: math.div($_component-height, 2);
    background-color: var(--atomic-background-color-6);
    color: var(--atomic-color-12);

    &.button-2 {
        height: calc(#{$_component-height} - 2px);
        border-color: transparent;
        color: var(--atomic-color-12);

        .size-s & {
            border-color: var(--atomic-border-color-4);
            background-color: var(--atomic-background-color-6);
        }
    }

    @media (hover: hover) {
        @include component.state-hover {
            background-color: #fff;

            &.button-2:not(.is-busy, .busy) {
                border-color: transparent;
            }
        }
    }

    .semi-transparent-pane {
        background: transparent;
        transition-duration: 0ms;
    }
}

.semi-transparent-pane__item {
    display: inline-block;
    min-width: 24px;
    height: 24px;
    border-radius: 12px;
    color: var(--atomic-color-1) !important;
    line-height: 24px;
    text-align: center;
    transition: background-color 0.3s ease;

    &.is-padding {
        padding-right: 4px !important;
        padding-left: 4px !important;
    }

    .size-s & {
        color: var(--atomic-color-13) !important;
    }

    &.option-in-wishlist {
        background-color: var(--atomic-background-color-23);
    }

    &:not(.option-in-wishlist):hover {
        background-color: #fff;
    }
}

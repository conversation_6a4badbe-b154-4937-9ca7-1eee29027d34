@use 'sass:map';
@use '~@tomandco/atomic/scss/atom/color';
@use '~@tomandco/atomic/scss/atom/typography';
@use '~@tomandco/atomic/scss/core';
@use '~@tomandco/atomic/scss/component';
@use '~@tomandco/atomic/scss/component/checkbox';
@use '~@tomandco/atomic/scss/component/mui-input/variables' with (
    $colors-map: (
        default: (
            background: white,
            background-hover: white,
            border: color.get(4),
            border-hover: color.get(4),
            color: white,
            color-busy: white
        )
    ),
    $label-color: color.get(13),
    $label-color-focus: color.get(12)
);
@use '~@tomandco/atomic/scss/component/mui-input/styles' as *;
@use 'styles/icons' as i;

$mui-input-padding-top: 1.375em;

.mui-input-padding-top {
    padding-top: $mui-input-padding-top;
}

.input-wrap {
    &._country {
        ._loading {
            display: none !important;
        }
    }
}

/** custom styles */
.mui-input {
    padding-top: $mui-input-padding-top;

    &::after {
        display: none;
    }

    input,
    textarea,
    select {
        overflow: auto;
        padding: 0 1em;
        border-width: 1px;
        border-radius: 8px;
        font-family: typography.fontFamily(1);
        font-size: core.px-to-rem(15);
        letter-spacing: 0.5px;
        line-height: core.px-to-rem(24);

        &:hover {
            border-color: color.get(12);
        }
    }

    .input,
    .select {
        height: map.get(component.$height, default);

        @each $name, $value in map.remove(component.$height, default) {
            &-#{$name} {
                line-height: $value;
            }
        }
    }

    textarea {
        height: 9.25em;
        padding-top: 1em;
        border-style: solid;
        line-height: 1.2em;
        resize: none;
    }

    input[type='number'] {
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            margin: 0;
            appearance: none;
        }

        &[type='number'] {
            appearance: textfield;
        }
    }

    select {
        background-image: url('/assets/images/arrow-down.svg');
        background-position: right 0.7em center;
        background-repeat: no-repeat;
        background-size: 1.5em;
    }

    input::placeholder {
        color: color.get(13);
    }

    label {
        top: 1.7em;
        left: 0.95rem;
        height: map.get(component.$height, default);
        padding: 0 0.25rem;
        font-family: futura-pt;
        font-size: core.px-to-rem(13);
        letter-spacing: core.px-to-rem(0.5);
        line-height: core.px-to-rem(21);
        transition-property: top, left, height, line-height, font-size, color, background-color;
    }

    @each $name, $value in map.remove(component.$height, default) {
        .input-#{$name},
        .select-#{$name} {
            label {
                height: $value;
            }
        }
    }

    ._loading {
        display: none !important;
    }

    &.is-focused,
    &.is-not-empty {
        label {
            top: 1.2em !important;
            background-color: #fff;
        }
    }

    &.form-highlight-label {
        &.is-focused,
        &.is-not-empty {
            label {
                background-color: color.get(5);
            }
        }
    }

    &.is-focused {
        input {
            border-color: color.get(21);
        }
    }

    &.is-not-empty.is-invalid:not(.is-pending) {
        input[required] {
            border-color: color.get(2);

            ~ label {
                color: color.get(2);
            }
        }
    }

    &.is-invalid {
        input[required] {
            input-wrap.async-validation:not(.has-validation) & {
                border-color: color.get(4) !important;
            }

            ~ label {
                input-wrap.async-validation:not(.has-validation) & {
                    color: color.get(12) !important;
                }
            }
        }

        &::before {
            input-wrap.async-validation:not(.has-validation) & {
                display: none !important;
            }
        }
    }

    &.is-required:not(.is-invalid):not(.is-valid) {
        input[required] {
            input-wrap.async-validation.has-validation & {
                border-color: color.get(2);
            }
        }
    }

    &.is-not-empty.is-valid:not(.is-focused) {
        label {
            color: color.get(13);
        }
    }

    &.is-valid:not(.is-select):not(.is-focused):not(.is-pending),
    &.is-invalid.is-not-empty:not(.is-select):not(.is-pending),
    .is-required.is-invalid & {
        &::before {
            @include i.icons;

            position: absolute;
            top: 2.6em;
            right: 1.2em;
        }
    }

    &.is-valid:not(.is-select):not(.is-focused):not(.is-pending) {
        &::before {
            @extend .icon-thick !optional;

            color: color.get(21);
            font-size: 0.9em;
        }
    }

    &.is-invalid.is-not-empty:not(.is-select):not(.is-pending),
    .is-required.is-invalid & {
        &::before {
            @extend .icon-close !optional;

            color: color.get(2);
            font-size: 0.9em;
        }
    }

    &.is-pending {
        ._loading {
            display: flex !important;
        }
    }
}

input-wrap.async-validation.has-validation .is-required .mui-input:not(.is-invalid):not(.is-valid) {
    &::before {
        @include i.icons;
        @extend .icon-close !optional;

        position: absolute;
        top: 2.6em;
        right: 1.2em;
        color: color.get(2);
        font-size: 0.9em;
    }
}

validation {
    display: block;
    padding-top: 0.6em;
    color: color.get(2) !important;
    font-size: 0.7em;
}

result,
checkout-result {
    &.error {
        color: color.get(2);
    }

    &.success {
        color: color.get(3);

        &.col-12 {
            color: color.get(12);
        }
    }
}

.is-disabled {
    cursor: default !important;
    opacity: 0.5;
    pointer-events: none;

    .fake-disabled & {
        opacity: 1;
    }
}

.input:disabled {
    .fake-disabled & {
        opacity: 1 !important;
    }
}

label {
    .fake-disabled & {
        opacity: 1 !important;
    }
}

//Radio
//--
.radio {
    @include checkbox.base;
    @include checkbox.icon-dot(color.get(12), black);

    [class^='icon-'],
    [class*=' icon-'] {
        top: calc(50% - 0.5em);
        width: 16px;
        height: 16px;
        border-color: color.get(13);
        border-radius: 18px;
        background: color.get(w);
    }

    &.is-checked,
    .is-checked {
        [class^='icon-'],
        [class*=' icon-'] {
            border-color: color.get(12);

            &::after {
                width: 8px;
                height: 8px;
                border-radius: 9px;
                margin: 0;
                transform: translate3d(-50%, -50%, 0);
            }
        }
    }

    &.radio-full-width-label {
        label {
            width: 100%;
        }
    }

    &.is-disabled,
    .is-disabled {
        opacity: 1;

        .radio__body {
            color: color.get(13);
        }
    }
}

/* Checkbox tick */

/* --- */
.checkbox,
.checkbox-tick {
    @include checkbox.base;
    @include checkbox.icon-ok(color.get(12), white);

    padding-left: core.px-to-rem(30, x);

    [class^='icon-'],
    [class*=' icon-'] {
        top: 50%;
        width: core.px-to-rem(18, x);
        height: core.px-to-rem(18, x);
        border-color: color.get(13);
        border-radius: 2px;
        background: color.get(w);
        transform: translate3d(0, -50%, 0);

        .login-form-newsletter & {
            top: 25%;
        }
    }

    &.is-checked,
    .is-checked {
        [class^='icon-'],
        [class*=' icon-'] {
            border-color: color.get(12);

            &::before {
                width: core.px-to-rem(6, x);
                height: core.px-to-rem(12, x);
                border-width: 0 2px 2px 0;
                border-color: color.get(1);
                margin-top: core.px-to-rem(-7, x);
                margin-left: core.px-to-rem(-3, x);
            }
        }
    }

    &.is-dot {
        [class^='icon-'],
        [class*=' icon-'] {
            border-radius: core.px-to-rem(18, x);
        }

        .icon-checkbox::before {
            display: none;
        }
    }

    &.is-dot .is-checked {
        [class^='icon-'],
        [class*=' icon-'] {
            &::before {
                width: 8px;
                height: 8px;
                border: 0;
                border-radius: 8px;
                margin: 0;
                margin-top: -4px;
                margin-left: -4px;
                background-color: color.get(1);
            }
        }
    }

    &.is-disabled,
    .is-disabled {
        opacity: 1;

        .checkbox__body {
            color: color.get(13);
        }
    }
}

.delivery-options,
.shipping-selection {
    .radio [class*=' icon-'],
    .radio [class^='icon-'] {
        top: 0;
        transform: translate3d(0, 2px, 0);
    }
}

.shipping-selection:hover {
    background: color.get(34);
}

.dropdown-options {
    top: -1px;
    border-bottom-left-radius: 7px;
    border-bottom-right-radius: 7px;

    .dropdown-option:hover {
        background: color.get(34);
    }
}

.login-panel {
    height: 100%;
}

.login-modal.modal-container {
    padding: 0;
    background-color: #fff;
}

.is-required-mark {
    &::after {
        display: inline-block;
        margin-left: 1px;
        color: color.get(2);
        content: '*';
    }
}

.toggle-password {
    height: map.get(component.$height, default);

    @each $name, $value in map.remove(component.$height, default) {
        .form-#{$name} & {
            height: $value;
        }
    }

    .is-invalid & {
        display: none;
    }
}

////
//* Links */
//* @group components.links */
////
@use 'sass:map';
@use '~@tomandco/atomic/scss/animation';
@use '~@tomandco/atomic/scss/atom/color';
@use '~@tomandco/atomic/scss/component';
@use '~@tomandco/atomic/scss/component/link/variables' with
    (
        $colors-map: (
            default: (
                color: color.get(11)
            ),
            1: (
                color: color.get(12)
            ),
            2: (
                color: color.get(13)
            )
        )
    );
@use '~@tomandco/atomic/scss/component/link';
@use './../variables' as local;

/* Links. */
/* --- */
.link {
    /* Links colors */
    /* --- */
    @each $name, $map in link.$colors-map {
        $color: inherit;
        $color-hover: $color;
        $color-focus: $color-hover;
        $color-active: $color-hover;
        $color-outline: transparent;

        @if (map.has-key($map, color)) {
            $color: map.get($map, color);
        } @else {
            $color: inherit;
        }

        @if (map.has-key($map, color-hover)) {
            $color-hover: map.get($map, color-hover);
        } @else {
            $color-hover: $color;
        }

        @if (map.has-key($map, color-focus)) {
            $color-focus: map.get($map, color-focus);
        } @else {
            $color-focus: $color-hover;
        }

        @if (map.has-key($map, color-active)) {
            $color-active: map.get($map, color-active);
        } @else {
            $color-active: $color-hover;
        }

        $append-class: '&';

        @if $name != default {
            $append-class: '&-#{$name}';
        }

        #{$append-class} {
            @include link.base;
            @include link.style($name);

            @include component.state-focus {
                @if map.has-key($map, link-shadow-focus) {
                    box-shadow: string.unquote(map.get($map, link-shadow-focus));
                } @else {
                    outline: 1px solid $color-outline;
                }
            }

            &.is-outlined {
                @include component.state-focus {
                    box-shadow: 0 0 0 1px rgba($color-outline, 1), 0 0 0 3px rgba($color-outline, 0.25);
                }
            }

            &.underline {
                text-decoration: underline;
            }

            &.underline-h {
                @include component.state-hover {
                    text-decoration: underline;
                }

                @include component.state-active {
                    text-decoration: underline;
                }
            }
        }
    }
}

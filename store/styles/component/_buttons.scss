/* stylelint-disable no-duplicate-selectors */
@use 'sass:map';
@use '~@tomandco/atomic/scss/animation';
@use '~@tomandco/atomic/scss/core';
@use '~@tomandco/atomic/scss/atom/color';
@use '~@tomandco/atomic/scss/atom/typography';
@use '~@tomandco/atomic/scss/component';
@use '~@tomandco/atomic/scss/helper';
@use 'styles/icons' as i;
@use '~@tomandco/atomic/scss/component/button/variables' with (
    $font-family: typography.font(1),
    $colors-map: (
        default: (
            background: color.get(1),
            background-disabled: color.get(5),
            border: color.get(1),
            border-disabled: color.get(5),
            color: white,
            color-busy: white,
            color-disabled: color.get(13)
        ),
        1: (
            background: transparent,
            background-disabled: color.get(5),
            border: color.get(1),
            border-disabled: color.get(5),
            color: color.get(1),
            color-busy: color.get(1),
            color-disabled: color.get(13)
        ),
        2: (
            background: white,
            background-disabled: color.get(5),
            border: white,
            border-disabled: color.get(5),
            color: color.get(1),
            color-busy: color.get(1),
            color-disabled: color.get(13)
        ),
        3: (
            background: transparent,
            background-disabled: color.get(5),
            border: white,
            border-disabled: color.get(5),
            color: white,
            color-busy: white,
            color-disabled: color.get(13)
        )
    ),
    $inline-colors-map: (
        default: (
            color: color.get(1)
        ),
        1: (
            color: white
        )
    )
);
@use '~@tomandco/atomic/scss/component/button';
@use '~@tomandco/atomic/scss/component/button/styles' as *;

button {
    @include component.state-disabled {
        cursor: default;
        pointer-events: none;
    }

    &[hidden] {
        visibility: hidden;
    }
}

%button-placeholder {
    @include button.button;
}

//* Button */
//* --- */
.button {
    $_component-height: map.get(component.$height, default);

    /* Buttons colors */

    /* --- */
    @each $name, $map in button.$colors-map {
        $append-class: '&';

        @if $name != default {
            $append-class: '&-#{$name}';
        }

        #{$append-class} {
            @extend %button-placeholder;

            @include button.style($name);
            @include button.busy-bounce($color: map.get($map, color-busy));

            /* stylelint-disable-next-line scss/selector-no-redundant-nesting-selector */
            & {
                padding-right: calc(#{$_component-height} / 2) !important;
                padding-left: calc(#{$_component-height} / 2) !important;
                transition-duration: animation.$transition-duration;
                transition-property: background-color, border-color, color, box-shadow;
                transition-timing-function: ease-in-out;
            }

            .button__body,
            &:not(action) > span {
                font-size: 13px;
                letter-spacing: 0.5px;
            }

            &:not(.success, .is-success) {
                .icon.c-success {
                    @include helper.visually-hidden;
                }
            }

            &:not(.error, .is-error) {
                .icon.button__error {
                    @include helper.visually-hidden;
                }
            }

            @if (map.has-key($map, background-disabled)) and (map.has-key($map, border-disabled)) and (map.has-key($map, color-disabled)) {
                @include component.state-disabled {
                    opacity: 1 !important;
                }
            }
        }
    }

    /* Button sizes */

    /* --- */
    @each $name, $value in component.$height {
        &-#{$name} {
            @include button.size($value);
        }
    }
}

// fallback for removed buttons
.button-4 {
    @extend .button-2;
}

%button-placeholder-inline {
    @include button.inline;
}

//* Button inline */
//* --- */
.button-inline {
    &-extra-small {
        .button__body,
        > span {
            display: inline-flex;
            font-size: 13px;
            font-weight: typography.fw(regular);

            .size-s & {
                font-size: 12px;
            }
        }
    }

    /* Buttons colors */

    /* --- */
    @each $name, $map in button.$inline-colors-map {
        $color: inherit;
        $color-hover: $color;
        $color-focus: $color-hover;
        $color-active: $color-hover;
        $color-busy: $color;
        $color-disabled: $color;

        @if (map.has-key($map, color)) {
            $color: map.get($map, color);
        } @else {
            $color: inherit;
        }

        @if (map.has-key($map, color-hover)) {
            $color-hover: map.get($map, color-hover);
        } @else {
            $color-hover: $color;
        }

        @if (map.has-key($map, color-focus)) {
            $color-focus: map.get($map, color-focus);
        } @else {
            $color-focus: $color-hover;
        }

        @if (map.has-key($map, color-active)) {
            $color-active: map.get($map, color-active);
        } @else {
            $color-active: $color-hover;
        }

        @if (map.has-key($map, color-busy)) {
            $color-busy: map.get($map, color-busy);
        } @else {
            $color-busy: $color;
        }

        @if (map.has-key($map, color-disabled)) {
            $color-disabled: map.get($map, color-disabled);
        } @else {
            $color-disabled: $color;
        }

        $append-class: '&';

        @if $name != default {
            $append-class: '&-#{$name}';
        }

        #{$append-class} {
            @extend %button-placeholder-inline;

            @include button.inline-style($color, $color-hover, $color-focus, $color-active, $color-busy, $color-disabled);
            @include button.inline-busy-underline($color: $color);

            /* stylelint-disable-next-line scss/selector-no-redundant-nesting-selector */
            & {
                position: relative;
                display: inline-block;
                text-align: left;
                text-decoration: none;
            }

            /* no styling for now */
            @include component.state-busy {
                &::before,
                &::after {
                    bottom: -4px;
                }

                &::after {
                    background-color: $color-busy !important;
                }

                &,
                &.underline {
                    &::before {
                        background-color: $color-busy;
                    }
                }
            }

            @include component.state-hover {
                &:not(.busy, .is-busy) {
                    &:not(.has-icon)::before {
                        background-color: $color-hover;
                        opacity: 1;
                    }

                    &.has-icon::after {
                        margin-left: core.px-to-rem(5);
                        opacity: 1;
                    }
                }
            }

            @include component.state-focus {
                &:not(.busy, .is-busy) {
                    &:not(.has-icon)::before {
                        background-color: $color-focus;
                        opacity: 1;
                    }

                    &.has-icon::after {
                        margin-left: core.px-to-rem(5);
                        opacity: 1;
                    }
                }
            }

            @include component.state-active {
                &:not(.busy, .is-busy) {
                    &:not(.has-icon)::before {
                        background-color: $color-active;
                        opacity: 1;
                    }

                    &.has-icon::after {
                        margin-left: core.px-to-rem(5);
                        opacity: 1;
                    }
                }
            }

            @include component.state-disabled {
                &::before {
                    background-color: $color-disabled;
                }
            }

            &:not(.has-icon)::before {
                position: absolute;
                z-index: 1;
                bottom: -4px;
                left: 0;
                width: 100%;
                height: 1px;
                background-color: $color;
                content: '';
                opacity: 0;
                pointer-events: none;
                transition: background-color animation.$transition-duration ease-in;
            }

            &.has-icon {
                display: inline-flex;
                align-items: center;

                &::after {
                    @extend .icon-arrow-forward, :before;

                    @include i.icons;

                    margin-left: core.px-to-rem(0);
                    opacity: 0;
                    transition:
                        opacity animation.$transition-duration ease-in,
                        margin-left animation.$transition-duration ease-in;

                    .size-s &,
                    .is-touchy & {
                        margin-left: core.px-to-rem(5);
                        opacity: 1;
                    }
                }

                &.is-visible::after {
                    opacity: 1;
                }
            }

            .button__body,
            > span {
                font-size: 15px;
                font-weight: typography.fw(bold);
                letter-spacing: 0.5px;

                .p2 & {
                    font-size: 13px;
                }

                .size-s .c1 & {
                    font-size: 11px;
                }

                .size-s & {
                    font-size: 14px;
                }
            }

            &:not(.no-fs) {
                .button__body,
                > span {
                    font-size: 15px;

                    .size-s & {
                        font-size: 14px;
                    }
                }
            }

            &.button--large {
                .button__body,
                > span {
                    font-size: core.px-to-rem(18) !important;
                }
            }

            &.underline {
                &::before {
                    opacity: 1;
                }
            }
        }
    }
}

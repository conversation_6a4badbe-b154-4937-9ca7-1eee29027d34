@use 'sass:map';
@use 'sass:math';
@use '@tomandco/atomic/scss/component';
@use 'styles/animation/keyframes' as *;

.product-listing {
    .carousel-dots {
        $carousel-dot-width: 20px;

        position: absolute;
        top: 1em;
        left: 0.1em;
        padding: 0;
        transform: scale(0.55);

        .size-s & {
            top: 0.5em;
            left: -0.55em;
            transform: scale(0.4);
        }

        .carousel-dots-inner {
            height: 10px;
        }

        .carousel-dot {
            position: relative;
            width: $carousel-dot-width;
            height: 10px;

            &::after {
                width: 50%;
                height: 10px;
                border-radius: 100%;
                transform: translateY(-50%);
            }

            @for $i from 1 through 10 {
                &:nth-of-type(#{$i}).is-active ~ .carousel-dots-underbar {
                    left: #{$carousel-dot-width * ($i - 1)};
                }
            }
        }

        .carousel-dots-underbar {
            width: $carousel-dot-width !important;
            height: 10px;

            &::after {
                top: 50%;
                width: 50%;
                height: 10px;
                border-radius: 100%;
                content: '';
                transform: translateY(0%);
            }
        }
    }

    .add-to-basket,
    .wishlist-heart {
        width: 2em;
        height: 2em;
        opacity: 0;

        .is-touchy & {
            opacity: 1;
        }
    }

    &:hover {
        .add-to-basket,
        .wishlist-heart {
            opacity: 1;
        }
    }
}

.size-guide {
    .size-s & {
        z-index: 1001;
    }
}

.panel-product-configurable-options {
    pointer-events: none !important;

    .modal-container {
        pointer-events: none;

        tray {
            pointer-events: auto;

            action.button {
                position: relative;
                justify-content: center;
                padding-right: 16px !important;
                padding-left: 16px !important;
                background-color: var(--atomic-color-1) !important;
                color: #fff !important;
                height: 42px;

                &.left {
                    justify-content: flex-start;
                }

                &::before {
                    position: absolute;
                    z-index: 1;
                    top: -1px;
                    bottom: 0;
                    left: -1px;
                    width: 0;
                    height: calc(100% + 2px) !important;
                    border-radius: 0;
                    margin: 0 !important;
                    animation: progressFinish 400ms forwards;
                    background-color: var(--atomic-background-color-1);
                    content: '';
                    opacity: 0;
                    will-change: width, opacity;
                }

                @include component.state-busy {
                    background-color: #fff !important;

                    &::before {
                        animation: progress 20000ms forwards;
                    }

                    &::after {
                        display: none;
                    }

                    ::ng-deep .button__body {
                        mix-blend-mode: difference;
                    }
                }

                span.button__body {
                    position: relative;
                    z-index: 3;
                    display: flex;
                    overflow: hidden;
                    width: 100%;
                    height: 100%;
                    align-items: center;
                    justify-content: center;
                    color: #fff;
                    text-align: left;
                }

                .icon-email-24 {
                    font-size: 20px;
                    width: 20px;
                    height: 20px;
                }

                &.is-disabled {
                    background-color: var(--atomic-background-color-12) !important;
                    border-color: var(--atomic-background-color-12) !important;
                    pointer-events: auto !important;
                    cursor: pointer !important;
                }
            }

            result.hidden {
                display: none;
            }

            ._options-tray {
                gap: 11px;

                &.is-swatch {
                    gap: 4px;
                    row-gap: 12px;
                }
            }
        }
    }

    ._option {
        @include component.state-busy {
            &::before,
            &::after {
                width: 1.8em;
                height: 1.8em;
                margin-top: -0.9em;
                margin-left: -0.9em;
                background-color: var(--atomic-background-color-35);
            }
        }

        &._in-wishlist {
            opacity: 0.5 !important;
        }

        &.is-picked {
            border-color: var(--atomic-border-color-1);
        }

        &.is-disabled {
            background-color: var(--atomic-background-color-5);
            color: var(--atomic-color-13);
            opacity: 1 !important;
            pointer-events: auto !important;

            &:not(.is-picked) {
                border-color: var(--atomic-border-color-5) !important;
            }
        }
    }
}

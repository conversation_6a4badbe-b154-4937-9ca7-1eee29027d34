@use 'sass:map';
@use '~@tomandco/atomic/scss/atom/color';
@use '~@tomandco/atomic/scss/atom/typography';
@use '~@tomandco/atomic/scss/component/input/variables' with (
    $font-family: typography.font(1),
    $font-size: 0.85em,
    $colors-map: (
        default: (
            background: white,
            border: color.get(13),
            color: color.get(1)
        )
    )
);

/* Base atomic styles */
@use '~@tomandco/atomic/scss/component/input/styles' as *;

.select {
    height: 3rem !important;
    padding: 0 1em !important;
    background: transparent;
    background-image: url('/assets/images/arrow-down.svg');
    background-position: right 0.5em center;
    background-repeat: no-repeat;
    background-size: 1.5em;
}

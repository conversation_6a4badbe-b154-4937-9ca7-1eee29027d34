@use '~@tomandco/atomic/scss/core';
@use '~@tomandco/atomic/scss/atom/typography';

.h1,
.text h1 {
    &,
    &-bold,
    &-italic,
    &-bold-italic {
        font-size: 29px;
        letter-spacing: 0.6px;
        line-height: 34px;

        .size-l & {
            font-size: 27px;
        }

        .size-m &,
        .size-s & {
            font-size: 26px;
            line-height: 32px;
        }
    }

    & {
        font-weight: typography.fw(light);
    }

    &-bold {
        font-weight: typography.fw(bold);
    }

    &-italic {
        font-style: italic;
        font-weight: typography.fw(regular);
    }

    &-bold-italic {
        font-style: italic;
        font-weight: typography.fw(bold);
    }
}

.h2,
.text h2 {
    &,
    &-bold,
    &-italic,
    &-bold-italic {
        font-size: 21px;
        letter-spacing: 0.5px;
        line-height: 26px;

        .size-m &,
        .size-s & {
            font-size: 20px;
            letter-spacing: 0.5px;
            line-height: 24px;
        }
    }

    &-bold {
        font-weight: typography.fw(bold);
    }

    &-italic {
        font-style: italic;
        font-weight: typography.fw(regular);
    }

    &-bold-italic {
        font-style: italic;
        font-weight: typography.fw(bold);
    }
}

.h3,
.text h3 {
    &,
    &-bold,
    &-italic,
    &-bold-italic {
        font-size: 19px;
        letter-spacing: 0.5px;
        line-height: 23px;

        .size-l & {
            font-size: 17px;
            line-height: 21px;
        }

        .size-m &,
        .size-s & {
            font-size: 16px;
            line-height: 21px;
        }
    }

    &-bold {
        font-weight: typography.fw(bold);
    }

    &-italic {
        font-style: italic;
        font-weight: typography.fw(regular);
    }

    &-bold-italic {
        font-style: italic;
        font-weight: typography.fw(bold);
    }
}

.hero {
    &,
    &-bold,
    &-italic,
    &-bold-italic {
        font-size: 70px;
        letter-spacing: 1.25px;
        line-height: 75px;

        .size-l & {
            font-size: 55px;
            letter-spacing: 1px;
            line-height: 60px;
        }

        .size-m & {
            font-size: 45px;
            letter-spacing: 1px;
            line-height: 50px;
        }

        .size-s & {
            font-size: 42px;
            letter-spacing: 1px;
            line-height: 47px;
        }
    }

    & {
        font-weight: typography.fw(light);
    }

    &-bold {
        font-weight: typography.fw(bold);
    }

    &-italic {
        font-style: italic;
        font-weight: typography.fw(regular);
    }

    &-bold-italic {
        font-style: italic;
        font-weight: typography.fw(bold);
    }
}

.hero2 {
    &,
    &-bold,
    &-italic,
    &-bold-italic {
        font-size: 50px;
        letter-spacing: 1.25px;
        line-height: 55px;

        .size-l & {
            font-size: 42px;
            letter-spacing: 1px;
            line-height: 48px;
        }

        .size-m &,
        .size-s & {
            font-size: 32px;
            letter-spacing: 1px;
            line-height: 37px;
        }
    }

    & {
        font-weight: typography.fw(light);
    }

    &-bold {
        font-weight: typography.fw(bold);
    }

    &-italic {
        font-style: italic;
        font-weight: typography.fw(regular);
    }

    &-bold-italic {
        font-style: italic;
        font-weight: typography.fw(bold);
    }
}

.hero3 {
    &,
    &-bold,
    &-italic,
    &-bold-italic {
        font-size: 120px;
        letter-spacing: 1.25px;
        line-height: 125px;

        .size-l & {
            font-size: 90px;
            letter-spacing: 1px;
            line-height: 95px;
        }

        .size-m &,
        .size-s & {
            font-size: 70px;
            letter-spacing: 1px;
            line-height: 75px;
        }
    }

    & {
        font-weight: typography.fw(light);
    }

    &-bold {
        font-weight: typography.fw(bold);
    }

    &-italic {
        font-style: italic;
        font-weight: typography.fw(regular);
    }

    &-bold-italic {
        font-style: italic;
        font-weight: typography.fw(bold);
    }
}

.s1 {
    font-size: 15px;
    font-weight: typography.fw(bold);
    letter-spacing: 0.5px;
    line-height: 27px;

    .size-m &,
    .size-s & {
        font-size: 14px;
        line-height: 26px;
    }
}

.s2 {
    font-size: 13px;
    font-weight: typography.fw(bold);
    letter-spacing: 0.5px;
    line-height: 25px;

    .size-m &,
    .size-s & {
        font-size: 12px;
        line-height: 24px;
    }
}

.p1 {
    font-size: 15px;
    letter-spacing: 0.5px;
    line-height: 24px;
    font-weight: typography.fw(regular);

    .size-m &,
    .size-s & {
        font-size: 14px;
        line-height: 22px;
    }
}

.p2 {
    font-size: 13px;
    letter-spacing: 0.5px;
    line-height: 21px;
    font-weight: typography.fw(regular);

    .size-m &,
    .size-s & {
        font-size: 12px;
        line-height: 20px;
    }
}

.p3 {
    font-size: 17px;
    letter-spacing: 0.7px;
    line-height: 29px;
    font-weight: typography.fw(regular);

    .size-m &,
    .size-s & {
        font-size: 16px;
        letter-spacing: 0.6px;
        line-height: 27px;
    }
}

.c1 {
    font-size: 11px;
    letter-spacing: 0.5px;
    line-height: 15px;
    font-weight: typography.fw(regular);
}

.o1 {
    font-size: 14px;
    letter-spacing: 1.4px;
    text-transform: uppercase;
    font-weight: typography.fw(regular);
}

.q1 {
    font-size: 27px;
    letter-spacing: 0.6px;
    line-height: 40px;
    font-weight: typography.fw(regular);

    .size-m &,
    .size-s & {
        font-size: 26px;
        line-height: 36px;
    }
}

.line-clamp {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}
